<template>
  <div class="app-container">
    <div class="app-container">
      <span>用户： </span>
      <!-- <el-input
          v-model="params.codeid"
          placeholder="请输入用户codeId"
          style="display: inline-block;width:200px"
        /> -->
      <el-select
        v-model="params.otherid"
        filterable
        remote
        reserve-keyword
        placeholder="请输入关键词"
        :remote-method="queryUserList"
        :loading="loading"
      >
        <el-option
          v-for="item in userQueryList"
          :key="item.id"
          :label="item.value"
          :value="item.id"
        />
      </el-select> <el-button type="primary" size="medium" style="margin-left: 20px;" @click="handleQuery">查询</el-button><p />
      <el-radio-group v-model="params.type" @change="handleQuery">
        <el-radio-button label="0">草稿</el-radio-button>
        <el-radio-button label="1">历史</el-radio-button>
      </el-radio-group>

    </div>
    <div class="table-container">
      <el-table
        ref="multipleTable"
        :data="list"
        tooltip-effect="dark"
        style="width: 100%"
        border
      >
        <!-- <el-table-column label="标签纸类型">
          <template slot-scope="scope">
            {{ scope.row.type===0 ? '草稿' : '历史箱' }}
          </template>
        </el-table-column> -->
        <!-- <el-table-column label="位置">
          <template slot-scope="scope">
            {{ scope.row.type===0 ? '草稿' : '历史箱' }}
          </template>
        </el-table-column> -->
        <el-table-column label="用户id">
          <template slot-scope="scope">
            {{ scope.row.codeId }}
          </template>
        </el-table-column>
        <el-table-column label="标签纸类型">
          <template slot-scope="scope">
            {{ formatPaperObj(scope.row.paperObj) }}
          </template>
        </el-table-column>
        <el-table-column label="打印尺寸">
          <template slot-scope="scope">
            {{ scope.row.paperObj.paperWidth + '*' + scope.row.paperObj.paperLength + 'mm' }}
          </template>
        </el-table-column>
        <el-table-column label="缩略图">
          <template slot-scope="scope">
            <img :src="scope.row.pic" style="width:120px; height:80px; object-fit:contain">
          </template>
        </el-table-column>
        <el-table-column label="打印时间">
          <template slot-scope="scope">
            {{ scope.row.createTime }}
          </template>
        </el-table-column>
        <el-table-column label="保存时间">
          <template slot-scope="scope">
            {{ scope.row.createTime }}
          </template>
        </el-table-column>
        <el-table-column v-if="params.type==='1'" label="打印设备">
          <template slot-scope="scope">
            {{ scope.row.printerType }}
          </template>
        </el-table-column>
        <el-table-column label="操作">
          <template slot-scope="scope">
            <el-button v-permission="'btn_usertemplate_edit'" size="mini" @click="handleEdit(scope.row)">纳入模板库</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="pagination-container" style="margin-top:20px;">
      <el-pagination
        :current-page="params.pageno"
        :page-sizes="[10, 20, 30, 40]"
        :page-size="params.pagesize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <el-dialog
      :title="'新增模板'"
      :visible.sync="showDialog"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      width="50%"
      center
    >
      <el-form ref="form" :model="templateDraft" :rules="rules" label-width="100px" class="edit-form">
        <el-form-item label="封面" prop="pic">
          <img :src="templateDraft.pic" class="avatar">
        </el-form-item>
        <!-- <el-form-item label="标题" prop="name">
          <el-input v-model="templateDraft.name" />
        </el-form-item> -->
        <el-form-item label="描述" prop="content">
          <el-input v-model="templateDraft.content" />
        </el-form-item>
        <el-form-item label="关键词" prop="recommend">
          <el-input v-model="templateDraft.recommend" />
        </el-form-item>
        <el-form-item label="语种" prop="localeCode">
          <!-- <el-radio-group v-model="templateDraft.localeCode">
            <el-radio-button v-for="item in localeData" :key="item.id" :label="item.id">{{ item.name }}</el-radio-button>
          </el-radio-group> -->
          <el-checkbox-group v-model="localeCodeStr">
            <el-checkbox-button v-for="item in localeData" :key="item.id" :label="item.id">{{ item.name }}</el-checkbox-button>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="类型" prop="type">
          <el-radio-group v-model="templateDraft.type">
            <el-radio-button v-for="item in typeData" :key="item.id" :label="item.id">{{ item.name }}</el-radio-button>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="适应纸张" prop="printerType">
          <!-- <el-checkbox-group v-model="printerTypeLabel">
            <el-checkbox-button v-for="item in printerTypeData" :key="item.id" :label="item.id">{{ item.name }}</el-checkbox-button>
          </el-checkbox-group> -->
          <el-checkbox-group v-model="paperSizeLabel">
            <el-checkbox-button v-for="item in paperSizeData" :key="item.id" :label="item.id">{{ item.name }}</el-checkbox-button>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="纸张类型" prop="paperType">
          <el-checkbox-group v-model="paperTypeLabel">
            <el-checkbox-button v-for="item in paperTypeData" :key="item.id" :label="item.id">{{ item.name }}</el-checkbox-button>
          </el-checkbox-group>
        </el-form-item>
        <!-- <el-form-item label="是否推荐" prop="isHot">
          <el-radio-group v-model="templateDraft.isHot">
            <el-radio-button label="0">否</el-radio-button>
            <el-radio-button label="1">是</el-radio-button>
          </el-radio-group>
        </el-form-item> -->
        <el-form-item label="排序" prop="sortNum">
          <el-input
            v-model="templateDraft.sortNum"
            type="text"
          />
        </el-form-item>
        <el-form-item>
          <el-button @click="handleClose">返回</el-button>
          <el-button v-permission="'btn_usertemplate_edit'" type="primary" @click="submit">保存</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>

  </div>
</template>
<script>
import { findUserInfoByKeyword } from '@/api/user/list'
import { fetchXeasylabelDraftListByPage } from '@/api/draft/userdraft'
import { saveTemplateDrafts } from '@/api/xeasylabel/templatedraft'
import { fetchDictTypeList as dictFetchList } from '@/api/xeasylabel/dict'
import { fetchListByPage as findPaperInfoList } from '@/api/xeasylabel/paperinfo'
export default ({
  name: 'UserDraft',
  data() {
    return {
      printerTypeLabel: ['-1'],
      paperTypeLabel: ['1', '2'],
      localeCodeStr: ['zh_CN'],
      typeData: [],
      localeData: [{
        'id': 'zh_CN',
        'name': '简体'
      }],

      printerTypeData: [{
        'id': '-1',
        'name': '15mm标签连续纸'
      }, {
        'id': '308',
        'name': '22×14mm'
      }, {
        'id': '420',
        'name': '30×14mm'
      }, {
        'id': '560',
        'name': '40×14mm'
      }, {
        'id': '700',
        'name': '50×14mm'
      }, {
        'id': '1500',
        'name': '50×30mm'
      }, {
        'id': '2000',
        'name': '50×40mm'
      }, {
        'id': '3750',
        'name': '50×75mm'
      }, {
        'id': '1680',
        'name': '56×30mm(bq1)'
      }, {
        'id': '1500 ',
        'name': '50×30mm(bq1)'
      }, {
        'id': '1200',
        'name': '40×30mm(bq1)'
      }, {
        'id': '600',
        'name': '20×30mm(bq1)'
      }, {
        'id': '2400',
        'name': '40×60mm(bq1)'
      }, {
        'id': '375',
        'name': '25×15mm(bq1)'
      }, {
        'id': '56',
        'name': '56mm连续纸(bq1)'
      }],
      paperSizeLabel: [],
      paperTypeData: [{
        'id': '1',
        'name': '连续纸'
      }, {
        'id': '2',
        'name': '间隙纸'
      }],
      paperColorData: {
        '11-15-1-1': 'LH15mm 白色',
        '11-25-1-1': 'LH15mm 白色'
      },
      // 查询参数
      params: {
        pageno: 1,
        pagesize: 10,
        codeid: '',
        haspic: '',
        ishot: '',
        createUserId: '',
        type: '0'
      },
      /** 校验规则 */
      rules: {
        // name: [
        //   { required: true, message: '请输入标题', trigger: 'blur' }
        // ],
        content: [
          { required: true, message: '请输入描述', trigger: 'blur' }
        ]
      },
      /** 列表 */
      list: [],
      /** 总条数 */
      total: 0,
      loading: false,
      userQueryList: [],
      showDialog: false,
      templateDraft: {},
      paperSizeData: []
    }
  },
  mounted() {
    const userId = this.$route.query.userId
    if (userId) {
      this.params.createUserId = userId
      this.getList()
    }
    this.initPaperSizeData()
    this.initPaperInfoList()
    this.initTemplateTypeData()
  },
  methods: {
    async initPaperInfoList() {
      const res = await findPaperInfoList()
      res.data.forEach(item => {
        let result = ''
        result += item.type === 1 ? 'L' : 'J'
        result += item.material === 1 ? 'H' : 'P'
        result += ' '

        const labelName = item.direction === 1 ? item.height + '*' + item.width + 'mm' : item.width + '*' + item.height + 'mm'

        result += item.type === 1 ? item.height + 'mm' : labelName
        result += ' ' + item.zhTitle

        const paperIds = item.paperId.split('-')
        const key = paperIds[0] + '-' + labelName + '-' + paperIds[3]

        this.paperColorData[key] = result
      })
    },
    async initPaperSizeData() {
      const res = await dictFetchList({
        type: 'paper_size'
      })
      this.paperSizeData = []
      res.data.forEach(item => {
        this.paperSizeData.push({
          'id': item.value,
          'name': item.label
        })
      })
      console.log(res)
    },
    async initTemplateTypeData() {
      const res = await dictFetchList({
        type: 'template_type'
      })
      this.typeData = []
      res.data.forEach(item => {
        this.typeData.push({
          'id': item.value,
          'name': item.label
        })
      })
      // console.log(res)
    },
    formatPaperObj(obj) {
      // const length = (obj.paperType === 11 || obj.paperType === 12) ? '1' : obj.paperLength
      const key = obj.paperType + '-' + obj.paperWidth + '-1-' + obj.paperColor
      console.log(key)
      return this.paperColorData[key]
    },
    queryUserList(e) {
      const params = {
        pageNo: 1,
        pageSize: 12,
        sort: 'desc',
        sortType: 'time',
        keyword: e
      }
      findUserInfoByKeyword(params).then(res => {
        if (res.head.ret === 0 && res.data.length > 0) {
          this.userQueryList = res.data
        } else {
          this.userQueryList = []
        }
        // } else {
        //   delete params['keyword']
        //   params['nickName'] = e
        //   fetchUserListByPage(params).then(res2 => {
        //     if (res2.head.ret === 0) {
        //       this.userQueryList = res2.data.result
        //     }
        //   })
        // }
      })
    },
    /** 查询 */
    handleQuery() {
      this.getList()
    },
    /** 获取列表 */
    async getList() {
      this.loading = true
      const res = await fetchXeasylabelDraftListByPage(this.params)
      this.loading = false
      if (res.head.ret === 0) {
        this.list = []
        this.list = res.data.result
        this.total = res.data.totalCount
      } else {
        this.$message.console.error(res.head.msg)
      }
    },
    /** 切换每页条数 */
    handleSizeChange(val) {
      this.params.pagesize = val
      this.getList()
    },
    /** 切换当前页 */
    handleCurrentChange(val) {
      this.params.pageno = val
      this.getList()
    },
    handleEdit(row) {
      const params = {}
      Object.assign(params, row)
      delete params.id
      this.templateDraft = {
        type: 'living',
        isHot: '0',
        pic: row.pic,
        draftsDto: params,
        localeCode: 'zh_CN'
      }
      this.showDialog = true
    },
    handleClose() {
      this.showDialog = false
    },
    submit() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          // this.templateDraft.printerType = this.printerTypeLabel.join(';')
          this.templateDraft.paperSize = this.paperSizeLabel.join(';')
          this.templateDraft.localeCode = this.localeCodeStr.join(';')
          this.templateDraft.paperType = this.paperTypeLabel.join(';')
          saveTemplateDrafts(this.templateDraft).then(res => {
            this.showDialog = false
            this.$message.success('保存成功')
          })
        } else {
          console.log('submit error')
          return -1
        }
      })
    }
  }
})
</script>
