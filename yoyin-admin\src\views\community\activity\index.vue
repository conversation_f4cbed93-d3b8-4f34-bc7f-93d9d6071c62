<!--
 * @Description: In User Settings Edit
 * @Author: your name
 * @Date: 2019-10-16 11:28:20
 * @LastEditTime: 2019-10-16 11:38:48
 * @LastEditors: Please set LastEditors
 -->
<template>
  <div class="app-container">
    <div class="table-query-container">
      <div class="query-date-hot">
        <span class="demonstration">日期范围： </span>
        <el-date-picker
          v-model="timeRange"
          :value-format="timeFormat"
          :format="timeFormat"
          :unlink-panels="true"
          type="datetimerange"
          :clearable="false"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          @change="changeTimeRange"
        />
        <span class="demonstration" style="margin-left: 20px;">类型：  </span>
        <el-button type="primary" size="medium" style="margin-left: 20px;" @click="handleQuery">查询</el-button>
      </div>

      <div class="query-code-btn">
        <span>用户codeId： </span>
        <!-- <el-input
          v-model="params.codeid"
          placeholder="请输入用户codeId"
          style="display: inline-block;width:200px"
        /> -->
        <el-select
          v-model="params.codeid"
          filterable
          remote
          reserve-keyword
          placeholder="请输入关键词"
          :remote-method="queryUserList"
          :loading="loading"
        >
          <el-option
            v-for="item in userQueryList"
            :key="item.id"
            :label="item.nickName"
            :value="item.codeId"
          />
        </el-select>

        <span style="margin-left: 20px;">动态Id： </span>
        <el-input
          v-model="params.feedid"
          placeholder="请输入动态Id"
          style="display: inline-block;width:300px"
        />
        <el-button size="medium" style="margin-left:20px;" type="primary" @click="handleAdd">新增动态</el-button>
      </div>

      <div class="query-code-btn">
        <span>素材标签： </span>
        <el-radio-group v-model="tabLabelId" @change="onChangeLabel">
          <el-radio-button v-for="labelItem in labelList" :key="labelItem.id" :label="labelItem.id">{{ labelItem.titleZh }}</el-radio-button>
        </el-radio-group>
        <el-button size="medium" style="margin-left:20px;" type="primary" @click="delCacheLabel">刷新缓存</el-button>
      </div>

      <div class="query-code-btn">
        <span>学科： </span>
        <el-radio-group v-model="tabSubjectId" @change="onChangeSubject">
          <el-radio-button label="">全部</el-radio-button>
          <el-radio-button v-for="item in subjectList" :key="item.id" :label="item.id">{{ item.name }}</el-radio-button>
        </el-radio-group>
      </div>

      <div class="query-code-btn">
        <span>标识： </span>
        <el-radio-group v-model="stickNum" @change="onChangeStickNum">
          <el-radio-button label="">全部</el-radio-button>
          <el-radio-button label="1">精选</el-radio-button>
          <el-radio-button label="999">置顶</el-radio-button>
        </el-radio-group>
      </div>

    </div>

    <div class="cards-container">
      <div v-for="(item, index) in list" :key="index" class="cards-container-item">
        <Card :id="item.feedId" :item="item" :label-list="labelList" :subject-list="subjectList" @notifyUpdate="notifyUpdate" />
      </div>
    </div>

    <div class="dialog-container">
      <el-dialog
        title="新增动态"
        :visible.sync="showEdit"
        width="50%"
        center
        :destroy-on-close="true"
      >
        <EditDialog :label-list="labelList" :subject-list="subjectList" @closeDialog="handleClose" />
      </el-dialog>
    </div>
    <!-- 动态详情 -->
    <div class="dialog-container">
      <el-dialog
        title="动态详情"
        :visible.sync="showDetail"
        width="450px"
      >
        <iframe
          title="webview"
          frameBorder="0"
          scrolling="auto"
          :src="detailUrl"
          style="height:600px; width:421px;"
        />
      </el-dialog>
    </div>

    <div class="pagination-container" style="margin-top:20px;">
      <el-pagination
        :current-page="params.pageno"
        :page-sizes="[12, 24, 36, 48]"
        :page-size="params.pagesize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>
<script>
import { fetchListByPage, findLabelPage, getSubjectList, delCacheLabelById } from '@/api/community/activity'
import EditDialog from './component/EditDialog/index'
import Card from './component/Card'
import { fetchUserListByPage } from '@/api/user/list'
export default {
  components: {
    EditDialog,
    Card
  },
  data() {
    return {
      stickNum: '',
      // 查询参数
      params: {
        startdate: '',
        enddate: '',
        pageno: 1,
        pagesize: 12,
        codeid: '',
        haspic: '',
        ishot: '',
        feedid: ''
      },
      /** 列表 */
      list: [],
      /** 总条数 */
      total: 0,
      /** 消息体 */
      banner: { },
      /** 勾选数据 */
      multipleSelection: [],
      /** 时间段 */
      timeRange: '',
      /** 时间格式 */
      timeFormat: 'yyyy-MM-dd',
      /** 显示编辑框 */
      showEdit: false,
      /** 标签列表 */
      labelList: [],
      /** 显示详情 */
      showDetail: false,
      /** 详情数据 */
      detailUrl: '',
      /** 素材标签 */
      scTabs: [],
      /** 活动标签 */
      htTabs: [],
      tabType: 'study',
      tabLabelId: '999999',
      tabSubjectId: '',
      subjectList: [],
      userQueryList: [],
      loading: false
    }
  },
  mounted() {
    const feedId = this.$route.query.feedId
    this.params.feedid = feedId
    const codeId = this.$route.query.codeId
    this.params.codeid = codeId
    this.getMySubjectList()
    this.getLableList()
    this.getList()
  },
  methods: {
    queryUserList(e) {
      const params = {
        pageNo: 1,
        pageSize: 12,
        sort: 'desc',
        sortType: 'time',
        codeId: e
      }
      fetchUserListByPage(params).then(res => {
        if (res.head.ret === 0 && res.data.result.length > 0) {
          this.userQueryList = res.data.result
        } else {
          delete params['codeId']
          params['nickName'] = e
          fetchUserListByPage(params).then(res2 => {
            if (res2.head.ret === 0) {
              this.userQueryList = res2.data.result
            }
          })
        }
      })
    },
    delCacheLabel() {
      if (this.tabLabelId) {
        const parms = {
          labelId: this.tabLabelId
        }
        delCacheLabelById(parms).then(res => {
          this.$message.success('操作成功')
        })
      }
    },
    async getMySubjectList() {
      const res = await getSubjectList()
      if (res.head.ret === 0) {
        this.subjectList = res.data
      }
    },
    /** 获取列表 */
    async getList() {
      const res = await fetchListByPage(this.params)
      if (res.head.ret === 0) {
        this.list = []
        this.list = res.data.result
        this.total = res.data.totalCount
      }
    },
    /** 切换每页条数 */
    handleSizeChange(val) {
      this.params.pagesize = val
      this.getList()
    },
    /** 切换当前页 */
    handleCurrentChange(val) {
      this.params.pageno = val
      this.getList()
    },
    onChangeSubject(val) {
      this.params.pageno = 1
      this.params.subject = val
      this.getList()
    },
    /** 选择时间段 */
    changeTimeRange(val) {
      this.params.startdate = val[0]
      this.params.enddate = val[1]
    },
    /** 查询 */
    handleQuery() {
      this.getList()
    },
    /** 新增 */
    handleAdd() {
      this.banner = {}
      // this.getLableList()
      this.showEdit = true
    },
    onChangeLabel(e) {
      this.params.pageno = 1
      this.params.labelId = e
      this.getList()
    },
    onChangeStickNum(e) {
      this.params.stickNum = e
      this.getList()
    },
    /** 关闭弹框 */
    closeDialog(code) {
      this.showEdit = false
      this.banner = {}
      // 保存code
      if (code === '1') {
        this.getList()
      }
    },
    /** 获取标签列表 */
    async getLableList() {
      const res = await findLabelPage({
        pageno: 1,
        pagesize: 10
      })
      if (res.head.ret === 0) {
        console.log('labelList', res.data)
        this.labelList = res.data
      }
    },
    /** 点击详情 */
    handleClickDetail(item) {
      this.detailUrl = item.htmlUrl
      this.showDetail = true
    },
    /** 保存后关闭刷新 */
    handleClose(val) {
      this.showEdit = false
      this.getList()
    },
    /** 删除后刷新 */
    notifyUpdate() {
      this.getList()
    }

  }
}
</script>

<style lang="scss" scoped>
.app-container {
  .table-query-container {
    .query-date-hot{
      margin-bottom: 10px;
    }
    .query-code-btn{
      margin-top: 10px;
      button{
        margin-right: 20px;
      }
    }
  }
  .cards-container{
    display: flex;
    flex-flow: row wrap;
    justify-content:flex-start;
    &-item{
      width: 30%;
      margin-right: 1%;
      margin-bottom: 20px;
    }
  }

}
</style>
