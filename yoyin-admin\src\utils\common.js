/*
 * @Author: your name
 * @Date: 2019-10-22 17:23:38
 * @LastEditTime: 2019-10-22 17:23:43
 * @LastEditors: your name
 * @Description: In User Settings Edit
 * @FilePath: \star-printer-admin\src\utils\common.js
 */
import aesjs from 'aes-js'

export const uuid = () => {
  const s = []
  const hexDigits = '0123456789abcdef'
  for (let i = 0; i < 36; i++) {
    s[i] = hexDigits.substr(Math.floor(Math.random() * 0x10), 1)
  }
  s[14] = '4' // bits 12-15 of the time_hi_and_version field to 0010
  const index = (s[19] && 0x3) || 0x8
  s[19] = hexDigits.substr(index, 1) // bits 6-7 of the clock_seq_hi_and_reserved to 01
  s[8] = s[13] = s[18] = s[23] = '-'
  return s.join('')
}

/**
 * 获取图片分辨率
 * @param image Blob类型 即从input标签.files数组中直接取出来的对象
 */
export const getImageSize = async image => {
  return new Promise((resolve, reject) => {
    if (Object.prototype.toString.call(image) === '[object String]') {
      const obj = new Image()
      obj.src = image
      obj.onload = () => {
        resolve({
          width: obj.width,
          height: obj.height,
          url: obj.src
        })
      }
      obj.onerror = e => {
        console.log(e)
        reject()
      }
    } else {
      // const reader = new FileReader();
      // reader.addEventListener('load', () => {
      //   const obj = new Image();
      //   obj.src = reader.result;
      //   resolve({
      //     width: obj.width,
      //     height: obj.height,
      //     url: reader.result,
      //   });
      // });
      // reader.readAsDataURL(image);
      const r = new FileReader()
      r.onload = () => {
        if (r.result) {
          const obj = new Image()
          obj.src = r.result
          obj.onload = () => {
            resolve({
              width: obj.width,
              height: obj.height,
              url: r.result
            })
          }
        } else {
          reject()
        }
      }
      r.readAsDataURL(image)
    }
  })
}

/**
 * 文本超过字符限制...显示
 * @param {string} text 文本
 * @param {number} limit 字数限制
 */
export const limitTextEllipsis = (text, limit) => {
  if (text.length >= limit) {
    text = text.substr(0, limit - 3) + '...'
  }
  return text
}

/**
 * 获取地址参数
 *
 * @param {string} url N 链接
 * @returns JSON
 */
export const getUrlParameter = (url = undefined) => {
  const query = {}
  const subUrl = url ? url.substr(url.indexOf('?')) : document.location.search.replace('?', '')
  let urlParams = []
  if (subUrl) {
    urlParams = subUrl.split('&')
    urlParams.forEach(item => {
      const value = item.split('=')
      query[value[0]] = value[1]
    })
  }
  return query
}

const key = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16]
const iv = [21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36]

/**
 * 文本加密
 * @param {string} text 需要加密的文本
 */
export const encrypt = text => {
  if (!text) {
    return text
  }
  const textBytes = aesjs.utils.utf8.toBytes(text)
  /* eslint-disable new-cap */
  const aesCfb = new aesjs.ModeOfOperation.cfb(key, iv)
  const encryptedBytes = aesCfb.encrypt(textBytes)
  const encryptedHex = aesjs.utils.hex.fromBytes(encryptedBytes)
  return encryptedHex
}

/**
 * aesHex 解密
 * @param {string} encryptedHex 需要解密的Hex
 */
export const decrypt = encryptedHex => {
  if (!encryptedHex) {
    return encryptedHex
  }
  const encryptedBytes = aesjs.utils.hex.toBytes(encryptedHex)
  const aesCfb = new aesjs.ModeOfOperation.cfb(key, iv)
  const decryptedBytes = aesCfb.decrypt(encryptedBytes)
  const decryptedText = aesjs.utils.utf8.fromBytes(decryptedBytes)
  return decryptedText
}

/**
 * 浏览器缓存
 */
export const storage = {
  get: keyName => decrypt(sessionStorage.getItem(keyName)),
  set: (keyName, value) => sessionStorage.setItem(keyName, encrypt(value)),
  remove: keyName => sessionStorage.setItem(keyName, ''),
  clear: () => sessionStorage.clear()
}
