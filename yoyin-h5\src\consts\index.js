import { browserInfo } from '@/utils/common'
export const CURR_ENV = 'test'
export const BASE_URL = CURR_ENV === 'test' ? 'https://test.yoyin.net' : 'https://api.yoyin.net' //  http://test.yoyin.net https://api.yoyin.net      http://************:20010
export const FILE_URL = 'https://m.yoyin.net/'   // https://m.yoyin.net/test
export const QUICK_LOGIN_ADDRESS = '/api/user/account/v1/login/getaccesstoken'

export const WHITE_ADDS = [QUICK_LOGIN_ADDRESS]

export const TOKEN_KEY = 'jwttoken'
export const CUSTOM_HEADER = 'header'
export const KEY_LOGIN_WAY = 'loginway'
export const KEY_LOGIN_ACCOUNT = 'account'

// 第三方授权信息的key
export const THIRD_AUTH_INFO = 'THIRD_AUTH_INFO'

// header头信息
export const USER_AGENT = 'sadais-agent'
export const VERSION = '1.0'
export const CHANNEL = 'STAR'
export const APPNAME = 'STARTPRINTER_H5'
export const SYSTEAM = 'H5'
export const NETWORK = ''
export const OPERATOR = ''

export const getUserAgent = flag => {
  // 应用英文名称（全大写）/当前版本/系统（全大写）/渠道信息/系统信息/网络信息/运营商(小程序取不到运营商，此项为空)，
  return `${APPNAME}/${VERSION}/${SYSTEAM}/${CHANNEL}/${SYSTEAM}/${NETWORK}/${flag}`
}

/**
 * app跳转类型
 */
export const TYPE_JUMP_APP = {
  NONE: 100, // 无跳转
  H5: 101, // H5跳转类型
  MESSAGE_LIST: 102, // 消息中心
  MSGNOTE: 103, // 纸条消息
  SHARE_NOTE: 104 // 分享消息
}

export const THROTTLE_TIME = () => {
  const browser = browserInfo()
  if (browser && browser.isAndroid) {
    return 200
  } else {
    return 10
  }
}
