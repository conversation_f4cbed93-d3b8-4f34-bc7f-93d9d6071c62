<template>
  <div class="faq">
    <div class="faq-title2">
      安卓手机如何导入文档？
    </div>
    <div class="faq-content2">
      <p class="first-line">1、通过电子邮件、微信、QQ等方式，将需要打印的文档发送并保存到手机上。</p>
      <p class="first-line"><img src="../../assets/img/p13-1.png"></p>
      <p class="first-line">2、打开APP，即可自动识别所保存的文档</p>
      <p class="first-line"><img src="../../assets/img/p13-2.png"></p>
      <p class="first-line">若上述方法未能成功导入，还可以手动导入。</p>
      <p class="first-line">选中文档后，点击右上角图标，选择【其他应用打开】，勾选柚印APP，即可导入文档。</p>
      <p class="first-line"><img src="../../assets/img/p13-3.png"></p>
      <p class="first-line"><img src="../../assets/img/p13-4.png"></p>
      <p class="remark">（* 各品牌手机可能会有所差异，但大致同理）</p>
    </div>

  </div>
</template>

<script>
export default {
  data () {
    return {
      moduleKey: 'faq'
    }
  },
  async mounted () { },
  methods: {},
  components: {}
}
</script>

<style lang="scss">
@import '../../assets/scss/faq.scss'; // 全局faq自定义的css样式
</style>
