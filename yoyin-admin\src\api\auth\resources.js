import request from '@/utils/request'

/**
 * 查询列表数据
 * @param params 参数
 */
export const fetchResourceTree = async params => {
  return request({
    url: '/platform/authority/auth/v1/resource/fetchResourcesTree',
    method: 'get',
    params
  })
}

/**
 * 保存记录
 * @param ids 记录id
 */
export const saveRecord = async params => {
  return request({
    url: '/platform/authority/auth/v1/resource/saveorupdate',
    method: 'post',
    data: params
  })
}

export const deleteRecord = async params => {
  return request({
    url: '/platform/authority/auth/v1/resource/del',
    method: 'post',
    data: params
  })
}

