function setChildrenStyle(el, cwidth) {
  if (el && el.children && el.children.length) {
    let list = el.children
    let len = list.length
    for (let i = 0; i < len; i++) {
      let item = list[i]
      item.style.paddingRight = '0px'
      item.style.width = cwidth + 'px'
    }
  }
}

export default {
  install: function (Vue) {
    Vue.directive('dialogDrag', {
      bind(el, binding, vnode, oldVnode) {
        const dialogHeaderEl = el.querySelector('.el-dialog__header')
        const dragDom = el.querySelector('.el-dialog')
        dialogHeaderEl.style.cursor = 'move'

        // 获取原有属性 ie dom元素.currentStyle 火狐谷歌 window.getComputedStyle(dom元素, null);
        const sty =
          dragDom.currentStyle || window.getComputedStyle(dragDom, null)

        dialogHeaderEl.onmousedown = (e) => {
          const disX = e.clientX - dialogHeaderEl.offsetLeft
          const disY = e.clientY - dialogHeaderEl.offsetTop
          let styL, styT
          if (sty.left.includes('%')) {
            styL =
              +document.body.clientWidth * (+sty.left.replace(/\%/g, '') / 100)
            styT =
              +document.body.clientHeight * (+sty.top.replace(/\%/g, '') / 100)
          } else {
            styL = +sty.left.replace(/\px/g, '')
            styT = +sty.top.replace(/\px/g, '')
          }
          document.onmousemove = function (e) {
            const l = e.clientX - disX
            const t = e.clientY - disY
            dragDom.style.left = `${l + styL}px`
            dragDom.style.top = `${t + styT}px`
          }
          document.onmouseup = function (e) {
            document.onmousemove = null
            document.onmouseup = null
          }
        }
      },
    })
    Vue.directive('autos', {
      inserted: function (el, binding, vnodes) {
        function children(nodeList) {
          for (let i = 0; i < nodeList.length; i++) {
            let item = nodeList[i]
            if (item.nodeName === 'IMG') {
              imgArr.push(item)
            }
            if (item.childNodes.length) {
              children(item.childNodes)
            }
          }
        }
        function imgLoad(img, callback) {
          if (img) {
            var timer = setInterval(function () {
              if (img.complete) {
                clearInterval(timer)
                callback(img)
              }
            }, 400)
            img.onerror = function (i) {
              clearInterval(timer)
              timer = null
              img.onerror = null
            }
          }
        }
        function calculation() {
          for (let i = 0; i < chirdnodes.length; i++) {
            let widths = chirdnodes[i].offsetWidth
            if (chirdnodes[i].nodeName === 'SPAN') {
              elWidth += widths
              chirdWidth.push(widths + 'px')
              maxWidth.push(widths)
            }
          }
          if (elWidth < 820 || elWidth > el.offsetWidth) {
            elWidth = el.offsetWidth
          }
          let maxNum = Math.max(...maxWidth)
          let chirdNodesLehgth = chirdWidth.length
          let averageWidth = elWidth / chirdNodesLehgth
          if (maxWidth.length === 4) {
            if (maxNum > averageWidth * 2) {
              column = 1
            } else if (
              maxNum > averageWidth &&
              maxNum < averageWidth * 2 - 30
            ) {
              column = 2
            } else if (maxNum < averageWidth) {
              column = 4
            }
          } else if (maxWidth.length === 3) {
            if (maxNum > averageWidth) {
              column = 1
            } else if (maxNum < averageWidth) {
              column = 3
            }
          } else if (maxWidth.length === 2) {
            if (maxNum > averageWidth) {
              column = 1
            } else if (maxNum < averageWidth) {
              column = 2
            }
          } else if (maxWidth.length > 4 && maxNum < averageWidth + 20) {
            column = 4
          } else if (maxWidth.length > 4 && maxNum > averageWidth + 20) {
            column = 1
          }
          if (column === 1) {
            el.style.display = 'grid'
            el.style.gridTemplateColumns = 'auto'
            setChildrenStyle(el, elWidth / column)
          } else if (column === 2) {
            el.style.display = 'grid'
            el.style.gridTemplateColumns = 'auto auto'
            setChildrenStyle(el, elWidth / column)
          } else if (column === 3) {
            el.style.display = 'flex'
            setChildrenStyle(el, elWidth / column)
          } else if (column === 4) {
            el.style.display = 'flex'
            el.style.flexWrap = 'wrap'
            setChildrenStyle(el, elWidth / column)
          } else {
          }
        }
        let elWidth = 0
        let chirdnodes = el.childNodes
        let column = 1
        let chirdWidth = []
        let maxWidth = []
        let imgArr = []
        children(el.childNodes)
        if (imgArr.length) {
          for (let i = 0; i < imgArr.length; i++) {
            imgLoad(imgArr[i], function () {
              if (i === imgArr.length - 1) {
                calculation()
              }
            })
          }
        } else {
          calculation()
        }
      },
    })
  },
}
