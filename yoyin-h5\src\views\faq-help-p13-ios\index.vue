<template>
  <div class="faq">
    <div class="faq-title2">
      IOS端文档导入步骤
    </div>
    <div class="faq-content2">
      <p class="first-line">1、将文档发送到手机微信/QQ/邮箱</p>
      <p class="first-line">2、前往微信/QQ/邮箱a，点击文档进入预览状态（示意图以微信为例）</p>
      <p class="first-line">3、微信点击"..."，选择“用其他应用打开”</p>
      <p class="first-line"><img src="../../assets/img/p13ios-01.png"></p>
      <p class="first-line"><img src="../../assets/img/p13ios-02.png"></p>

      <p class="first-line">4、选择【XPLife】</p>
      <p class="first-line">a. 第一次使用【XPLife】文档打印</p>
      <p class="first-line">(1) 在右边第二行向右滑动到最后，点击“更多”</p>
      <p class="first-line">(2) 找到【XPLife】，点击图标</p>
      <p class="first-line"><img src="../../assets/img/p13ios-03.png"></p>
      <p class="first-line"><img src="../../assets/img/p13ios-04.png"></p>
      
      <p class="first-line">b. 第二次使用【XPLife】，则直接选择【XPLife】</p>
      <p class="first-line"><img src="../../assets/img/p13ios-05.png"></p>

      <p class="first-line">5、进入【XPLife】文档打印设置页面，选择对应的文档，设置后点击“打印”即可。</p>
      <p class="first-line"><img src="../../assets/img/p13ios-06.png"></p>
    </div>

  </div>
</template>

<script>
export default {
  data () {
    return {
      moduleKey: 'faq'
    }
  },
  async mounted () { },
  methods: {},
  components: {}
}
</script>

<style lang="scss">
@import '../../assets/scss/faq.scss'; // 全局faq自定义的css样式
</style>
