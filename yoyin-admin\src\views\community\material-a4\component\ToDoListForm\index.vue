<template>
  <div class="todolist">
    <el-form :inline="true" class="demo-form-inline">
      <el-form-item label="预览图：">
        <SingleUpload key="listUrl" key-word="listUrl" :init-url="isEdit && files.listUrl ? files.listUrl.pic: ''" @updateFile="updateFile" />
      </el-form-item>
      <el-form-item label="资源图：">
        <SingleUpload key="resUrl" key-word="resUrl" :init-url="isEdit && files.resUrl ? files.resUrl.pic: '' " @updateFile="updateFile" />
      </el-form-item>
      <el-form-item v-if="mName === 'tz_todolist' " label="左侧添加图：">
        <SingleUpload key-word="addLeftUrl" :init-url="isEdit && files.addLeftUrl ? files.addLeftUrl.pic: '' " @updateFile="updateFile" />

      </el-form-item>
      <el-form-item label="顶部图：">
        <SingleUpload key-word="topUrl" :init-url="isEdit && files.topUrl ? files.topUrl.pic: '' " @updateFile="updateFile" />

      </el-form-item>
      <el-form-item label="底部图：">
        <SingleUpload key-word="downUrl" :init-url="isEdit && files.downUrl ? files.downUrl.pic: '' " @updateFile="updateFile" />

      </el-form-item>
      <el-form-item label="左侧图：">
        <SingleUpload key-word="leftUrl" :init-url="isEdit && files.leftUrl ? files.leftUrl.pic: '' " @updateFile="updateFile" />

      </el-form-item>
      <el-form-item label="右侧图：">
        <SingleUpload key-word="rightUrl" :init-url="isEdit && files.rightUrl ? files.rightUrl.pic: '' " @updateFile="updateFile" />
      </el-form-item>
      <br>
      <el-form-item v-if="mName === 'tz_frame'" label="纸张类型：">
        <el-radio-group v-model="paperLength">
          <el-radio-button v-for="(item, index) in paperList " :key="index" :label="item.label">{{ item.name }}</el-radio-button>
        </el-radio-group>
      </el-form-item>
      <br>
      <el-form-item v-if="mName === 'tz_frame'" label="编辑方向：">
        <el-radio-group v-model="placeTypeP">
          <el-radio-button v-for="(item, index) in placeTypeList " :key="index" :label="item.label">{{ item.name }}</el-radio-button>
        </el-radio-group>
      </el-form-item>
      <br>
      <el-form-item label="是否展示new" prop="shareFlag">
        <el-radio-group v-model="formData.isNew">
          <el-radio-button label="0">不展示</el-radio-button>
          <el-radio-button label="1">展示</el-radio-button>
        </el-radio-group>
      </el-form-item>
      <span style="padding-left:20px;padding-right:15px">期限:</span><el-date-picker
        v-model="formData.newFlagBeforeDate"
        type="date"
        value-format="yyyy-MM-dd"
        placeholder="选择展示期限"
      />
      <br>
      <el-form-item style="margin-left: 40%;">
        <el-button @click="handleBack">返回</el-button>
        <el-button v-permission="'btn-menuA4-material-edit'" type="primary" @click="handleSave">保存</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>
<script>
import { saveRecord } from '@/api/community/material'
import SingleUpload from '../SingleUpload/index'
import AliyunOSS from '@/utils/aliyunOSS'
const oss = new AliyunOSS()
const TIP_MESSAGES = {
  listUrl: '请选择预览图',
  resUrl: '请选择资源图'// ,
  // topUrl: '请选择顶部图',
  // downUrl: '请选择底部图',
  // leftUrl: '请选择左边图',
  // rightUrl: '请选择右边图',
  // addLeftUrl: '请选择左侧添加图'
}

export default {
  components: {
    SingleUpload
  },
  props: {
    mId: {
      type: String,
      default: ''
    },
    mName: {
      type: String,
      default: ''
    },

    formData: {
      type: Object,
      default: () => { return {} }
    }
  },
  data() {
    return {
      uploadFiles: { listUrl: {}, resUrl: {}, topUrl: {}, downUrl: {}, leftUrl: {}, rightUrl: {}},
      paperLabel: '0',
      placeType: '0',
      isNew: 0
    }
  },
  computed: {
    isEdit() {
      if (this.formData && this.formData.id) {
        return true
      } else {
        return false
      }
    },
    /** 编辑的数据 */
    files() {
      if (this.isEdit) {
        return this.formData.resMap
      } else {
        return {}
      }
    },
    /** 编辑ID */
    addId() {
      if (this.isEdit) {
        return this.formData.id
      } else {
        return ''
      }
    },
    /** 纸张类型 */
    paperLength: {
      // getter
      get: function() {
        if (this.isEdit) {
          return this.paperLabel === '0' ? this.formData.length : this.paperLabel
        } else {
          return 0
        }
      },
      // setter
      set: function(newValue) {
        this.paperLabel = newValue
      }

    },
    /** 纸张类型 */
    placeTypeP: {
      // getter
      get: function() {
        if (this.isEdit) {
          return this.placeType === '0' ? this.formData.placeType : this.placeType
        } else {
          return 0
        }
      },
      // setter
      set: function(newValue) {
        this.placeType = newValue
      }
    },
    isShowNew: {
      // getter
      get: function() {
        if (this.isEdit) {
          return this.formData.isNew
        } else {
          return 0
        }
      },
      // setter
      set: function(newValue) {
        this.isNew = newValue
      }
    },
    /** 纸张类型枚举 */
    paperList() {
      return [
        {
          label: -1,
          name: '连续纸'
        },
        {
          label: 30,
          name: '标签纸-30'
        },
        {
          label: 40,
          name: '标签纸-40'
        },
        {
          label: 50,
          name: '标签纸-50'
        },
        {
          label: 75,
          name: '标签纸-75'
        }

      ]
    },
    /** 编辑方向类型枚举 */
    placeTypeList() {
      return [
        {
          label: 0,
          name: '竖向'
        },
        {
          label: 1,
          name: '横向'
        }
      ]
    }

  },
  mounted() {

  },
  methods: {
    handleBack(val) {
      this.$emit('handleBack', val)
    },
    /** 子组件上传文件 */
    updateFile(key, file) {
      this.uploadFiles[key] = {
        file,
        key
      }
    },
    /** 校验文件是否上传 */
    validateFiles() {
      let flag = true
      // 合并
      if (this.isEdit) {
        Object.keys(this.files).forEach(key => {
          const item = this.uploadFiles[key]
          if (!item || !item.hasOwnProperty('file')) {
            this.uploadFiles[key] = this.files[key]
          }
        })
      }
      // 校验
      Object.keys(this.uploadFiles).forEach(key => {
        if (!flag) {
          return
        }
        const file = this.uploadFiles[key]
        if (TIP_MESSAGES[key] && file && !file.file && (!file.pic || file.pic.indexOf('http') < 0)) {
          this.$message.error(TIP_MESSAGES[key])
          flag = false
        }
      })
      return flag
    },
    /** 保存 */
    async handleSave() {
      // 检验是否选纸张
      if (this.mName === 'tz_frame' && this.paperLabel === '0') {
        this.$message.error('请选择纸张')
        return
      }
      if (this.validateFiles()) {
        const upFiles = []
        const picDto = {}
        Object.keys(this.uploadFiles).forEach(key => {
          const item = this.uploadFiles[key]
          if (item.file) {
            item.file.key = key
            upFiles.push(item.file)
          } else {
            picDto[key] = { pic: item.pic }
          }
        })
        // 上传
        oss.uploadFiles(upFiles, (results, error) => {
          if (error) {
            this.$message.error('文件上传失败，请检查')
            return
          }
          if (results) {
            results.forEach(res => {
              picDto[res.key] = { pic: res.name }
            })
          }
          // 保存
          const params = {
            id: this.addId,
            mId: this.mId,
            length: this.paperLabel,
            placeType: this.placeType,
            isNew: this.formData.isNew,
            newFlagBeforeDate: this.formData.newFlagBeforeDate,
            picDto
          }
          saveRecord(params).then(res => {
            if (res.head.ret === 0) {
              this.$message.success('保存成功')
              this.handleBack('1')
            } else {
              this.$message.error(res.head.msg)
            }
          })
        })
      }
    }
  }
}
</script>
