<template>
  <div class="card">
    <div class="card-info">
      <!-- <div class="card-info-id">id:{{ item.id }}</div> -->
      <div class="card-info-size">
        <span>宽:{{ item.width }}px</span>
        <span> 高:{{ item.height }}px</span>
      </div>
      <div class="card-info-time">创建时间：{{ item.createTime }}</div>
      <div v-if="parseInt(item.length) > 0" class="card-info-type">纸张类型: 标签纸-{{ formatPaper(item.length) }}</div>
      <div v-if="parseInt(item.length) === -1" class="card-info-type">纸张类型: 连续纸</div>
      <div>排序：{{ item.sortNum }}</div>
    </div>
    <div class="card-image">
      <el-image
        :src="item.pic"
        fit="scale-down"
        style="width:100%;height:300px;"
      >
        <div slot="placeholder" class="image-slot">
          加载中<span class="dot">...</span>
        </div>
      </el-image>
    </div>
    <div class="card-control">
      <el-tooltip content="编辑" placement="top">
        <el-button class="el-icon-edit" @click="notifyEdit" />
      </el-tooltip>
      <el-tooltip content="删除" placement="top">
        <el-button v-permission="'btn-xeasylabel-material-edit'" class="el-icon-delete" @click.stop="handelDelete" />
      </el-tooltip>
      <el-tooltip content="导出" placement="top">
        <el-button class="el-icon-view" @click.stop="handelExport" />
      </el-tooltip>
    </div>
    <el-dialog :append-to-body="true" title="素材数据" :visible.sync="showDailog" center :show-close="true" :destroy-on-close="true">
      <el-input v-model="materialResourceObjStr" type="textarea" rows="5">{{ materialResourceObjStr }}</el-input>
    </el-dialog>
  </div>
</template>
<script>
import { deleteRecord, exportMaterialResource } from '@/api/xeasylabel/material'
export default {
  name: 'Card',
  props: {
    item: {
      type: Object,
      required: true,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      paperList: [
        {
          label: 9999,
          name: '全部'
        }, {
          label: -1,
          name: '标签连续纸'
        },
        {
          label: 450,
          name: '30×15mm'
        },
        {
          label: 750,
          name: '50×15mm'
        },
        {
          label: 360,
          name: '30×12mm'
        },
        {
          label: 480,
          name: '40×12mm'
        },
        {
          label: 600,
          name: '50×12mm'
        },
        {
          label: 420,
          name: '30×14mm'
        },
        {
          label: 560,
          name: '40×14mm'
        },
        {
          label: 700,
          name: '50×14mm'
        },
        {
          label: 1500,
          name: '50×30mm'
        },
        {
          label: 2000,
          name: '50×40mm'
        },
        {
          label: 3750,
          name: '50×75mm'
        }

      ],
      showDailog: false,
      materialResourceObjStr: ''
    }
  },
  methods: {
    async handelExport(id) {
      const _this = this
      // id: this.item.id
      _this.showDailog = true
      exportMaterialResource({
        id: this.item.id
      }).then(res => {
        this.materialResourceObjStr = JSON.stringify(res.data)
      })
    },
    async handelDelete() {
      const _this = this
      this.$confirm('此操作将永久删除素材，不可恢复, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteRecord({
          id: this.item.id
        }).then(res => {
          if (res.head.ret === 0) {
            _this.$message.success('删除成功')
          } else {
            _this.$message.error('删除失败')
          }
          _this.notifyUpdate()
        })
      }).catch(() => {
      })
    },
    notifyUpdate() {
      this.$emit('notifyUpdate')
    },
    /** 通知父进入编辑 */
    notifyEdit() {
      this.$emit('notifyEdit', this.item)
    },
    formatPaper(paperValue) {
      let retValue = ''
      this.paperList.forEach(item => {
        if (item.label === paperValue) {
          retValue = item.name
        }
      })
      return retValue
    }
  }
}
</script>

<style lang="scss" scoped>
.card{
  font-size: 12px;
  color: #999;

  &-info{
      padding: 30px 5px 5px 5px;
    div{
      margin-bottom: 5px;
    }
  }
  &-control{
    width: 100%;
    display: flex;
    justify-content: flex-start;
    button{
      margin: 0px;
      display: inline-block;
      width: 33%;
    }
  }
}
</style>
