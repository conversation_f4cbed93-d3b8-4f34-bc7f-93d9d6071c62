<template>
  <div class="app-container">
    <div class="table-query-container">
      <div class="query-code-btn">
        <span class="demonstration">时间范围</span>
        <el-date-picker
          v-model="activityDate"
          type="daterange"
          align="right"
          unlink-panels
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd"
          :picker-options="pickerOptions"
          @change="handleChangeAtivityDate"
        />

        <el-button size="medium" style="margin-left:20px;" @click="getList">查询</el-button>
        <el-button size="medium" style="margin-left:20px;" @click="reset">重置</el-button>
        <el-button v-permission="'btn-menuCoin-statisticscoin-edit'" size="medium" style="margin-left:20px;" @click="exportx">导出</el-button>
      </div>
    </div>
    <div class="table-container">
      <el-table
        ref="multipleTable"
        :data="list"
        tooltip-effect="dark"
        style="width: 100%"
        border
      >
        <el-table-column type="selection" width="55" />
        <el-table-column label="任务名称">
          <template slot-scope="scope">{{ scope.row.missionName }}</template>
        </el-table-column>
        <el-table-column label="任务完成次数">
          <template slot-scope="scope">{{ scope.row.finishTimes }}</template>
        </el-table-column>
        <el-table-column label="任务完成人数">
          <template slot-scope="scope">{{ scope.row.finishUserCount }}</template>
        </el-table-column>
        <el-table-column label="点取奖励次数">
          <template slot-scope="scope">{{ scope.row.fetchTimes }}</template>
        </el-table-column>
        <el-table-column label="平均每人获得奖励">
          <template slot-scope="scope">{{ scope.row.fetchAverageCoins }}</template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>
<script>
import { getStatisticsMissionFinish, exportStatisticsMissionFinish } from '@/api/coin/analysis'
import { fetchListByPage } from '@/api/user/list'
import { parseTime } from '@/utils/index'
export default {
  name: 'CurrCoinRank',
  data() {
    return {
      // 查询参数
      /** 列表 */
      list: [],
      /** 总条数 */
      rankTop: 10,
      showDialog: false,
      searchUserId: '',
      userQueryList: [],
      loading: false,
      activityDate: [],
      pickerOptions: {
        shortcuts: [{
          text: '最近一周',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近一个月',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近三个月',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
            picker.$emit('pick', [start, end])
          }
        }]
      }
    }
  },
  mounted() {
    const end = new Date()
    const start = new Date()
    start.setTime(end.getTime() - 3600 * 1000 * 24 * 7)
    this.activityDate = [parseTime(start, '{y}-{m}-{d}'), parseTime(end, '{y}-{m}-{d}')]
    this.getList()
  },
  methods: {
    reset() {
      this.searchUserId = ''
    },
    /** 获取列表 */
    async getList() {
      const parms = {
        beginDate: this.activityDate[0],
        endDate: this.activityDate[1]
      }
      const res = await getStatisticsMissionFinish(parms)
      if (res.head.ret === 0) {
        this.list = res.data
        // this.total = res.data.totalCount
      }
    },

    handleSelectionChange(val) {
      this.sendUserArr = val
    },
    closeDialog() {
      this.showDialog = false
    },
    queryUserList(e) {
      const params = {
        pageNo: 1,
        pageSize: 12,
        sort: 'desc',
        sortType: 'time',
        codeId: e
      }
      fetchListByPage(params).then(res => {
        if (res.head.ret === 0 && res.data.result.length > 0) {
          this.userQueryList = res.data.result
        } else {
          delete params['codeId']
          params['nickName'] = e
          fetchListByPage(params).then(res2 => {
            if (res2.head.ret === 0) {
              this.userQueryList = res2.data.result
            }
          })
        }
      })
    },
    async exportx() {
      const parms = {
        beginDate: this.activityDate[0],
        endDate: this.activityDate[1]
      }
      const res = await exportStatisticsMissionFinish(parms)
      if (res.head.ret === 0) {
        const a = document.createElement('a')
        a.href = res.data // 这里的请求方式为get，如果需要认证，接口上需要带上token
        a.click()
      }
    }
  }
}
</script>
<style lang="scss" scoped>

</style>
