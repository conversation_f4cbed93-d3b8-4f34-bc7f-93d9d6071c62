import axios from '@/plugin/axios'
import Cookies from 'js-cookie'
import { getUrlParameter } from '@/utils/common'

/**
 * 获取Url用户ID参数值
 */
const getUrlUserId = () => {
  const query = getUrlParameter()
  return (
    Cookies.get('SIGN_USER_ID') || query.userid || query.userId || query.user_id
  )
}

/**
 * 获取商品列表
 * @param {*} goodids 商品列表ids
 */
export const getGoodList = async goodids => {
  let str = ''
  for (const goodid of goodids) {
    str += `&productids=${goodid}`
  }
  let { data } = await axios(
    `/mall/v1/item/getproductbyid?userid=${getUrlUserId()}${str}`
  )
  return data
}

/**
 * 判断用户是否购买过订单
 */
export const hasBuyOrder = async () => {
  let params = {
    userId: getUrlUserId()
  }
  let { data } = await axios('/ws/mall/v1/activity/hasbuyorder', {
    params
  })
  return data
}

/**
 * 判断用户是否领取活动优惠券
 * @param {String} activityId 活动ID 必填
 * @param {String} userId 用户ID 可选，不填则取url中的userId
 */
export const getReceived = async (activityId, userId) => {
  userId = userId || getUrlUserId()
  let params = {
    id: activityId,
    userid: userId
  }
  let { data } = await axios('/ws/qm/customer/activityreceived', {
    params
  })
  return data
}

/**
 * 用户根据活动ID领取活动优惠券
 * @param {String} activityId 活动ID
 * @param {String} userId 用户ID 可选，不填则取url中的userId
 */
export const getUserActivityCoupon = async (activityId, userId) => {
  userId = userId || getUrlUserId()
  let params = {
    id: activityId,
    userid: userId
  }
  let { data } = await axios('/ws/qm/customer/getuseractivitycoupon', {
    params
  })
  return data
}
