<template>
  <div class="faq">
    <div class="faq-title2">
      连接后可能出现的异常情况？
    </div>
    <div class="faq-content2">
      <p class="first-line">1、纸张不足：及时更换即可；</p>
      <p class="first-line">2、打印机温度过高：关机休息一下；</p>
      <p class="first-line">3、电量不足：及时充电；</p>
      <p class="first-line">4、打印机开盖了：检查设备，关好盖子。</p>
    </div>

  </div>
</template>

<script>
export default {
  data () {
    return {
      moduleKey: 'faq'
    }
  },
  async mounted () { },
  methods: {},
  components: {}
}
</script>

<style lang="scss">
@import '../../assets/scss/faq.scss'; // 全局faq自定义的css样式
</style>
