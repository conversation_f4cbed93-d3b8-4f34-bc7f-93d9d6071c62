<!--
 * @Description: In User Settings Edit
 * @Author: your name
 * @Date: 2019-10-16 11:28:20
 * @LastEditTime: 2019-10-16 11:38:48
 * @LastEditors: Please set LastEditors
 -->
<template>
  <div class="app-container">
    <div class="table-query-container">
      <el-row style="display:flex;line-height: 40px;padding:15px">
        <span class="demonstration" style="padding-right: 10px">用户ID</span>
        <el-input v-model="params.otherId" type="text" maxlength="100" style="width:200px;padding-right: 10px" />
      </el-row>
      <el-row>
        <span class="demonstration" style="padding-right: 10px">统计时间 </span>
        <el-date-picker
          v-model="timeRange"
          :value-format="timeFormat"
          :format="timeFormat"
          :unlink-panels="true"
          type="datetimerange"
          :clearable="false"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          @change="changeTimeRange"
        />
        <el-radio-group v-model="params.businessType" style="margin-bottom: 30px;" @change="changeBusinessType">
              <el-radio-button label="">全部</el-radio-button>
              <el-radio-button label="baiduOrc">百度orc</el-radio-button>
              <el-radio-button label="XueKeCond">试题推送</el-radio-button>
              <el-radio-button label="XueKeKeyword">关键词搜题</el-radio-button>
              <el-radio-button label="XueKeSimilar">相似题搜题</el-radio-button>
              <el-radio-button label="XueKeText">拍搜（精品）</el-radio-button>
              <el-radio-button label="XueKeTiku">拍搜（海量）</el-radio-button>
            </el-radio-group>
        <el-button type="primary" @click="handleQuery">查询</el-button>
      </el-row>
    </div>
    <div class="table-container">
      <el-table
        ref="multipleTable"
        :data="list"
        tooltip-effect="dark"
        style="width: 100%"
        border
        max-height="700px"
      >
        <el-table-column prop="userId" label="用户ID" header-align="center" align="center" />
        <el-table-column prop="callTime" label="调用时间" header-align="center" align="center" />
        <el-table-column prop="businessType" label="调用接口" header-align="center" align="center" />
      </el-table>
    </div>
    <div class="pagination-container" style="margin-top:20px;">
      <el-pagination
        :current-page="params.pageno"
        :page-sizes="[10, 50, 100]"
        :page-size="params.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>
<script>
import { getApiCallPage } from '@/api/dataanalysis'
import { deviceNameTypeList } from '@/consts'
export default {
  data() {
    return {
      // 查询参数
      params: {
        startDate: '',
        endDate: '',
        pageno: 1,
        pagesize: 10
      },
      /** 列表 */
      list: [],
      /** 总条数 */
      total: 0,
      downloadUrl: '',
      /** 时间段 */
      timeRange: '',
      otherid: '',
      deviceSn: '',
      deviceName: '',
      macAddress: '',
      /** 时间格式 */
      timeFormat: 'yyyy-MM-dd',
      showDialog: false,
      deviceNameTypeList: deviceNameTypeList
    }
  },
  mounted() {
    // this.getList()
  },
  methods: {
    changeBusinessType() {
        this.params["pageno"] = 1
        this.getList()
    },
    initData(startDate, endDate, otherid, businessType){
        this.params = {
          startdate: startDate,
          enddate: endDate,
          pageno: 1,
          pagesize: 10,
          businessType: businessType,
          otherId: otherid
        }
        this.timeRange =[startDate, endDate]
        this.getList()
    },
    /** 获取列表 */
    async getList() {
      
      const res = await getApiCallPage(this.params)
      if (res.head.ret === 0) {
        this.list = res.data.result
        this.total = res.data.totalCount
      }
    },
    /** 切换每页条数 */
    handleSizeChange(val) {
      this.params.pagesize = val
      this.getList()
    },
    /** 切换当前页 */
    handleCurrentChange(val) {
      this.params.pageno = val
      this.getList()
    },
    /** 选择时间段 */
    changeTimeRange(val) {
      this.params.startdate = val[0]
      this.params.enddate = val[1]
    },
    /** 查询 */
    handleQuery() {
      this.getList()
    },
    /** 查看动态 */
    handleActivity(row) {
      this.$router.push({
        path: '/user/userdraft',
        query: {
          userId: row.codeId,
          subType: row.deviceName
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>

</style>
