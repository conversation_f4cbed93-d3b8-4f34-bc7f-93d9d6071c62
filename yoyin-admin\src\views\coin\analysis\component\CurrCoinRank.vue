<template>
  <div class="app-container">
    <div class="table-query-container">
      <div class="query-code-btn">
        <span class="demonstration">用户昵称或ID</span>
        <el-select
          v-model="searchUserId"
          filterable
          remote
          reserve-keyword
          placeholder="请输入昵称或ID"
          :remote-method="queryUserList"
          :loading="loading"
        >
          <el-option
            v-for="item in userQueryList"
            :key="item.id"
            :label="item.nickName"
            :value="item.userId"
          />
        </el-select>

        <el-input-number v-model="rankTop" />
        <el-button size="medium" style="margin-left:20px;" @click="getList">查询</el-button>
        <el-button size="medium" style="margin-left:20px;" @click="reset">重置</el-button>
        <el-button v-permission="'btn-menuCoin-statisticscoin-edit'" size="medium" style="margin-left:20px;" @click="exportx">导出</el-button>
      </div>
    </div>
    <div class="table-container">
      <el-table
        ref="multipleTable"
        :data="list"
        tooltip-effect="dark"
        style="width: 100%"
        border
      >
        <el-table-column type="selection" width="55" />
        <el-table-column label="名次" width="55">
          <template slot-scope="scope">{{ scope.row.rankIndex }}</template>
        </el-table-column>
        <el-table-column label="用户昵称">
          <template slot-scope="scope">{{ scope.row.userInfoDto.name }}</template>
        </el-table-column>
        <el-table-column label="用户id">
          <template slot-scope="scope">{{ scope.row.userInfoDto.codeId }}</template>
        </el-table-column>
        <el-table-column label="当前积分余额">
          <template slot-scope="scope">{{ scope.row.currCoin }}</template>
        </el-table-column>
        <el-table-column label="累计积分余额">
          <template slot-scope="scope">{{ scope.row.totalCoin }}</template>
        </el-table-column>
        <el-table-column label="奖罚明细" min-width="200">
          <template slot-scope="scope">
            <el-collapse>
              <el-collapse-item title="点击展开明细" name="1">
                <div v-for="(item, index) in scope.row.missionList" :key="scope.row.userInfoDto.userId + index + item.name">
                  {{ item.name }}：{{ item.num }}
                </div>
              </el-collapse-item>
            </el-collapse>
          </template>
        </el-table-column>
        <el-table-column label="操作">
          <template slot-scope="scope">
            <el-button v-permission="'btn-menuCoin-statisticscoin-edit'" @click="showSendDialog(scope.row.userInfoDto.userId)">发送奖励</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="dialog-container">
      <el-dialog :visible.sync="showDialog" title="发送奖励消息">
        <el-form ref="form" :model="form" label-width="120px">
          <el-form-item label="奖励金额">
            <el-input-number v-model="form.num" />
          </el-form-item>
          <el-form-item label="官方消息内容">
            <el-input v-model="form.info" type="textarea" />
          </el-form-item>
          <el-form-item>
            <el-button @click="closeDialog">关闭</el-button>
            <el-button v-permission="'btn-menuCoin-statisticscoin-edit'" type="primary" @click="submit">发送</el-button>
          </el-form-item>
        </el-form>
      </el-dialog>
    </div>
  </div>
</template>
<script>
import { getStatisticsUserCoinRankList, exportStatisticsUserCoinRankList, giveCoinCurrRecord } from '@/api/coin/analysis'
import { fetchListByPage } from '@/api/user/list'
export default {
  name: 'CurrCoinRank',
  data() {
    return {
      // 查询参数
      /** 列表 */
      list: [],
      /** 总条数 */
      rankTop: 10,
      showDialog: false,
      searchUserId: '',
      userQueryList: [],
      loading: false,
      form: {
        num: 1,
        info: '',
        userId: ''
      }
    }
  },
  mounted() {
    this.getList()
  },
  methods: {
    reset() {
      this.searchUserId = ''
    },
    /** 获取列表 */
    async getList() {
      const parms = {
        top: this.rankTop,
        searchUserId: this.searchUserId
      }
      const res = await getStatisticsUserCoinRankList(parms)
      if (res.head.ret === 0) {
        this.list = res.data
        // this.total = res.data.totalCount
      }
    },

    handleSelectionChange(val) {
      this.sendUserArr = val
    },
    closeDialog() {
      this.showDialog = false
    },
    submit() {
      if (this.form.num === 0) {
        this.$message.error('请输入积分数量')
        return
      }
      if (this.form.info === '' || this.form.info.trim() === '') {
        this.$message.error('请输入官方消息内容')
        return
      }
      this.form.userIds = ''
      this.sendUserArr.forEach(element => {
        this.form.userIds += element + ';'
      })
      this.$confirm('此操作将奖励并发送官方消息给用户, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const loading = this.$loading({
          lock: true,
          text: '提交中',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        })
        giveCoinCurrRecord(this.form)
        setTimeout(() => {
          loading.close()
          this.closeDialog()
          this.$message({
            type: 'success',
            message: '操作成功!'
          })
        }, 3000)
      }).catch(() => {

      })
    },
    showSendDialog(userId) {
      this.sendUserArr = []
      this.sendUserArr.push(userId)
      this.showDialog = true
    },
    queryUserList(e) {
      const params = {
        pageNo: 1,
        pageSize: 12,
        sort: 'desc',
        sortType: 'time',
        codeId: e
      }
      fetchListByPage(params).then(res => {
        if (res.head.ret === 0 && res.data.result.length > 0) {
          this.userQueryList = res.data.result
        } else {
          delete params['codeId']
          params['nickName'] = e
          fetchListByPage(params).then(res2 => {
            if (res2.head.ret === 0) {
              this.userQueryList = res2.data.result
            }
          })
        }
      })
    },
    async exportx() {
      const parms = {
        top: this.rankTop,
        searchUserId: this.searchUserId
      }
      const res = await exportStatisticsUserCoinRankList(parms)
      if (res.head.ret === 0) {
        const a = document.createElement('a')
        a.href = res.data // 这里的请求方式为get，如果需要认证，接口上需要带上token
        a.click()
      }
    }
  }
}
</script>
<style lang="scss" scoped>

</style>
