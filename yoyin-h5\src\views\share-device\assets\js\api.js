import axios from '@/plugin/axios'

export const getShareInfo = async shareid => {
  const params = {
    shareid
  }
  const { data } = await axios.get(
    '/api/gam/community/v1/share/getshareprintinfo',
    {
      params
    }
  )
  return data
}

/**
 * 保存分享纸条消息
 * @param {Object} params 具体参数
 */
export const savePrinterRecord = async params => {
  const { data } = await axios.post(
    '/api/gam/community/v1/share/saveprintrecord',
    params
  )
  return data
}
