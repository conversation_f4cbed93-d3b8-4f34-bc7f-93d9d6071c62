<template>
  <div class="content">
    <img src="./assets/img/h5_pic_share01.png" />
    <div v-if="downloadUrl" class="bottom-box">
      <div class="img-log"> </div>
      <div class="name">{{$t('starpany')}}</div>
      <a class="btn-download" :href="downloadUrl">{{$t('share-about-us.download-now')}}</a>
    </div>
    <div v-if="isApp" class="btn-share" @click="sharePage">{{$t('share-about-us.share')}}</div>
  </div>
</template>

<script>
import Cookies from 'js-cookie'
export default {
  data () {
    return {
      moduleKey: 'share-about-us',
      isApp: false,
      downloadUrl: undefined
    }
  },
  computed: {
    isiosSystem() {
      if (window.webkit !== undefined && window.webkit.messageHandlers !== undefined) {
        return true
      } else {
        return false
      }
    }
  },
  async mounted () {
    if (window.StarPrinterJS || this.isiosSystem) {
      this.isApp = true
    } else {
      let ua = navigator.userAgent
      let isiOS = !!ua.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/) || ua.indexOf('iPhone') > -1 || ua.indexOf('iPad') > -1
      this.downloadUrl = isiOS
        ? 'https://itunes.apple.com/cn/app/id1440940435'
        : 'https://a.app.qq.com/o/simple.jsp?pkgname=com.starprinter.app&channel=0002160650432d595942&fromcase=60001'
    }
  },
  methods: {
    sharePage () {
      const params = {
        title: this.$t('starpany'),
        desc: this.$t('share-about-us.product-manual'),
        content: `${location.href}?time=${new Date().getTime()}`
      }
      if (this.isiosSystem) {
        window.webkit.messageHandlers.share.postMessage(JSON.stringify(params))
      } else {
        window.StarPrinterJS.share(JSON.stringify(params))
      }
    }
  },
  components: {}
}
</script>

<style lang="scss">
.content {
  img {
    width: 100%;
    height: auto;
    padding-bottom: 3.125rem;
  }

  .btn-share {
    position: fixed;
    bottom: 0;
    width: 100%;
    height: 3.0625rem;
    line-height: 3.0625rem;
    background: #5a7dff;
    color: white;
    font-size: 1rem;
    text-align: center;
  }

  .bottom-box {
    position: fixed;
    bottom: 0;
    width: 100%;
    height: 3.4375rem;
    display: flex;
    align-items: center;
    background: white;
    border-top: 1px solid #d5d4d9;
    .img-log {
      width: 2rem;
      height: 2rem;
      margin-left: 1rem;
      background-image: url('./assets/img/icon_logo.png');
      background-size: 100%;
    }
    .name {
      flex: 1;
      font-size: 1rem;
      color: #000000;
      line-height: 1rem;
      margin-left: 0.75rem;
    }

    .btn-download {
      background: #5a7dff;
      box-shadow: 0 8px 8px 0 rgba(0, 54, 255, 0.16);
      border-radius: 3.125rem;
      margin-right: 1rem;
      padding: 0 12px;
      color: white;
      font-size: 0.875rem;
      line-height: 2.375rem;
      text-align: center;
    }
  }
}
</style>
