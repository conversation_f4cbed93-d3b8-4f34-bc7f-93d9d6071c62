<template>
  <div class="app-container">
    <div class="app-container">
      <el-row>
        <span class="demonstration" style="padding-right: 10px">账号/ID </span>
        <el-input v-model="otherid" type="text" maxlength="100" style="width:120px;padding-right: 10px" />
        <span class="demonstration" style="padding-right: 10px">功能类别 </span>
        <el-select v-model="subType" placeholder="请选择类别">
          <el-option lablel="全部" value="">全部</el-option>
          <el-option
            v-for="item in subTypeData"
            :key="item.id"
            :label="item.label"
            :value="item.id"
          />
        </el-select>
        <span class="demonstration" style="padding-right: 10px">设备名称</span>
        <el-select v-model="printerType" placeholder="请选择设备">
          <el-option lablel="全部" value="">全部</el-option>
          <el-option
            v-for="item in deviceNameTypeList"
            :key="item.key"
            :label="item.value"
            :value="item.key"
          />
        </el-select>
        <span class="demonstration" style="padding-right: 10px">打印时间 </span>
        <el-date-picker
          v-model="timeRange"
          :value-format="timeFormat"
          :format="timeFormat"
          :unlink-panels="true"
          type="datetimerange"
          :clearable="false"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          @change="changeTimeRange"
        />
        <el-button type="primary" @click="handleQuery">查询</el-button>
      </el-row>
    </div>
    <div class="table-container">
      <el-table
        ref="multipleTable"
        :data="list"
        tooltip-effect="dark"
        style="width: 100%"
        border
      >
        <el-table-column label="用户账号">
          <template slot-scope="scope">
            {{ scope.row.userName }}
          </template>
        </el-table-column>
        <el-table-column label="用户id">
          <template slot-scope="scope">
            {{ scope.row.codeId }}
          </template>
        </el-table-column>
        <el-table-column label="功能类型">
          <template slot-scope="scope">
            {{ formatPaperObj(scope.row) }}
          </template>
        </el-table-column>
        <el-table-column label="缩略图">
          <template slot-scope="scope">
            <!-- <img :src="scope.row.pic" style="width:120px; height:80px; object-fit:contain"> -->
            <el-image
              v-for="pics in scope.row.pic"
              :key="pics"
              style="width:120px; height:80px;"
              :src="pics"
              :preview-src-list="scope.row.pic"
              fit="contain"
              @click.stop="handleClickStop"
            />
          </template>
        </el-table-column>
        <el-table-column label="打印时间">
          <template slot-scope="scope">
            {{ scope.row.createTime }}
          </template>
        </el-table-column>
        <el-table-column label="打印设备">
          <template slot-scope="scope">
            {{ scope.row.printerType }}
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="pagination-container" style="margin-top:20px;">
      <el-pagination
        :current-page="params.pageno"
        :page-sizes="[10, 20, 30, 40]"
        :page-size="params.pagesize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>
<script>
import { fetchUserDraftListByPage } from '@/api/draft/userdraft'
import  {deviceNameTypeList} from '@/consts/index'
export default ({
  name: 'UserDraft',
  data() {
    return {
      deviceNameTypeList: deviceNameTypeList,
      subTypeData: [
        { 'id': '0', 'label': '编辑纸条' },
        { 'id': '1', 'label': '清单' },
        { 'id': '2', 'label': '便利贴' },
        { 'id': '3', 'label': '大字横幅' },
        { 'id': '4', 'label': '网页打印' },
        { 'id': '5', 'label': '课程表' },
        { 'id': '6', 'label': '名片' },
        { 'id': '7', 'label': '识别卡' },
        { 'id': '8', 'label': '横向编辑纸条' },
        { 'id': '9', 'label': '横向表格' },
        { 'id': '10', 'label': '竖向表格' },
        { 'id': '11', 'label': 'A4打印' },
        { 'id': '12', 'label': '姓名贴' },
        { 'id': '13', 'label': '去字迹' }
      ],
      /** 时间格式 */
      timeFormat: 'yyyy-MM-dd',
      // 查询参数
      params: {
        pageno: 1,
        pagesize: 10
      },
      /** 校验规则 */
      rules: {
        // name: [
        //   { required: true, message: '请输入标题', trigger: 'blur' }
        // ],
        content: [
          { required: true, message: '请输入描述', trigger: 'blur' }
        ]
      },
      /** 列表 */
      list: [],
      /** 总条数 */
      total: 0,
      loading: false,
      userQueryList: [],
      showDialog: false,
      templateDraft: {},
      paperSizeData: [],
      otherid: '',
      subType: '',
      printerType: '',
      timeRange: []

    }
  },
  mounted() {
    const userId = this.$route.query.userId
    const subType = this.$route.query.subType
    if (userId) {
      this.otherid = userId
    }
    if (subType) {
      this.subType = subType
    }
    this.getList()
  },
  methods: {
    formatPaperObj(row) {
      const subType = row.subType
      for (let index = 0; index < this.subTypeData.length; index++) {
        const element = this.subTypeData[index]
        if (subType === element.id) {
          return element.label
        }
      }
      return ''
    },
    /** 选择时间段 */
    changeTimeRange(val) {
      this.params.startdate = val[0]
      this.params.enddate = val[1]
    },
    /** 查询 */
    handleQuery() {
      this.params.pageno = 1
      this.getList()
    },
    /** 获取列表 */
    async getList() {
      this.loading = true

      if (this.otherid) {
        this.params['otherid'] = this.otherid
      } else {
        delete this.params['otherid']
      }
      if (this.subType) {
        this.params['subType'] = this.subType
      } else {
        delete this.params['subType']
      }
      if (this.printerType || this.printerType === 0) {
        this.params['printerType'] = this.printerType
      } else {
        delete this.params['printerType']
      }
      console.log('params', this.params)
      const res = await fetchUserDraftListByPage(this.params)
      this.loading = false
      if (res.head.ret === 0) {
        this.list = []
        this.list = res.data.result
        this.total = res.data.totalCount
      } else {
        this.$message.console.error(res.head.msg)
      }
    },
    handleClickStop() {
      this.$nextTick(() => {
        const domImageView = document.querySelector('.el-image-viewer__mask') // 获取遮罩层dom
        if (!domImageView) {
          return
        }
        domImageView.addEventListener('click', () => {
          // 点击遮罩层时调用关闭按钮的 click 事件
          document.querySelector('.el-image-viewer__close').click()
        })
      })
    },
    /** 切换每页条数 */
    handleSizeChange(val) {
      this.params.pagesize = val
      this.getList()
    },
    /** 切换当前页 */
    handleCurrentChange(val) {
      this.params.pageno = val
      this.getList()
    },
    handleClose() {
      this.showDialog = false
    }
  }
})
</script>
