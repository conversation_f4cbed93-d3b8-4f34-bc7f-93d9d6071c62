import request from '@/utils/request'

// 翻页查询
const LIST_URL = '/platform/gam/community/v1/initialtraining/getDictPage'
const DELETE_RECORD_URL = '/platform/gam/community/v1/initialtraining/deleteDict'
const SAVE_URL = '/platform/gam/community/v1/initialtraining/addOrUpdateDict'
const DISTINCT_TYPE_URL = '/platform/gam/community/v1/initialtraining/getDistinctTypeList'
// 删除记录

/**
 * 查询列表数据
 * @param params 参数
 */
export const fetchListByPage = async params => {
  return request({
    url: LIST_URL,
    method: 'get',
    params: params
  })
}

/**
 * 删除记录
 * @param {String} id 记录id
 */
export const deleteRecord = async params => {
  return request({
    url: DELETE_RECORD_URL,
    method: 'get',
    params
  })
}

/** 保存记录 */
export const saveRecord = async params => {
  return request({
    url: SAVE_URL,
    method: 'post',
    params
  })
}

export const fetchDistinctTypeList = async() => {
  return request({
    url: DISTINCT_TYPE_URL,
    method: 'get'
  })
}

