<!--
 * @Description: In User Settings Edit
 * @Author: your name
 * @Date: 2019-10-16 11:28:20
 * @LastEditTime: 2019-10-23 09:51:04
 * @LastEditors: Please set LastEditors
 -->
<template>
  <div class="app-container">
    <el-tabs v-model="activeName" type="card">
      <!-- 活动页 -->
      <el-tab-pane label="活动页" name="0">
        <el-form ref="activityForm" :model="activityForm" :rules="activityRules" label-width="100px" class="demo-activityForm">
          <el-form-item label="名称" prop="name">
            <el-input v-model="activityForm.name" />
          </el-form-item>
          <el-row>
            <el-col :span="12">
              <el-form-item label="类型" prop="jumpType">
                <el-radio-group v-model="activityForm.jumpType">
                  <el-radio-button label="100">无跳转</el-radio-button>
                  <el-radio-button label="101">H5</el-radio-button>
                  <el-radio-button label="105">原生</el-radio-button>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="起始时间" prop="startTime">
                <el-date-picker
                  v-model="activityForm.startTime"
                  :value-format="timeFormat"
                  type="datetime"
                  placeholder="选择日期时间"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item label="状态" prop="status">
                <el-radio-group v-model="activityForm.status">
                  <el-radio-button label="审核通过">启用</el-radio-button>
                  <el-radio-button label="已删除">禁用</el-radio-button>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="结束时间" prop="endTime">
                <el-date-picker
                  v-model="activityForm.endTime"
                  :value-format="timeFormat"
                  type="datetime"
                  placeholder="选择日期时间"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-form-item v-if="showActivityLink" label="链接" prop="jumpVal">
            <el-input v-model="activityForm.jumpVal" />
          </el-form-item>
          <el-form-item label="开始展示的版本号" prop="version">
            <el-input
              v-model="activityForm.version"
              type="text"
              maxlength="200"
              placeholder="请输入格式x.x.x"
            />
            {主版本号} + "." + {版本小号} + "." + {修复版本号} 例如:  3.3.1
          </el-form-item>
          <el-form-item v-if="activityForm.jumpType === 105 || activityForm.jumpType === '105' " label="andriod调用参数" prop="paramAndroid">
            <el-input v-model="activityForm.paramAndroid" />
          </el-form-item>
          <el-form-item v-if="activityForm.jumpType === 105 || activityForm.jumpType === '105' " label="ios调用参数" prop="paramIos">
            <el-input v-model="activityForm.paramIos" />
          </el-form-item>
          <el-form-item label="活动图片" prop="pics">
            <el-upload
              action="OSS上传路径，必填"
              list-type="picture-card"
              :before-upload="beforeUpload"
              :http-request="upLoad"
              :on-preview="handlePictureCardPreview"
              :on-remove="handleRemove"
              :file-list="activityForm.pics"
            >
              <i class="el-icon-plus" />
            </el-upload>
            <el-dialog :visible.sync="dialogVisible">
              <img width="100%" :src="dialogImageUrl" alt="">
            </el-dialog>
          </el-form-item>

          <el-form-item>
            <el-button v-permission="'btn-menuSystemsetting-activion-edit'" @click="resetForm('activityForm')">重置</el-button>
            <el-button v-permission="'btn-menuSystemsetting-activion-edit'" type="primary" @click="submitForm('activityForm')">保存</el-button>
          </el-form-item>
        </el-form>
      </el-tab-pane>
      <!-- 启动页 -->
      <el-tab-pane label="启动页" name="1">
        <el-form ref="startForm" :model="startForm" :rules="startRules" label-width="100px" class="demo-activityForm">
          <el-form-item label="名称" prop="name">
            <el-input v-model="startForm.name" />
          </el-form-item>

          <el-form-item label="类型" prop="type">
            <el-radio-group v-model="startForm.type">
              <el-radio-button label="100">无跳转</el-radio-button>
              <el-radio-button label="101">H5</el-radio-button>
              <el-radio-button label="105">原生</el-radio-button>
            </el-radio-group>
          </el-form-item>

          <el-row>
            <el-col :span="12">
              <el-form-item label="起始时间" prop="startTime">
                <el-date-picker
                  v-model="startForm.startTime"
                  :value-format="timeFormat"
                  type="datetime"
                  placeholder="选择日期时间"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="结束时间" prop="endTime">
                <el-date-picker
                  v-model="startForm.endTime"
                  :value-format="timeFormat"
                  type="datetime"
                  placeholder="选择日期时间"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-form-item v-if="showStartLink" label="链接" prop="jumpVal">
            <el-input v-model="startForm.jumpVal" />
          </el-form-item>

          <el-form-item label="状态" prop="status">
            <el-radio-group v-model="startForm.status">
              <el-radio-button label="审核通过">启用</el-radio-button>
              <el-radio-button label="已删除">禁用</el-radio-button>
            </el-radio-group>
          </el-form-item>

          <el-form-item label="开始展示的版本号" prop="version">
            <el-input
              v-model="startForm.version"
              type="text"
              maxlength="200"
              placeholder="请输入格式x.x.x"
            />
            {主版本号} + "." + {版本小号} + "." + {修复版本号} 例如:  3.3.1
          </el-form-item>
          <el-form-item v-if="startForm.type === 105 || startForm.type === '105' " label="andriod调用参数" prop="paramAndroid">
            <el-input v-model="startForm.paramAndroid" />
          </el-form-item>
          <el-form-item v-if="startForm.type === 105 || startForm.type === '105' " label="ios调用参数" prop="paramIos">
            <el-input v-model="startForm.paramIos" />
          </el-form-item>

          <el-form-item label="大图" prop="loadingPic">
            <el-upload
              action="OSS上传路径，必填"
              list-type="picture-card"
              :before-upload="beforeUploadBig"
              :http-request="upLoadLoading"
              :on-preview="handlePictureCardPreview"
              :before-remove="beforeoRemoveStart"
              :file-list="startForm.loadingPic"
            >
              <i class="el-icon-plus" />
            </el-upload>
            <el-dialog :visible.sync="dialogVisible">
              <img width="100%" :src="dialogImageUrl" alt="">
            </el-dialog>
          </el-form-item>

          <el-form-item label="小图" prop="loadingMinPic">
            <el-upload
              action="OSS上传路径，必填"
              list-type="picture-card"
              :before-upload="beforeUploadSm"
              :http-request="upLoadLoadingMin"
              :on-preview="handlePictureCardPreview"
              :before-remove="beforeoRemoveStart"
              :file-list="startForm.loadingMinPic"
            >
              <i class="el-icon-plus" />
            </el-upload>
            <el-dialog :visible.sync="dialogVisible">
              <img width="100%" :src="dialogImageUrl" alt="">
            </el-dialog>
          </el-form-item>

          <el-form-item>
            <el-button v-permission="'btn-menuSystemsetting-activion-edit'" @click="resetForm('startForm')">重置</el-button>
            <el-button v-permission="'btn-menuSystemsetting-activion-edit'" type="primary" @click="saveStartConfig('startForm')">保存</el-button>
          </el-form-item>
        </el-form>

      </el-tab-pane>
      <!-- 活动页 -->
      <el-tab-pane label="悬浮配置" name="2">
        <el-form ref="danglingForm" :model="danglingForm" :rules="danglingRules" label-width="100px" class="demo-danglingForm">
          <el-form-item label="名称" prop="name">
            <el-input v-model="danglingForm.name" />
          </el-form-item>
          <el-row>
            <el-col :span="12">
              <el-form-item label="类型" prop="jumpType">
                <el-radio-group v-model="danglingForm.jumpType">
                  <el-radio-button label="100">无跳转</el-radio-button>
                  <el-radio-button label="101">H5</el-radio-button>
                  <el-radio-button label="105">原生</el-radio-button>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="起始时间" prop="startTime">
                <el-date-picker
                  v-model="danglingForm.startTime"
                  :value-format="timeFormat"
                  type="datetime"
                  placeholder="选择日期时间"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item label="状态" prop="status">
                <el-radio-group v-model="danglingForm.status">
                  <el-radio-button label="审核通过">启用</el-radio-button>
                  <el-radio-button label="已删除">禁用</el-radio-button>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="结束时间" prop="endTime">
                <el-date-picker
                  v-model="danglingForm.endTime"
                  :value-format="timeFormat"
                  type="datetime"
                  placeholder="选择日期时间"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-form-item label="开始展示的版本号" prop="version">
            <el-input
              v-model="danglingForm.version"
              type="text"
              maxlength="200"
              placeholder="请输入格式x.x.x"
            />
            {主版本号} + "." + {版本小号} + "." + {修复版本号} 例如:  3.3.1
          </el-form-item>
          <el-form-item v-if="showDanglingLink" label="链接" prop="jumpVal">
            <el-input v-model="danglingForm.jumpVal" />
          </el-form-item>
          <el-form-item v-if="danglingForm.jumpType === 105 || danglingForm.jumpType === '105' " label="andriod调用参数" prop="paramAndroid">
            <el-input v-model="danglingForm.paramAndroid" />
          </el-form-item>
          <el-form-item v-if="danglingForm.jumpType === 105 || danglingForm.jumpType === '105' " label="ios调用参数" prop="paramIos">
            <el-input v-model="danglingForm.paramIos" />
          </el-form-item>

          <el-form-item label="悬浮图片" prop="pics">
            <el-upload
              action="OSS上传路径，必填"
              list-type="picture-card"
              :before-upload="beforeUploadDangling"
              :http-request="upLoadDangling"
              :on-preview="handlePictureCardPreview"
              :on-remove="handleRemoveDangling"
              :file-list="danglingForm.pics"
            >
              <i class="el-icon-plus" />
            </el-upload>
            <el-dialog :visible.sync="dialogVisible">
              <img width="100%" :src="dialogImageUrl" alt="">
            </el-dialog>
          </el-form-item>

          <el-form-item>
            <el-button v-permission="'btn-menuSystemsetting-activion-edit'" @click="resetForm('danglingForm')">重置</el-button>
            <el-button v-permission="'btn-menuSystemsetting-activion-edit'" type="primary" @click="submitFormDangling('danglingForm')">保存</el-button>
          </el-form-item>
        </el-form>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
<script>
import { getSystemConfig, updateConfig } from '@/api/systemsetting/setting'
import { uuid } from '@/utils/index'
import AliyunOSS from '@/utils/aliyunOSS'
const oss = new AliyunOSS()
export default {
  data() {
    return {
      // 悬浮页提交参数
      danglingParams: {},
      // 活动页提交参数
      actParams: {},
      // 启动页提交参数
      startParams: {},
      // 基础表单内容
      initStartForm: {},
      initForm: {},
      activeName: '0',
      dialogImageUrl: '',
      dialogVisible: false,
      activityForm: {
        name: '',
        jumpType: '',
        status: '',
        jumpVal: '',
        version: '',
        paramAndroid: '',
        paramIos: '',
        startTime: '',
        endTime: '',
        pics: []
      },
      danglingForm: {
        name: '',
        jumpType: '',
        status: '',
        jumpVal: '',
        version: '',
        paramAndroid: '',
        paramIos: '',
        startTime: '',
        endTime: '',
        pics: []
      },
      startForm: {
        name: '',
        type: '',
        jumpVal: '',
        status: '',
        version: '',
        paramAndroid: '',
        paramIos: '',
        startTime: '',
        endTime: '',
        loadingPic: [],
        loadingMinPic: []
      },
      /** 时间格式 */
      timeFormat: 'yyyy-MM-dd HH:mm:ss',
      activityRules: {
        name: [
          { required: true, message: '请输入活动名称', trigger: 'blur' },
          { min: 3, max: 20, message: '长度在 3 到 20 个字符', trigger: 'blur' }
        ],
        jumpType: [
          { required: true, message: '请选择类型', trigger: 'change' }
        ],
        status: [
          { required: true, message: '请选择状态', trigger: 'change' }
        ],
        jumpVal: [
          { required: true, message: '请输入活动链接', trigger: 'blur' }
        ],
        pics: [
          { required: true, message: '请选择活动图片', trigger: 'change' }
        ]
      },
      danglingRules: {
        name: [
          { required: true, message: '请输入活动名称', trigger: 'blur' },
          { min: 3, max: 20, message: '长度在 3 到 20 个字符', trigger: 'blur' }
        ],
        jumpType: [
          { required: true, message: '请选择类型', trigger: 'change' }
        ],
        status: [
          { required: true, message: '请选择状态', trigger: 'change' }
        ],
        jumpVal: [
          { required: true, message: '请输入活动链接', trigger: 'blur' }
        ],
        pics: [
          { required: true, message: '请选择活动图片', trigger: 'change' }
        ]
      },
      startRules: {
        name: [
          { required: true, message: '请输入名称', trigger: 'blur' },
          { min: 3, max: 20, message: '长度在 3 到 20 个字符', trigger: 'blur' }
        ],
        type: [
          { required: true, message: '请选择类型', trigger: 'change' }
        ],
        jumpVal: [
          { required: true, message: '请输入活动链接', trigger: 'blur' }
        ],
        status: [
          { required: true, message: '请选择状态', trigger: 'change' }
        ],
        loadingPic: [
          { required: true, message: '请选择大图', trigger: 'change' }
        ],
        loadingMinPic: [
          { required: true, message: '请选择小图', trigger: 'change' }
        ]
      }
    }
  },
  computed: {
    showStartLink() {
      return this.startForm.type === '101'
    },
    showActivityLink() {
      return this.activityForm.jumpType.toString() === '101'
    },
    showDanglingLink() {
      return this.danglingForm.jumpType.toString() === '101'
    }
  },
  mounted() {
    this.getConfig()
  },
  methods: {
    /** 保存启动页 */
    saveStartConfig(formName) {
      this.$refs[formName].validate(async(valid) => {
        if (valid) {
          // 参数
          const loadUrl = this.startForm.loadingPic[0].url
          const loadMinUrl = this.startForm.loadingMinPic[0].url
          this.startParams.value = { ...this.startForm }
          this.startParams.value.loadingMinPic = loadMinUrl
          this.startParams.value.loadingPic = loadUrl
          this.startParams.status = this.startForm.status
          // 无图片上传
          if (!this.startForm.loadingPic[0].file && !this.startForm.loadingMinPic[0].file) {
            this.updateStartConfig()
            return
          }

          // 有图片上传
          const uploadFiles = []
          if (this.startForm.loadingPic[0].file) {
            this.startForm.loadingPic[0].file.key = 'loadingPic'
            uploadFiles.push(this.startForm.loadingPic[0].file)
          }
          if (this.startForm.loadingMinPic[0].file) {
            this.startForm.loadingMinPic[0].file.key = 'loadingMinPic'
            uploadFiles.push(this.startForm.loadingMinPic[0].file)
          }
          oss.uploadFiles(uploadFiles, (results, error) => {
            if (results) {
              results.forEach(res => {
                this.startForm[res.key][0].url = res.name
                this.startForm[res.key][0].file = null
              })
              this.startParams.value.loadingMinPic = this.startForm.loadingMinPic[0].url
              this.startParams.value.loadingPic = this.startForm.loadingPic[0].url
              this.updateStartConfig()
            } if (error) {
              this.$message({ message: '上传图片失败', type: 'error' })
              return
            }

            return
          })
        } else {
          return false
        }
      })
    },
    /** 更新启动页数据 */
    updateStartConfig() {
      updateConfig(this.startParams).then(res => {
        if (res.head.ret === 0) {
          this.$message({
            message: '上传成功',
            type: 'success'
          })
        }
      })
    },
    /** 保存活动页 */
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          const uploadFiles = []
          const pics = []
          const files = this.activityForm.pics
          files.forEach(item => {
            if (item.file) {
              uploadFiles.push(item.file)
            }
            if (item.url.indexOf('http') > -1) {
              pics.push(item.url)
            }
          })

          if (uploadFiles.length > 0) {
            // 上传
            oss.uploadFiles(uploadFiles, (results, error) => {
              if (results) {
                results.forEach(res => {
                  pics.push(res.name)
                })
                this.actParams.status = this.activityForm.status
                this.actParams.value = { ...this.activityForm }
                this.actParams.value.pics = pics
                // 更新
                updateConfig(this.actParams).then(res => {
                  this.$message({
                    message: '上传成功',
                    type: 'success'
                  })
                })
              }
              if (error) {
                return
              }
            })
          } else {
            this.actParams.status = this.activityForm.status
            this.actParams.value = { ...this.activityForm }
            this.actParams.value.pics = pics
            // 更新
            updateConfig(this.actParams).then(res => {
              this.$message({
                message: '保存成功',
                type: 'success'
              })
            })
          }
        } else {
          return false
        }
      })
    },
    submitFormDangling(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          const uploadFiles = []
          const pics = []
          const files = this.danglingForm.pics
          files.forEach(item => {
            if (item.file) {
              uploadFiles.push(item.file)
            }
            if (item.url.indexOf('http') > -1) {
              pics.push(item.url)
            }
          })
          this.danglingParams.id = this.danglingForm.id
          if (uploadFiles.length === 0) {
            this.danglingParams.status = this.danglingForm.status
            this.danglingParams.value = { ...this.danglingForm }
            this.danglingParams.value.pics = pics
            this.danglingParams.value.id = this.danglingForm.id
            // 更新
            updateConfig(this.danglingParams).then(res => {
              this.$message({
                message: '上传成功',
                type: 'success'
              })
            })
          }
          // 上传
          oss.uploadFiles(uploadFiles, (results, error) => {
            if (results) {
              results.forEach(res => {
                pics.push(res.name)
              })
              this.danglingParams.status = this.danglingForm.status
              this.danglingParams.value = { ...this.danglingForm }
              this.danglingParams.value.pics = pics
              // 更新
              updateConfig(this.danglingParams).then(res => {
                this.$message({
                  message: '上传成功',
                  type: 'success'
                })
              })
            }
            if (error) {
              return
            }
          })
        } else {
          return false
        }
      })
    },
    /** 重置 */
    resetForm(formName) {
      // this.$refs[formName].resetFields()
      if (formName === 'startForm') {
        this.startForm = { ...this.initStartForm }
        this.startForm.loadingMinPic = []
        this.startForm.loadingPic = []
        this.startForm.loadingMinPic.push(this.initStartForm.loadingMinPic[0])
        this.startForm.loadingPic.push(this.initStartForm.loadingPic[0])
      }
      if (formName === 'activityForm') {
        this.activityForm = { ...this.initForm }
      }
    },
    /** 移除 */
    handleRemove(file, fileList) {
      this.activityForm.pics = fileList
    },
    /** 移除 */
    handleRemoveDangling(file, fileList) {
      this.danglingForm.pics = fileList
    },
    /** 移除 */
    beforeoRemoveStart(file, fileList) {
      if (fileList.length === 1) {
        this.$message({
          message: '请至少保留一张图片',
          type: 'warning'
        })
        return false
      }
    },
    /** 查看 */
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url
      this.dialogVisible = true
    },
    /** 上传前 */
    beforeUpload(file) {
      if (file && /image\//i.test(file.type)) {
        this.getBase64(file, img => {
          this.activityForm.pics.push({ url: img, file, uid: uuid() })
        })
      }
      return false
    },
    /** 上传前 */
    beforeUploadDangling(file) {
      if (file && /image\//i.test(file.type)) {
        this.getBase64(file, img => {
          this.danglingForm.pics.push({ url: img, file, uid: uuid() })
        })
      }
      return false
    },
    /** 上传大图前 */
    beforeUploadBig(file) {
      let pic = {}
      if (file && /image\//i.test(file.type)) {
        this.getBase64(file, img => {
          pic = { url: img, file, uid: uuid() }
          this.startForm.loadingPic.pop()
          this.startForm.loadingPic.push(pic)
        })
      }
      return false
    },
    /** 上传小图图前 */
    beforeUploadSm(file) {
      let pic = {}
      if (file && /image\//i.test(file.type)) {
        this.getBase64(file, img => {
          pic = { url: img, file, uid: uuid() }
          this.startForm.loadingMinPic.pop()
          this.startForm.loadingMinPic.push(pic)
        })
      }
      return false
    },
    /** get Base64 */
    getBase64(img, callback) {
      const reader = new FileReader()
      reader.addEventListener('load', () => callback(reader.result))
      reader.readAsDataURL(img)
    },
    /** 活动页上传文件 */
    upLoad(file) {

    },
    /** 启动页 大图上传文件 */
    upLoadLoading(file) {

    },
    /** 启动页 小图上传文件 */
    upLoadLoadingMin(file) {
    },
    /** 活动页上传文件 */
    upLoadDangling(file) {

    },
    /** 获取配置 */
    async getConfig() {
      const res = await getSystemConfig()
      const { data, head } = res
      if (head.ret === 0 && data) {
        data.result.forEach(item => {
          if (item.type === 5) {
            // 活动页数据
            this.activityForm.pics = []
            const actData = item.value
            this.actParams = item
            this.activityForm.jumpType = actData.jumpType
            this.activityForm.name = actData.name
            this.activityForm.status = actData.status
            this.activityForm.jumpVal = actData.jumpVal
            this.activityForm.version = actData.version
            this.activityForm.paramIos = actData.paramIos
            this.activityForm.paramAndroid = actData.paramAndroid
            this.activityForm.startTime = actData.startTime
            this.activityForm.endTime = actData.endTime
            for (let i = 0; i < actData.pics.length; i++) {
              this.activityForm.pics.push({
                name: uuid(),
                url: actData.pics[i]
              })
            }
          } else if (item.type === 8) {
            // 悬浮页
            this.danglingForm.pics = []
            const dangData = item.value
            this.dangParams = item
            this.danglingForm.jumpType = dangData.jumpType
            this.danglingForm.name = dangData.name
            this.danglingForm.status = dangData.status
            this.danglingForm.jumpVal = dangData.jumpVal
            this.danglingForm.version = dangData.version
            this.danglingForm.id = this.dangParams.id
            this.danglingForm.paramIos = dangData.paramIos
            this.danglingForm.paramAndroid = dangData.paramAndroid
            this.danglingForm.startTime = dangData.startTime
            this.danglingForm.endTime = dangData.endTime
            for (let i = 0; i < dangData.pics.length; i++) {
              this.danglingForm.pics.push({
                name: uuid(),
                url: dangData.pics[i]
              })
            }
          } else if (item.type === 1) {
            // 启动页
            const startData = data.result[6].value
            this.startParams = data.result[6]
            this.startForm = startData
            this.startForm.loadingPic = [{
              name: uuid(),
              url: this.startForm.loadingPic
            }]
            this.startForm.loadingMinPic = [{
              name: uuid(),
              url: this.startForm.loadingMinPic
            }]
          }
        })

        this.initForm = { ...this.activityForm }
        this.initStartForm = { ...this.startForm }
        this.initDanglingForm = { ...this.danglingForm }
      }
    }
  }
}
</script>

<style lang="scss" scoped>

</style>
