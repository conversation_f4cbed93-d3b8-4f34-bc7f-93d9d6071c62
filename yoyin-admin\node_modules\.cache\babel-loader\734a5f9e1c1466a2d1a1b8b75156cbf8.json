{"remainingRequest": "D:\\workspace\\xplife\\yoyin-ui\\yoyin-admin\\node_modules\\thread-loader\\dist\\cjs.js!D:\\workspace\\xplife\\yoyin-ui\\yoyin-admin\\node_modules\\babel-loader\\lib\\index.js!D:\\workspace\\xplife\\yoyin-ui\\yoyin-admin\\src\\utils\\auth.js", "dependencies": [{"path": "D:\\workspace\\xplife\\yoyin-ui\\yoyin-admin\\src\\utils\\auth.js", "mtime": 1751273530208}, {"path": "D:\\workspace\\xplife\\yoyin-ui\\yoyin-admin\\babel.config.js", "mtime": 1744192657864}, {"path": "D:\\workspace\\xplife\\yoyin-ui\\yoyin-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\workspace\\xplife\\yoyin-ui\\yoyin-admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\workspace\\xplife\\yoyin-ui\\yoyin-admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}], "contextDependencies": [], "result": ["import \"core-js/modules/es6.regexp.split\";\n// import Cookies from 'js-cookie'\n\n// const TokenKey = 'vue_admin_template_token'\n\n// export function getToken() {\n//   const res = Cookies.get(TokenKey)\n//   console.log('getToken', res)\n//   return res\n// }\n\n// export function setToken(token) {\n//   const res = Cookies.set(TokenKey, token)\n//   console.log('setToken', res)\n//   return res\n// }\n\n// export function removeToken() {\n//   return Cookies.remove(TokenKey)\n// }\n\nvar TokenKey = 'star_admin_token';\nvar AuthKey = 'star_admin_auth';\nexport function getToken() {\n  return localStorage.getItem(TokenKey);\n}\nexport function setToken(token) {\n  return localStorage.setItem(TokenKey, token);\n}\nexport function removeToken() {\n  return localStorage.removeItem(TokenKey);\n}\nexport function setAuthList(resList) {\n  return localStorage.setItem(Auth<PERSON><PERSON>, resList.join(','));\n}\nexport function getAuthList() {\n  var authStr = localStorage.getItem(AuthKey);\n  if (authStr) {\n    return authStr.split(',');\n  }\n  return [];\n}", null]}