<template>
  <div>
    <div v-if="ques.questionId">
      <slot name="question" :ques="ques"></slot>
    </div>
    <div v-else>
      <slot name="segment" :segment="ques"></slot>

      <div v-if="ques.children && ques.children.length">
        <paper-body-cell
          v-for="(item, index) in ques.children"
          :key="index"
          :ques="item"
          v-on="$listeners"
        >
          <template #question="slotProps">
            <slot name="question" :ques="slotProps.ques"></slot>
          </template>
          <template #segment="slotProps">
            <slot name="segment" :segment="slotProps.segment"></slot>
          </template>
        </paper-body-cell>
      </div>
    </div>
  </div>
</template>

<script>
  export default {
    name: 'paper-body-cell',
    props: {
      ques: Object
    },
    data() {
      return {}
    },
    methods: {}
  }
</script>

<style lang="scss" scoped>
  .paper-segment {
    margin-bottom: 20px;
    width: 874px;
    background: #f6f6f6;
    font-size: 16px;
    font-family: Microsoft YaHei;
    font-weight: 400;
    color: #333333;
    line-height: 42px;
    text-align: center;
  }
  .big-exam-question {
    margin: 20px 0;
    display: flex;
    align-items: flex-end;
    .big-ques-name {
      font-size: 14px;
      font-weight: bold;
      color: #333333;
      line-height: 20px;
      padding: 5px 0;
      max-width: 600px;
    }
    .big-ques-lines {
      flex: 1;
      padding: 0 15px;
      display: flex;
      height: 30px;
      align-items: flex-end;
      .double-triangle {
        width: 30px;
        padding: 9px 0;
        display: inline-block;
        &::before {
          display: inline-block;
          content: '';
          border-width: 5px 5px 5px 6px;
          border-style: solid;
          border-color: transparent transparent transparent #cccccc;
        }
        &::after {
          display: inline-block;
          content: '';
          border-width: 5px 5px 5px 6px;
          border-style: solid;
          border-color: transparent transparent transparent #e7e7e7;
          margin-left: -3px;
        }
      }

      .dotted-line {
        display: inline-block;
        flex: 1;
        height: 15px;
        position: relative;
        border-top: 1px dashed #dadada;
      }
    }
    .big-ques-button {
      width: 130px;
      line-height: 30px;
      border: 1px dashed #619cf5;
      text-align: center;
      color: #619cf5;
      border-radius: 15px;
      font-size: 12px;
      cursor: pointer;
    }
    .big-ques-button:hover {
      border: 1px dashed #3e73cd;
      color: #3e73cd;
    }
  }

  .paper-segment-layered {
    font-size: 16px;
    font-weight: bold;
    color: #333333;
    margin-bottom: 20px;
  }
</style>
