<!--
 * @Description: In User Settings Edit
 * @Author: your name
 * @Date: 2019-10-16 11:28:20
 * @LastEditTime: 2019-10-16 11:38:48
 * @LastEditors: Please set LastEditors
 -->
<template>
  <div class="app-container">
    <div class="table-container">
      <el-table
        ref="multipleTable"
        :data="list"
        tooltip-effect="dark"
        style="width: 100%"
        border
        @selection-change="handleSelectionChange"
      >
        <el-table-column label="封面" width="100">
          <template slot-scope="scope">
            <a :href="scope.row.headUrl" target="_blank">
              <el-avatar shape="square" :size="80" :src="scope.row.headUrl" />
            </a>
          </template>
        </el-table-column>
        <el-table-column label="标题" width="200">
          <template slot-scope="scope">{{ scope.row.title }}</template>
        </el-table-column>
        <el-table-column label="科目" width="80">
          <template slot-scope="scope">{{ scope.row.subject }}</template>
        </el-table-column>
        <el-table-column label="年级" width="80">
          <template slot-scope="scope">{{ scope.row.subject }}</template>
        </el-table-column>
        <el-table-column label="分享次数" width="80">
          <template slot-scope="scope">{{ scope.row.shareNum }}</template>
        </el-table-column>
        <el-table-column label="打印册数" width="80">
          <template slot-scope="scope">{{ scope.row.printNum }}</template>
        </el-table-column>
        <el-table-column label="操作">
          <template slot-scope="scope">
            <el-button size="mini" @click="selectRow(scope.row)">选择</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="pagination-container" style="margin-top:20px;">
      <el-pagination
        :current-page="params.pageno"
        :page-sizes="[10, 20, 30, 40]"
        :page-size="params.pagesize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>
<script>
import { fetchListByPage, deleteRecord } from '@/api/community/knowledge'
export default {
  name: 'KnowLedgeList',
  data() {
    return {
      // 查询参数
      params: {
        startdate: '',
        enddate: '',
        pageno: 1,
        pagesize: 10
      },
      /** 列表 */
      list: [],
      /** 总条数 */
      total: 0,
      /** 消息体 */
      banner: { },
      /** 勾选数据 */
      multipleSelection: [],
      /** 时间段 */
      timeRange: '',
      /** 时间格式 */
      timeFormat: 'yyyy-MM-dd',
      /** 显示编辑框 */
      showDialog: false
    }
  },
  mounted() {
    this.getList()
  },
  methods: {
    selectRow(row) {
      this.$emit('selectKnowLedge', row.id)
    },
    /** 获取列表 */
    async getList() {
      const res = await fetchListByPage(this.params)
      if (res.head.ret === 0) {
        this.list = res.data.result
        this.total = res.data.totalCount
      }
    },
    /** 切换每页条数 */
    handleSizeChange(val) {
      this.params.pagesize = val
      this.getList()
    },
    /** 切换当前页 */
    handleCurrentChange(val) {
      this.params.pageno = val
      this.getList()
    },
    /** 编辑 */
    handleEdit(index, row) {
      this.banner = row
      this.showDialog = true
    },
    /** 删除单行 */
    async handleDeleteRow(index, row) {
      const params = {
        id: row.id
      }
      this.$confirm('此操作将永久删除该记录, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteRecord(params).then(res => {
          if (res.head.ret === 0) {
            this.$message({
              type: 'success',
              message: '删除成功!'
            })
            this.getList()
          }
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
    },
    /** 多选删除 */
    handelMultiDel() {
      this.$confirm('此操作将永久删除选中记录, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const rows = [...this.multipleSelection]
        rows.forEach(item => {
          const params = { id: item.id }
          deleteRecord(params).then(res => {
            if (res.head.ret === 0) {
              this.$message.success('删除成功')
              this.getList()
            }
          })
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
    },
    /** 勾选事件 */
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    /** 清空勾选 */
    handleClearSelection() {
      this.$refs.multipleTable.clearSelection()
    },
    /** 跳转类型 转换 */
    jumpTypeFormat(row, column) {
      const code = parseInt(row.jumpType)
      if (code === 100) {
        return '无跳转'
      } else if (code === 101) {
        return 'H5'
      } else if (code === 102) {
        return '消息中心'
      } else if (code === 103) {
        return '纸条消息'
      } else if (code === 104) {
        return '共享打印'
      }
      return '未知类型'
    },
    /** 栏目类型 转换 */
    columnTypeFormat(row, column) {
      const code = row.column
      if (code === 'home') {
        return '首页'
      } else if (code === 'other') {
        return '其他'
      }
      return '未知类型'
    },
    /** 选择时间段 */
    changeTimeRange(val) {
      this.params.startdate = val[0]
      this.params.enddate = val[1]
    },
    /** 查询 */
    handleQuery() {
      this.getList()
    },
    /** 新增 */
    handleAdd() {
      this.banner = {}
      this.showDialog = true
    },
    /** 关闭弹框 */
    closeDialog(code) {
      this.showDialog = false
      this.banner = {}
      // 保存code
      if (code === '1') {
        this.getList()
      }
    }

  }
}
</script>

<style lang="scss" scoped>

</style>
