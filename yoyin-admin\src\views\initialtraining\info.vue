<!--
 * @Description: In User Settings Edit
 * @Author: your name
 * @Date: 2019-10-16 11:28:20
 * @LastEditTime: 2019-10-16 11:38:48
 * @LastEditors: Please set LastEditors
 -->
<template>
  <div class="app-container">
    <div class="table-query-container">
      <el-radio-group v-model="mainDictId">
        <el-radio-button :label="item.value" v-for="item in mainDictList" :key="item.id">{{item.label}}</el-radio-button>
      </el-radio-group>
      <p />
      <el-radio-group v-model="params.type" @change="getList">
        <el-radio-button label="" >全部</el-radio-button>
        <el-radio-button :label="item.value" v-for="item in subDictList" :key="item.id">{{item.label}}</el-radio-button>
      </el-radio-group>
      <p />
      <el-button type="success" @click="handleAdd(null)">新增</el-button>
    </div>
    <div class="table-container">
      <el-table
        ref="multipleTable"
        :data="list"
        tooltip-effect="dark"
        style="width: 100%"
        border
        max-height="700px"
      >
        <el-table-column label="排序" prop="sortNum" header-align="center" align="center" />
        <el-table-column label="标题" prop="name" header-align="center" align="center" />
        <el-table-column label="类型" prop="type" header-align="center" align="center" :formatter="formattingType"/>
        <el-table-column label="资源图" prop="resUrl" header-align="center" align="center">
          <template slot-scope="scope">
            <div v-if="scope.row.resUrl">
              <img :src="scope.row.resUrl" style="width: 80px; height:80px;">
            </div>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="245" header-align="center" align="center">
          <template slot-scope="scope">
            <el-button size="mini" type="error" @click="handleEdit(scope.row)">编辑</el-button>
            <!-- <el-button size="mini" type="error" @click="handleAdd(scope.row)">新增同类</el-button> -->
            <el-button size="mini" type="error" @click="deleteSubject(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="pagination-container" style="margin-top:20px;">
      <el-pagination
        :current-page="params.pageNo"
        :page-sizes="[10, 20, 30, 40]"
        :page-size="params.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
    <el-dialog
      :visible.sync="showDialog"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      width="60%"
      title="编辑"
    >
      <el-form ref="form" :model="form" label-width="100px" class="edit-form">
        <el-form-item label="标题" prop="name">
          <el-input v-model="form.name" />
        </el-form-item>
        <el-form-item label="类别" prop="type">
          这里用两个联动选择固定
          <el-input v-model="form.type" />
        </el-form-item>
        <el-form-item label="资源图" prop="resUrl">
          <el-upload
            class="avatar-uploader"
            action=""
            :show-file-list="false"
            :on-change="changePic"
            :before-upload="beforeUpload"
            accept="image/png, image/jpeg"
          >
            <img v-if="form.resUrl" :src="form.resUrl" class="avatar">
            <i v-else class="el-icon-plus avatar-uploader-icon" />
          </el-upload>
          <img v-show="false" :src="testUrl" class="avatar">
        </el-form-item>
        <el-form-item label="排序" prop="sortNum">
          <el-input
            v-model="form.sortNum"
            type="text"
          />
        </el-form-item>
        <el-form-item>
          <el-button @click="handleClose">返回</el-button>
          <el-button v-permission="'btn-xeasylabel-dict-edit'" type="primary" @click="submit">保存</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>
<script>
import { fetchListByPage, deleteRecord, saveRecord } from '@/api/initialtrain/info'
import { fetchListByPage as fetchDictPage } from '@/api/initialtrain/dict'
import AliyunOSS from '@/utils/aliyunOSS'
const oss = new AliyunOSS()
oss.clearStorage()
export default {
  data() {
    return {
      mainDictId: "",
      // 查询参数
      params: {
        pageNo: 1,
        pageSize: 10,
        type: ''
      },
      distinctTypeList: [],
      cacheKey: '',
      /** 列表 */
      list: [],
      /** 总条数 */
      total: 0,
      downloadUrl: '',
      /** 时间段 */
      timeRange: '',
      timeRange2: '',
      keyword: '',
      form: {
        name: '',
        type: '',
        label: '',
        value: '',
        localeCode: '',
        icon: '',
        sortNum: 0,
        forbid: 0,
        remark: ''
      },
      dictList: [],
      /** 时间格式 */
      timeFormat: 'yyyy-MM-dd',
      showDialog: false,
      testUrl: '',
      /** 待上传的文件 */
      upFile: undefined
    }
  },
  watch: {
    mainDictList(newVal, oldVal) {
      if (newVal.length > 0 && this.mainDictId===''){
        this.mainDictId = newVal[0].value
      }
    },
  },
  computed: {
    mainDictList() {
      if (this.dictList && this.dictList.length > 0) {
        // 从dictList中返回type="main_type"的数据
        let tmpList = this.dictList.filter(item => {
          return item.type === 'main_type'
        })
        return tmpList;
      } else {
        return []
      }
    },
    subDictList() {
      if (this.mainDictId) {
        let tmpList = this.dictList.filter(item => {
          return item.type === this.mainDictId
        })
        return tmpList;
      } else {
        return []
      }
    }
  },
  mounted() {
    this.getList()
    this.fetchMainDict()
  },
  methods: {
    formattingType(row, column) {
      let name = ''
      this.dictList.forEach(item => {
        if (item.value === row.type) {
          name = item.name
        }
      })
      return name
    },
    fetchMainDict() {
      const params = {
        pageSize: 999
      }
      fetchDictPage(params).then(res => {
        if (res.head.ret === 0) {
          this.dictList = res.data.result
          console.log(this.dictList)
        }
      })
    },
    handleChangeGrade(item) {
      this.form.gradeName = item.name
    },
    handleChangeSubject(item) {
      this.form.subjectName = item.name
    },
    async getList() {
      const res = await fetchListByPage(this.params)
      if (res.head.ret === 0) {
        this.list = res.data.result
        this.total = res.data.totalCount
      }
    },
    /** 切换每页条数 */
    handleSizeChange(val) {
      this.params.pageSize = val
      this.getList()
    },
    /** 切换当前页 */
    handleCurrentChange(val) {
      this.params.pageNo = val
      this.getList()
    },
    /** 查询 */
    handleQuery() {
      this.params.pageNo = 1
      this.params.pageSize = 10
      this.getList()
    },
    /** 查看动态 */
    handleEdit(row) {
      this.form = row
      this.showDialog = true
    },
    deleteSubject(row) {
      const parms = {
        id: row.id
      }
      this.$confirm('此操作将永久删除，不可恢复, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteRecord(parms).then(res => {
          if (res.head.ret === 0) {
            this.$message.success('删除成功')
            this.getList()
          }
        })
      }).catch(() => {
      })
    },
    /** 提交 */
    submit() {
      if (!this.form.name) {
        this.$message.error('请输入标题')
        return
      }
      if (!this.form.type) {
        this.$message.error('请选择类型')
        return
      }
      if (!this.form.resUrl) {
        this.$message.error('请上传资源图')
        return
      }

      if (this.upFile) {
        oss.uploadFile(this.upFile, (result, error) => {
          if (error) {
            this.$message.error('上传文件失败')
            return -1
          }
          if (result) {
            console.log('上传完图片后返回的数据', result)
            this.form.resUrl = result.name
            this.handleSave(this.form)
          }
        })
      } else {
        // 直接保存
        this.handleSave(this.form)
      }
    },
    handleSave(parms) {
      saveRecord(parms).then(res => {
        if (res.head.ret === 0) {
          this.$message.success('保存成功')
        }
        this.getList()
        this.showDialog = false
      })
    },
    handleAdd(row) {
      if (row) {
        this.form = {
          sortNum: 0,
          showFlag: 1,
          id: '',
        }
      } else {
        this.form = {
          name: '',
          type: '',
          label: '',
          value: '',
          localeCode: '',
          icon: '',
          sortNum: 0,
          id: '',
        }
      }
      this.showDialog = true
    },
    /** 选择图片*/
    changePic(file) {
      this.upFile = file.raw
      this.form.resUrl = URL.createObjectURL(file.raw)
      this.testUrl = URL.createObjectURL(file.raw)
      return false
    },
    /** 上传前 */
    beforeUpload(file) {
      return false
    },
    /** 返回 */
    handleClose(code) {
      this.showDialog = false
    }
  }
}
</script>

<style lang="scss" scoped>

</style>
