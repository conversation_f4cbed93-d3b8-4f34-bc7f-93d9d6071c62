<template>
  <div class="app-container">
    <div class="table-container">
      <el-row>
        <el-col :span="4">
          <el-button
            v-permission="'btn-menu2cunkuan-englishword-edit'"
            size="small"
            type="primary"
            @click="handleCreateCourse"
            >创建课程</el-button
          >
        </el-col>
        <el-col :span="4" v-if="shouldShowImportButton">
          <el-button
            v-permission="'btn-menu2cunkuan-englishword-edit'"
            size="small"
            type="primary"
            @click="importDialogVisible = true"
            >导入JSON</el-button
          >
          <el-tooltip
            class="item"
            effect="dark"
            placement="top"
            :content="jsonExample"
          >
            <i
              class="el-icon-question"
              style="margin-left: 5px; cursor: pointer"
            ></i>
          </el-tooltip>
        </el-col>
        <el-col :span="4" v-if="clickCourseId">
          <el-button
            v-permission="'btn-menu2cunkuan-englishword-edit'"
            size="small"
            type="primary"
            @click="handleExportJson"
            >导出JSON</el-button
          >
        </el-col>
      </el-row>
    </div>
    <div class="table-container">
      <el-tabs @tab-click="handleTabClick" v-model="activeTab">
        <el-tab-pane
          v-for="(item, index) in courseList"
          :key="item.id"
          :label="item.name"
          :lazy="true"
        >
          <template slot="label">
            <span @contextmenu.prevent="handleContextMenu($event, item)">{{
              item.name
            }}</span>
          </template>
          <el-table
            :data="wordsList"
            tooltip-effect="dark"
            style="width: 100%"
            border
            max-height="700px"
            @selection-change="handleSelectionChange"
            ref="multipleTable"
          >
            <el-table-column type="selection" width="55"> </el-table-column>
            <el-table-column label="单词" prop="word" width="120" />
            <el-table-column label="音标" width="150">
              <template slot-scope="scope">
                <div class="phonetic-container">
                  <span>{{ scope.row.phonetic }}</span>
                  <i
                    v-if="scope.row.pronunciation"
                    class="el-icon-video-play"
                    @click="playAudio(scope.row.pronunciation)"
                  ></i>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="释义">
              <template slot-scope="scope">
                <div v-for="(def, index) in scope.row.definitions" :key="index">
                  {{ def }}
                </div>
              </template>
            </el-table-column>
            <el-table-column label="例句" width="120">
              <template slot-scope="scope">
                <el-button type="text" @click="handleExpandRow(scope.row)">
                  {{ scope.row.examples.length }}个例句
                </el-button>
              </template>
            </el-table-column>
            <el-table-column label="操作">
              <template slot-scope="scope">
                <a
                  v-permission="'btn-menu2cunkuan-englishword-edit'"
                  style="
                    color: #1890ff;
                    text-decoration: none;
                    cursor: pointer;
                    transition: color 0.3s;
                  "
                  @click="handleEditWord(scope.row)"
                  >修改</a
                >
                <a
                  style="
                    color: #1890ff;
                    text-decoration: none;
                    cursor: pointer;
                    transition: color 0.3s;
                  "
                  @click="handleViewDetails(scope.row)"
                  >查看</a
                >
                <a
                  v-permission="'btn-menu2cunkuan-englishword-edit'"
                  style="
                    color: #f56c6c;
                    text-decoration: none;
                    cursor: pointer;
                    transition: color 0.3s;
                    margin-left: 10px;
                  "
                  @click="handleDeleteWord(scope.row)"
                  >删除</a
                >
              </template>
            </el-table-column>
          </el-table>
          <div class="pagination-container" style="margin-top: 20px">
            <el-pagination
              :current-page="params.pageNo"
              :page-sizes="[10, 20, 30, 40]"
              :page-size="params.pageSize"
              layout="total, sizes, prev, pager, next, jumper"
              :total="total"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
            />
          </div>
          <el-dialog
            title="例句详情"
            :visible.sync="exampleDialogVisible"
            width="50%"
            :modal-append-to-body="false"
            :append-to-body="true"
          >
            <div v-if="currentRow">
              <div
                v-for="(example, index) in currentRow.examples"
                :key="index"
                class="example-item"
              >
                <p class="sentence">{{ example.sentence }}</p>
                <p class="translation">{{ example.translation }}</p>
                <el-button
                  type="text"
                  @click="playAudio(example.audio)"
                  v-if="example.audio"
                >
                  播放
                </el-button>
                <el-divider
                  v-if="index < currentRow.examples.length - 1"
                ></el-divider>
              </div>
            </div>
          </el-dialog>
        </el-tab-pane>
      </el-tabs>
    </div>

    <!-- 创建课程对话框 -->
    <el-dialog
      title="创建课程"
      :visible.sync="createCourseDialog"
      width="30%"
      :modal-append-to-body="false"
      :append-to-body="true"
    >
      <el-form :model="courseForm" ref="courseForm" :rules="courseRules">
        <el-form-item label="课程名称" prop="name">
          <el-input
            v-model="courseForm.name"
            placeholder="请输入课程名称"
          ></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="createCourseDialog = false">取 消</el-button>
        <el-button type="primary" @click="submitCreateCourse">确 定</el-button>
      </span>
    </el-dialog>

    <!-- 右键菜单 -->
    <div
      v-show="contextMenuVisible"
      :style="contextMenuStyle"
      class="context-menu"
    >
      <div class="menu-item" @click="handleEditCourseName">修改课程名称</div>
      <div class="menu-item" @click="handleDeleteCourse">删除课程</div>
      <div class="menu-item" @click="handleClearWords">清空单词</div>
    </div>

    <!-- 修改课程名称对话框 -->
    <el-dialog
      title="修改课程名称"
      :visible.sync="editCourseDialog"
      width="30%"
      :modal-append-to-body="false"
      :append-to-body="true"
    >
      <el-form
        :model="editCourseForm"
        ref="editCourseForm"
        :rules="courseRules"
      >
        <el-form-item label="课程名称" prop="name">
          <el-input
            v-model="editCourseForm.name"
            placeholder="请输入课程名称"
          ></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="editCourseDialog = false">取 消</el-button>
        <el-button type="primary" @click="submitEditCourse">确 定</el-button>
      </span>
    </el-dialog>

    <!-- 修改单词对话框 -->
    <el-dialog
      title="修改单词"
      :visible.sync="editWordDialog"
      width="50%"
      :modal-append-to-body="false"
      :append-to-body="true"
    >
      <el-form
        :model="editWordForm"
        ref="editWordForm"
        :rules="wordRules"
        label-width="80px"
      >
        <el-form-item label="单词" prop="word">
          <el-input v-model="editWordForm.word"></el-input>
        </el-form-item>
        <el-form-item label="音标" prop="phonetic">
          <el-input v-model="editWordForm.phonetic"></el-input>
        </el-form-item>
        <el-form-item label="发音">
          <el-input v-model="editWordForm.pronunciation"></el-input>
        </el-form-item>
        <el-form-item label="释义">
          <el-input
            type="textarea"
            v-model="editWordForm.definitions"
            :rows="3"
            placeholder="每行一个释义"
          ></el-input>
        </el-form-item>
        <el-form-item label="例句">
          <div
            v-for="(example, index) in editWordForm.examples"
            :key="index"
            class="example-item"
          >
            <el-input
              v-model="example.sentence"
              placeholder="英文例句"
            ></el-input>
            <el-input
              v-model="example.translation"
              placeholder="中文翻译"
              style="margin-top: 8px"
            ></el-input>
            <el-input
              v-model="example.audio"
              placeholder="音频URL"
              style="margin-top: 8px"
            ></el-input>
            <el-button
              type="text"
              @click="removeExample(index)"
              style="margin-top: 8px"
              >删除例句</el-button
            >
          </div>
          <el-button type="text" @click="addExample">添加例句</el-button>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="editWordDialog = false">取 消</el-button>
        <el-button type="primary" @click="submitEditWord">确 定</el-button>
      </span>
    </el-dialog>

    <!-- 查看单词详情对话框 -->
    <el-dialog
      title="单词详情"
      :visible.sync="viewDetailsDialog"
      width="50%"
      :modal-append-to-body="false"
      :append-to-body="true"
    >
      <div v-if="currentWord" class="word-details">
        <div class="detail-item">
          <span class="label">单词：</span>
          <span class="value">{{ currentWord.word }}</span>
        </div>
        <div class="detail-item">
          <span class="label">音标：</span>
          <span class="value">{{ currentWord.phonetic }}</span>
          <el-button
            v-if="currentWord.pronunciation"
            type="text"
            size="mini"
            @click="playAudio(currentWord.pronunciation)"
          >
            播放
          </el-button>
        </div>
        <div class="detail-item">
          <span class="label">释义：</span>
          <div class="value">
            <p v-for="(def, index) in currentWord.definitions" :key="index">
              {{ def }}
            </p>
          </div>
        </div>
        <div class="detail-item">
          <span class="label">例句：</span>
          <div class="value">
            <div
              v-for="(example, index) in currentWord.examples"
              :key="index"
              class="example-item"
            >
              <p class="sentence">{{ example.sentence }}</p>
              <p class="translation">{{ example.translation }}</p>
              <el-button
                v-if="example.audio"
                type="text"
                size="mini"
                @click="playAudio(example.audio)"
              >
                播放
              </el-button>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>

    <!-- 批量导入JSON弹窗 -->
    <el-dialog
      title="批量导入JSON"
      :visible.sync="importDialogVisible"
      width="600px"
      :modal-append-to-body="false"
      :append-to-body="true"
    >
      <el-upload
        ref="importUpload"
        class="upload-demo full-width-upload"
        action
        :auto-upload="false"
        :on-change="handleImportFileChange"
        :file-list="importFileList"
        accept=".json"
        multiple
        drag
        style="width: 100%"
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">
          将文件拖到此处，或 <em>点击选择文件</em>
        </div>
        <div slot="tip" class="el-upload__tip">只能上传JSON文件，可多选</div>
      </el-upload>
      <div style="margin-top: 20px; text-align: right">
        <el-button @click="importDialogVisible = false">取 消</el-button>
        <el-button
          type="primary"
          @click="handleImportFiles"
          :disabled="importFileList.length === 0"
          >确 定</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { fetchWordList, fetchCourseList, importWordsJson, createCourse, updateCourse, deleteRecord, saveWord, deleteWordsByCourseId, batchDeleteWords } from '@/api/words/list'
import AliyunOSS from '@/utils/aliyunOSS'
import request from '@/utils/request'
const oss = new AliyunOSS()
export default {
  props: {
    courseId: {
      type: String,
      default: '',
      required: true
    }
  },
  data() {
    return {
      // 查询参数
      params: {
        startDate: '',
        endDate: '',
        pageNo: 1,
        pageSize: 10,
        sort: 'desc',
        sortType: 'time'
      },
      total: 100,
      tableList: [],
      multipleSelection: [],
      // 弹框
      showDialog: false,
      // 批量
      showMuliDialog: false,
      // 仅上传文件模式
      onlyUpload: false,
      // 表单
      form: {
        id: '',
        channel: '',
        url: '',
        param: '',
        version: '',
        remark: ''
      },
      // 表单
      multiForm: {
        version: '',
        remark: ''
      },
      loading: false,
      showUrl: false,
      typeMap: {},
      courseMap: {},
      courseList: [],
      wordsList: [],
      clickCourseId: '',
      typeId: '',
      createCourseDialog: false,
      courseForm: {
        name: ''
      },
      courseRules: {
        name: [
          { required: true, message: '请输入课程名称', trigger: 'blur' }
        ]
      },
      editCourseDialog: false,
      editCourseForm: {
        id: '',
        name: ''
      },
      contextMenuVisible: false,
      contextMenuStyle: {
        left: '0px',
        top: '0px'
      },
      activeTab: '0',
      jsonExample: `[
  {
    "word": "example",
    "phonetic": "/ɪɡˈzæmpəl/",
    "pronunciation": "音频URL",
    "definitions": ["定义1", "定义2"],
    "examples": [
      {
        "sentence": "This is an example sentence.",
        "translation": "这是一个示例句子。",
        "audio": "音频URL"
      }
    ]
  }
]`,
      exampleDialogVisible: false,
      currentRow: null,
      editWordDialog: false,
      editWordForm: {
        id: '',
        word: '',
        phonetic: '',
        pronunciation: '',
        definitions: [],
        examples: []
      },
      wordRules: {
        word: [
          { required: true, message: '请输入单词', trigger: 'blur' }
        ],
        phonetic: [
          { required: true, message: '请输入音标', trigger: 'blur' }
        ]
      },
      viewDetailsDialog: false,
      currentWord: null,
      selectedWords: [],
      importDialogVisible: false,
      importFileList: []
    }
  },
  computed: {
    shouldShowImportButton() {
      if (!this.clickCourseId) return false
      const currentCourse = this.courseList.find(course => course.id === this.clickCourseId)
      return currentCourse
    }
  },
  watch: {
    courseId(oldValue, newValue) {
      if (newValue) {
        this.getList()
      }
    }
  },
  mounted() {
    this.getList()
    // 添加点击事件监听器来关闭右键菜单
    document.addEventListener('click', this.closeContextMenu)
  },
  beforeDestroy() {
    // 移除事件监听器
    document.removeEventListener('click', this.closeContextMenu)
  },
  methods: {
    handleTabClick(tab) {
      this.clickCourseId = this.courseList[tab.index].id
      this.activeTab = tab.index.toString()
      this.params.pageNo = 1
      this.loadWordList()
    },
    /** 查询 */
    queryVersion() {
      this.loadWordList()
    },
    /** 切换每页条数 */
    handleSizeChange(val) {
      console.log(`每页 ${val} 条`)
      this.params.pageSize = val
      this.loadWordList()
    },
    /** 切换当前页 */
    handleCurrentChange(val) {
      console.log(`当前页: ${val}`)
      this.params.pageNo = val
      this.loadWordList()
    },
    /** 获取表数据 */
    async getList() {
      if (!this.courseId) {
        return
      }
      const params = {
        id: this.courseId
      }
      const res = await fetchCourseList(params)
      console.log('getList,res', res.head.ret, res)
      if (res.head.ret === 0) {
        this.courseList = res.data
        this.clickCourseId = res.data[0].id
        this.activeTab = '0'
        this.loadWordList()
      }
    },
    clearSelection() {
      this.$refs.multipleTable.clearSelection()
    },
    /** 勾选事件 */
    handleSelectionChange(val) {
      this.selectedWords = val
    },
    /** 编辑 */
    handleEdit(index, row) {
      this.form = row
      this.showDialog = true
      this.showUrl = true
      this.onlyUpload = false
    },
    /** 编辑 仅上传文件 */
    handleOnlyFile(index, row) {
      this.form = row
      this.showDialog = true
      this.showUrl = true
      this.onlyUpload = true
    },
    async loadWordList() {
      const params = {
        pageno: this.params.pageNo,
        pagesize: this.params.pageSize,
        courseId: this.clickCourseId
      }
      const res = await fetchWordList(params)
      if (res.head.ret === 0) {
        this.wordsList = res.data.result
        this.total = res.data.totalCount
      }
    },

    // 表单编辑
    /** 保存 */
    submitForm() {
      this.$refs['ruleForm'].validate(valid => {
        if (valid) {
          this.loading = true
          console.log(this.file)
          if (!this.file) {
            this.saveConfig()
            return
          }
          oss.uploadFile(this.file, (result, error) => {
            if (error) {
              console.log('error')
              console.log(error)
              this.$message.error('上传文件失败')
              this.loading = false
              return
            }
            if (result) {
              this.form.url = result.url
              this.saveConfig()
              this.getList()
            }
          })
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    handleImportFileChange(file, fileList) {
      this.importFileList = fileList
    },
    async handleImportFiles() {
      if (!this.clickCourseId) {
        this.$message.warning('请先选择课程')
        this.importFileList = []
        return
      }
      const readFileAsText = file =>
        new Promise((resolve, reject) => {
          const reader = new FileReader()
          reader.onload = e => resolve(e.target.result)
          reader.onerror = reject
          reader.readAsText(file.raw)
        })
      try {
        const importFiles = []
        for (const fileItem of this.importFileList) {
          const content = await readFileAsText(fileItem)
          JSON.parse(content)
          importFiles.push(content)
        }
        const params = {
          courseId: this.clickCourseId,
          importFiles: JSON.stringify(importFiles),
          userid: this.userid // 如果有userid
        }
        const res = await importWordsJson(params)
        if (res.head.ret === 0) {
          this.$message.success('批量导入成功')
          this.importFileList = []
          this.importDialogVisible = false
          await this.loadWordList()
        } else {
          this.$message.error('导入失败：' + (res.head.msg || '未知错误'))
          this.importFileList = []
        }
      } catch (error) {
        this.$message.error('导入失败：' + error.message)
        this.importFileList = []
      }
    },
    handleCreateCourse() {
      this.createCourseDialog = true
    },
    async submitCreateCourse() {
      this.$refs['courseForm'].validate(async valid => {
        if (valid) {
          try {
            // 使用传入的courseId作为pId
            console.log('创建课程请求参数：', {
              name: this.courseForm.name,
              pId: this.courseId
            })
            const res = await createCourse({
              name: this.courseForm.name,
              pId: this.courseId
            })
            if (res.head.ret === 0) {
              this.$message.success('创建课程成功')
              this.createCourseDialog = false
              this.courseForm.name = ''
              // 刷新课程列表
              await this.getList()
            }
          } catch (error) {
            this.$message.error('创建课程失败')
          }
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    playAudio(audioUrl) {
      if (audioUrl) {
        const audio = new Audio(audioUrl)
        audio.play()
      }
    },
    handleEditWord(row) {
      this.editWordForm = {
        id: row.id,
        word: row.word,
        phonetic: row.phonetic,
        pronunciation: row.pronunciation,
        definitions: row.definitions,
        examples: row.examples
      }
      this.editWordDialog = true
    },
    handleContextMenu(event, item) {
      event.stopPropagation() // 阻止事件冒泡
      this.contextMenuVisible = true
      this.contextMenuStyle.left = event.clientX + 'px'
      this.contextMenuStyle.top = event.clientY + 'px'
      this.editCourseForm = {
        id: item.id,
        name: item.name
      }
    },
    closeContextMenu() {
      this.contextMenuVisible = false
    },
    handleEditCourseName() {
      this.editCourseDialog = true
      this.contextMenuVisible = false
    },
    async submitEditCourse() {
      this.$refs['editCourseForm'].validate(async valid => {
        if (valid) {
          try {
            const res = await updateCourse(this.editCourseForm)
            if (res.head.ret === 0) {
              this.$message.success('修改课程名称成功')
              this.editCourseDialog = false
              // 刷新课程列表
              await this.getList()
            }
          } catch (error) {
            this.$message.error('修改课程名称失败')
          }
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    handleDeleteCourse() {
      this.$confirm('确认删除该课程吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          const res = await deleteRecord({ id: this.editCourseForm.id })
          if (res.head.ret === 0) {
            this.$message.success('删除成功')
            this.contextMenuVisible = false
            // 刷新课程列表
            await this.getList()
          }
        } catch (error) {
          this.$message.error('删除失败')
        }
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
    },
    handleClearWords() {
      this.$confirm('确认清空该课程的所有单词吗？此操作不可恢复！', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          const res = await deleteWordsByCourseId({
            courseId: this.editCourseForm.id
          })
          if (res.head.ret === 0) {
            this.$message.success('清空单词成功')
            this.contextMenuVisible = false
            // 刷新单词列表
            await this.loadWordList()
          } else {
            this.$message.error('清空单词失败：' + (res.head.msg || '未知错误'))
          }
        } catch (error) {
          this.$message.error('清空单词失败：' + error.message)
        }
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消清空'
        })
      })
    },
    handleExpandRow(row) {
      this.currentRow = row
      this.exampleDialogVisible = true
    },
    async submitEditWord() {
      this.$refs['editWordForm'].validate(async valid => {
        if (valid) {
          try {
            const params = {
              ...this.editWordForm
            }

            // 确保音标格式正确
            if (params.phonetic && !params.phonetic.startsWith('[')) {
              params.phonetic = '[' + params.phonetic
            }
            if (params.phonetic && !params.phonetic.endsWith(']')) {
              params.phonetic = params.phonetic + ']'
            }

            // 处理数组类型的字段
            if (params.definitions && params.definitions.length > 0) {
              params.definitions = JSON.stringify(params.definitions)
            }
            if (params.examples && params.examples.length > 0) {
              params.examples = JSON.stringify(params.examples)
            }

            console.log('提交的参数：', params)
            const res = await saveWord(params)
            if (res.head.ret === 0) {
              this.$message.success('修改单词成功')
              this.editWordDialog = false
              // 刷新单词列表
              await this.loadWordList()
            }
          } catch (error) {
            console.error('保存失败：', error)
            this.$message.error('修改单词失败：' + (error.message || '未知错误'))
          }
        }
      })
    },
    addExample() {
      this.editWordForm.examples.push({
        sentence: '',
        translation: '',
        audio: ''
      })
    },
    removeExample(index) {
      this.editWordForm.examples.splice(index, 1)
    },
    handleViewDetails(row) {
      this.currentWord = row
      this.viewDetailsDialog = true
    },
    async handleExportJson() {
      if (!this.clickCourseId) {
        this.$message.warning('请先选择课程')
        return
      }

      try {
        // 获取当前课程的所有单词数据
        const params = {
          pageno: 1,
          pagesize: 9999, // 设置一个较大的数值以获取所有数据
          courseId: this.clickCourseId
        }
        const res = await fetchWordList(params)
        if (res.head.ret === 0) {
          const wordsList = res.data.result

          // 获取当前课程信息
          const currentCourse = this.courseList.find(course => course.id === this.clickCourseId)
          const fileName = currentCourse ?
            `${currentCourse.typeName || '未知类型'}_${currentCourse.subName || '未知子类'}_${currentCourse.name || '未知课程'}_${new Date().getTime()}.json` :
            `words_${this.clickCourseId}_${new Date().getTime()}.json`

          // 格式化数据
          const exportData = wordsList.map(word => ({
            word: word.word,
            phonetic: word.phonetic,
            pronunciation: word.pronunciation,
            definitions: word.definitions,
            examples: word.examples
          }))

          // 创建Blob对象
          const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' })

          // 创建下载链接
          const url = window.URL.createObjectURL(blob)
          const link = document.createElement('a')
          link.href = url
          link.download = fileName

          // 触发下载
          document.body.appendChild(link)
          link.click()

          // 清理
          document.body.removeChild(link)
          window.URL.revokeObjectURL(url)

          this.$message.success('导出成功')
        } else {
          this.$message.error('导出失败：' + (res.head.msg || '未知错误'))
        }
      } catch (error) {
        this.$message.error('导出失败：' + error.message)
      }
    },
    handleDeleteWord(row) {
      this.$confirm('确认删除该单词吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          const res = await batchDeleteWords({
            wordIds: JSON.stringify([row.id])
          })

          if (res.head.ret === 0) {
            this.$message.success('删除成功')
            // 刷新列表
            await this.loadWordList()
          } else {
            this.$message.error('删除失败：' + (res.head.msg || '未知错误'))
          }
        } catch (error) {
          this.$message.error('删除失败：' + error.message)
        }
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.app-container {
  .table-container {
    padding-bottom: 20px;
  }
  .control-container {
    margin-bottom: 20px;
  }
  .choose-container {
    margin-bottom: 20px;
    background-color: #e6f7ff;
    border: 1px solid #91d5ff;

    span {
      margin-left: 20px;
      line-height: 48px;
    }
    a {
      margin-left: 20px;
      color: #1890ff;
      text-decoration: none;
      background-color: transparent;
      outline: none;
      cursor: pointer;
      transition: color 0.3s;
    }
  }
}
.context-menu {
  position: fixed;
  background: white;
  border: 1px solid #ccc;
  border-radius: 4px;
  padding: 5px 0;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  z-index: 3000;

  .menu-item {
    padding: 8px 20px;
    cursor: pointer;

    &:hover {
      background-color: #f5f7fa;
    }
  }
}
.example-item {
  margin-bottom: 15px;
  .sentence {
    font-size: 16px;
    color: #333;
    margin-bottom: 8px;
  }
  .translation {
    font-size: 14px;
    color: #666;
    margin-bottom: 8px;
  }
}
.phonetic-container {
  display: flex;
  align-items: center;
  gap: 4px;

  .el-icon-video-play {
    color: #409eff;
    cursor: pointer;
    font-size: 14px;

    &:hover {
      color: #66b1ff;
    }
  }
}
.word-details {
  .detail-item {
    margin-bottom: 20px;

    .label {
      font-weight: bold;
      color: #606266;
      margin-right: 10px;
    }

    .value {
      color: #333;

      p {
        margin: 5px 0;
      }
    }
  }

  .example-item {
    margin-bottom: 15px;
    padding-bottom: 15px;
    border-bottom: 1px solid #ebeef5;

    &:last-child {
      border-bottom: none;
    }

    .sentence {
      font-size: 16px;
      color: #333;
      margin-bottom: 8px;
    }

    .translation {
      font-size: 14px;
      color: #666;
      margin-bottom: 8px;
    }
  }
}
::v-deep .full-width-upload .el-upload {
  width: 100% !important;
}
::v-deep .full-width-upload .el-upload-dragger {
  width: 100% !important;
  min-width: 100% !important;
  max-width: 100% !important;
  min-height: 160px;
  box-sizing: border-box;
}
</style>
