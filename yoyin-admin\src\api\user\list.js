import request from '@/utils/request'

const getListUrl = '/platform/gam/community/v1/user/finduserinfopage'
const getExportListUrl = '/platform/gam/community/v1/user/exportUserInfoList'
const saveUrl = '/platform/gam/community/v1/user/updateusertitletype'
const getRandomUserUrl = '/platform/gam/community/v1/user/randomselect'
const saveInfoUrl = '/platform/gam/community/v1/user/updateuserinfo'
const findUserInfoByKeywordUrl = '/platform/gam/community/v1/user/findUserInfoByKeyword'
const findUserInfoByKeywordUrl2 = '/platform/gam/community/v1/user/findUserInfoByKeyword2'

export const fetchListByPage = async params => {
  return request({
    url: getListUrl,
    method: 'get',
    params
  })
}

export const saveUser = async params => {
  return request({
    url: saveUrl,
    method: 'post',
    data: params
  })
}

export const saveUserInfo = async params => {
  return request({
    url: saveInfoUrl,
    method: 'post',
    data: params
  })
}

export const getRandomUser = async params => {
  return request({
    url: getRandomUserUrl,
    method: 'get',
    params
  })
}

export const fetchUserListByPage = async params => {
  return request({
    url: getListUrl,
    method: 'get',
    params
  })
}

export const exportUserList = async params => {
  return request({
    url: getExportListUrl,
    method: 'get',
    params
  })
}

export const findUserInfoByKeyword = async params => {
  return request({
    url: findUserInfoByKeywordUrl,
    method: 'post',
    data: params
  })
}

export const findUserInfoByKeyword2 = async params => {
  return request({
    url: findUserInfoByKeywordUrl2,
    method: 'post',
    data: params
  })
}

export const getDefaultManagerUserInfo = async params => {
  return request({
    url: '/platform/gam/community/v1/user/getDefaultManagerUserInfo',
    method: 'get',
    params
  })
}

export const settingDefaultManagerUserInfo = async params => {
  return request({
    url: '/platform/gam/community/v1/user/settingDefaultManagerUserInfo',
    method: 'get',
    params
  })
}

export const bindingDefaultManagerUserId = async params => {
  return request({
    url: '/platform/gam/community/v1/user/bindingDefaultManagerUserId',
    method: 'get',
    params
  })
}
