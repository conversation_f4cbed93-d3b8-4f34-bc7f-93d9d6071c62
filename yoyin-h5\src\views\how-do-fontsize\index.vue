<template>
  <div class="content">
    <img src="https://m.yoyin.net/h5/img/howdofontsize/01.png">
    <img src="https://m.yoyin.net/h5/img/howdofontsize/02.png">
    <img src="https://m.yoyin.net/h5/img/howdofontsize/03.png">
    <img src="https://m.yoyin.net/h5/img/howdofontsize/04.png">
  </div>
</template>

<script>
export default {
  data () {
    return {
       moduleKey: 'faq'
    }
  },
  async mounted () { },
  methods: {},
  components: {}
}
</script>

<style lang="scss">
// .content {
//   margin: 2em;
// }
img {
  width: 100%;
  object-fit: contain;
}
</style>
