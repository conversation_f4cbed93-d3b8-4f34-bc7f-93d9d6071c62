<template>
  <div class="faq">
    <div class="faq-title2">
      {{$t(`faq-p13.q`)}}
    </div>
    <div class="faq-content2">
      <p class="first-line">{{$t(`faq-p13.a1`)}}</p>
    </div>

  </div>
</template>

<script>
export default {
  data () {
    return {
      moduleKey: 'faq'
    }
  },
  async mounted () { },
  methods: {},
  components: {}
}
</script>

<style lang="scss">
@import '../../assets/scss/faq.scss'; // 全局faq自定义的css样式
</style>
