<template>
  <div v-if="visiable" class="edit-dialog">
    <el-dialog
      ref="dialog"
      :show-close="false"
      :title="form.id === '' ? '新增' : '编辑'"
      :visible.sync="visiable"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :destroy-on-close="true"
      width="50%"
      center
    >
      <el-form
        ref="form"
        :model="form"
        :rules="rules"
        label-width="100px"
        class="edit-form"
      >
        <!-- 表单项 -->
        <!-- ... 省略表单项 ... -->
        <el-form-item>
          <el-button @click="handleClose">返回</el-button>
          <el-button type="primary" @click="submit">保存</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
// ... 省略 import 和其他代码 ...
export default {
  name: 'EditDialog',
  props: {
    visiable: {
      type: Boolean,
      required: true,
      default: false
    },
    formData: {
      type: Object,
      default: () => ({}) // 确保默认值是一个函数返回空对象
    }
  },
  data() {
    return {
      form: {}, // 内部表单数据模型
      // ... rules 等其他 data 属性 ...
    };
  },
  watch: {
    // 监听外部传入的 formData，并用其副本来更新内部的 form
    // 配合 destroy-on-close，这个 watch 主要在编辑时首次打开弹窗时生效
    formData: {
      handler(val) {
        // 使用传入数据的副本更新内部表单
        // 如果 destroy-on-close 生效，每次打开弹窗 form 都会是初始状态
        // 然后这个 watch 会用传入的 formData (空对象或编辑数据) 更新 form
        this.form = { ...val };
      },
      immediate: true, // 立即执行一次
      // deep: true // 如果 formData 结构复杂，可能需要深度监听，但通常配合副本和 destroy-on-close 不需要
    },
    // (可选) 监听 visiable 关闭时重置表单，作为 destroy-on-close 的备用方案或补充
    // visiable(newVal) {
    //   if (!newVal) {
    //     // 弹窗关闭时尝试重置表单验证状态
    //     this.$nextTick(() => {
    //       if (this.$refs.form) {
    //         this.$refs.form.resetFields();
    //       }
    //       // 手动清空非 prop 绑定的字段（如果 resetFields 不够用）
    //       this.form = {};
    //     });
    //   }
    // }
  },
  methods: {
    handleClose() {
      this.$emit('closeDialog'); // 或者 update:visiable
    },
    submit() {
      this.$refs.form.validate(valid => {
        if (valid) {
          // ... 提交逻辑 ...
          // 触发父组件事件，并传递当前表单数据副本
          this.$emit('submit', { ...this.form });
          this.handleClose();
        } else {
          console.log('error submit!!');
          return false;
        }
      });
    },
    // ... 其他方法 ...
  }
  // ... 省略其他生命周期钩子等 ...
}
</script>

// ... 省略样式 ...
