<!--
 * @Description: In User Settings Edit
 * @Author: your name
 * @Date: 2019-10-16 11:28:20
 * @LastEditTime: 2019-10-16 11:46:31
 * @LastEditors: Please set LastEditors
 -->
<template>
  <div class="app-container">
    <div class="list-contatiner">
      <div class="list-container-change">
        <el-radio-group v-model="currentId" size="small" @change="handelMaterialChange">
          <el-radio-button v-for="item in materialTypes" :key="item.id" :label="item.id">{{ item.zhTitle }}</el-radio-button>
        </el-radio-group>
        <el-button v-permission="'btn-xeasylabel-material-edit'" type="primary" size="small" style="margin-left: 20px;" @click="handleAdd">新增</el-button>
        <el-button v-permission="'btn-xeasylabel-material-edit'" type="primary" size="small" style="margin-left: 20px;" @click="importMaterialResourceShowFlag = true">导入</el-button>
        <el-button v-permission="'btn-xeasylabel-material-edit'" type="danger" size="small" style="margin-left: 20px;" @click="handleBatchDelete">批量删除</el-button>
        <el-button v-if="currentId==='5fb7265e5baa9f55802cf75b'" v-permission="'btn-xeasylabel-material-edit'" type="primary" size="small" style="margin-left: 20px;" @click="handleBatchAdd">批量新增</el-button>
        <p>
          <el-radio-group v-if="lableList" v-model="labelId" size="small" @change="handelMaterialLableChange">
            <el-radio-button v-for="item in lableList" :key="item.id" :label="item.id">{{ item.name }}</el-radio-button>
          </el-radio-group>

          <span v-if="currentId === '5fb726715baa9f55802cf75d'" class="demonstration">语种:</span>
          <el-radio-group v-if="currentId === '5fb726715baa9f55802cf75d'" v-model="localeCode" size="small" @change="handelMaterialLableChange">
            <el-radio-button label="">全部</el-radio-button>
            <el-radio-button v-for="item in localeData" :key="item.id" :label="item.id">{{ item.name }}</el-radio-button>
          </el-radio-group>
        </p></div>
      <div class="list-container-data">
        <el-checkbox-group v-model="checkedMaterialList" @change="handleCheckedMaterialChange">
          <div class="list-container-data-cards">
            <div v-for="(item, index) in list" :key="index" class="list-container-data-cards-item">
              <Card :item="item" :mult-select-mode="multSelectMode" @notifyUpdate="notifyUpdate" @notifyEdit="notifyEdit" />
              <div class="checkboxclass">
                <el-checkbox size="medium" :label="item.id" />
              </div>
            </div>
          </div>
        </el-checkbox-group>
        <div class="list-container-data-page">
          <el-pagination
            :total="page.total"
            :current-page="page.no"
            :page-size="page.size"
            layout="total, sizes, prev, pager, next, jumper"
            @current-change="handleCurrentChange"
            @size-change="handleSizeChange"
          />
        </div>
      </div>
    </div>
    <div class="dialog-container">
      <el-dialog
        :append-to-body="true"
        title="批量新增"
        :visible.sync="showBatchAdd"
        center
        :show-close="false"
        :destroy-on-close="true"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
      >
        <CommonFormBatch v-if="currentView === 'CommonForm'" :m-id="currentId" :m-name="currentName" :form-data="currentForm" @handleBack="handleBack" />
      </el-dialog>
    </div>
    <div class="dialog-container">
      <el-dialog
        :append-to-body="true"
        :title="currentTitile"
        :visible.sync="showAdd"
        center
        :show-close="false"
        :destroy-on-close="true"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
      >
        <FontForm v-if="currentView === 'FontForm'" :id="currentId" :form-data="currentForm" @handleBack="handleBack" />
        <TextPopForm v-if="currentView === 'TextPopForm' && showAdd" :m-id="currentId" :m-name="currentName" :form-data="currentForm" @handleBack="handleBack" />
        <ToDoListForm v-if="(currentView === 'ToDoListForm' || currentView === '')" :m-id="currentId" :m-name="currentName" :form-data="currentForm" @handleBack="handleBack" />
        <CommonForm v-if="currentView === 'CommonForm'" :m-id="currentId" :m-name="currentName" :form-data="currentForm" @handleBack="handleBack" />
      </el-dialog>
      <!-- 素材类型 -->
      <el-dialog
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        :title="typeEditForm.id ? '编辑贴纸类型': '新增贴纸类型'"
        :visible.sync="showEditType"
        width="30%"
        center
      >
        <el-input v-model="typeEditForm.enTitle" maxlength="20" placeholder="请输入英文标题,tz_开头如tz_todolist" style="margin-bottom: 10px;" />
        <el-input v-model="typeEditForm.zhTitle" maxlength="8" placeholder="请输入中文标题" />
        <span slot="footer" class="dialog-footer">
          <el-button @click="showEditType = false">取 消</el-button>
          <el-button v-permission="'btn-xeasylabel-material-edit'" type="primary" @click="handleAddOrUpdateType">确 定</el-button>
        </span>
      </el-dialog>

      <el-dialog
        title="导入素材"
        :visible.sync="importMaterialResourceShowFlag"
        width="50%"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        center
      >
        <el-input v-model="importMaterialResourceStr" type="textarea" rows="8" />
        <span slot="footer" class="dialog-footer">
          <el-button @click="importMaterialResourceShowFlag = false">取 消</el-button>
          <el-button v-permission="'btn-xeasylabel-material-edit'" type="primary" @click="handleImport">确 定</el-button>
        </span>
      </el-dialog>
    </div>

  </div>
</template>
<script>
import { getTypes, fetchListByPage, getMaterialDetail, addMaterialType, updateMaterialType, deleteType, importMaterialResource, deleteRecord } from '@/api/xeasylabel/material'
import Card from './component/Card/index'
import CommonForm from './component/CommonForm/index'
import CommonFormBatch from './component/CommonFormBatch/index'
import FontForm from './component/FontForm/index'
import TextPopForm from './component/TextPopForm/index'
import ToDoListForm from './component/ToDoListForm/index'
import { fetchDictTypeList } from '@/api/xeasylabel/dict'

/** 选项卡与 组件对应关系 */
// FontForm -- tz_font
// ToDoListForm -- tz_todolist tz_postitnotes tz_frame
// TextPopForm -- tz_textbubble tz_curriculum tz_businesscard tz_idcard
// CommonForm -- tz_emoji tz_decoration tz_food tz_gesture
// const tabComponentMap = {
//   'tz_front': 'FontForm',
//   'tz_todolist': 'ToDoListForm', 'tz_postitnotes': 'ToDoListForm', 'tz_frame': 'ToDoListForm',
//   'tz_textbubble': 'TextPopForm', 'tz_curriculum': 'TextPopForm', 'tz_businesscard': 'TextPopForm', 'tz_idcard': 'TextPopForm',
//   'tz_emoji': 'CommonForm', 'tz_decoration': 'CommonForm', 'tz_food': 'CommonForm', 'tz_gesture': 'CommonForm'
// }
export default {
  components: {
    Card,
    CommonForm,
    FontForm,
    TextPopForm,
    ToDoListForm,
    CommonFormBatch
  },
  data() {
    return {
      showBatchAdd: false,
      multSelectMode: false,
      /** 素材类型 */
      materialTypes: [],
      /** 当前素材类型Id */
      currentId: '',
      /** 当前素材类型名 */
      currentName: '',
      labelId: '',
      /** 标题名 */
      currentTitile: '',
      /** 分页查询 */
      page: {
        size: 20,
        no: 1,
        total: 0
      },
      /** 当前素材列表 */
      list: [],
      /** 编辑添加模式 */
      showAdd: false,
      /** 当前添加组件 */
      currentView: 'FontForm',
      localeCode: '',
      /** 当前卡片内容 */
      currentForm: {},
      /** 显示编辑框 */
      showEditType: false,
      /** 类型编辑 */
      typeEditForm: {
        com: 'CommonForm',
        enTitle: '',
        name: '',
        id: '',
        zhTitle: ''
      },
      localeData: [{ 'id': 'zh_CN', 'name': '简体' }, { 'id': 'zh_TW', 'name': '繁体' }, { 'id': 'en_US', 'name': '英语' }, { 'id': 'ko_KR', 'name': '韩语' }, { 'id': 'ja_JP', 'name': '日语' }, { 'id': 'ru_RU', 'name': '俄语' }, { 'id': 'fr_FR', 'name': '法语' }, { 'id': 'es_ES', 'name': '西班牙语(欧)' }, { 'id': 'es_LA', 'name': '西班牙语(拉美)' }, { 'id': 'ar_AE', 'name': '阿拉伯' }, {
        'id': 'de_DE',
        'name': '德语'
      }, {
        'id': 'it_IT',
        'name': '意大利'
      }, {
        'id': 'pt_PT',
        'name': '葡萄牙'
      }, {
        'id': 'th_TH',
        'name': '泰语'
      }, {
        'id': 'vi_VI',
        'name': '越南语'
      }],
      borderList: [{ id: 'all', name: '全部' }, { id: 'hot', name: '热门' }, { id: 'simple', name: '简约' }, { id: 'lovely', name: '可爱' }, { id: 'figure', name: '花纹' }, { id: 'chinese', name: '中国风' }, { id: 'classical', name: '古典' }],
      decorateList: [{ id: 'all', name: '全部' }, { id: 'hot', name: '热门' }, { id: 'storage', name: '收纳' }, { id: 'skin', name: '护肤' }, { id: 'kitchen', name: '厨房' }, { id: 'pet', name: '动物' }, { id: 'symbol', name: '符号' }, { id: 'currency', name: '货币' }, { id: 'face', name: '表情' }, { id: 'office', name: '办公' }, { id: 'plant', name: '植物' }, { id: 'cartoon', name: '卡通' }, { id: 'travel', name: '旅游' }, { id: 'study', name: '学习' }, { id: 'fruits', name: '食物' }, { id: 'bubble', name: '气泡' }],
      /** 选项卡与组件映射 */
      tabComponentMap: undefined,
      importMaterialResourceShowFlag: false,
      importMaterialResourceStr: '',
      checkedMaterialList: []
    }
  },
  computed: {
    lableList: function() {
      if (this.currentId) {
        if (this.currentId === '5fb726675baa9f55802cf75c') {
          return this.borderList
        } else if (this.currentId === '5fb7265e5baa9f55802cf75b') {
          return this.decorateList
        } else {
          return null
        }
      } else {
        return null
      }
    }
  },
  mounted() {
    this.getMaterialTypes()
    this.getBorderList()
    this.getDecorateList()
  },
  methods: {
    handleCheckedMaterialChange(e) {
      console.log(e)
      this.checkedMaterialList = e
      // jjjj
    },
    async getBorderList() {
      const params = {
        type: 'material_sub_type_bk'
      }
      const res = await fetchDictTypeList(params)
      if (res.head.ret === 0) {
        const result = [{ id: '', name: '全部' }]
        res.data.forEach(item => {
          const temp = {
            id: item.value,
            name: item.label
          }
          result.push(temp)
        })
        this.borderList = result
      }
    },
    async getDecorateList() {
      const params = {
        type: 'material_sub_type_zs'
      }
      const res = await fetchDictTypeList(params)
      if (res.head.ret === 0) {
        const result = [{ id: '', name: '全部' }]
        res.data.forEach(item => {
          const temp = {
            id: item.value,
            name: item.label
          }
          result.push(temp)
        })
        this.decorateList = result
      }
    },
    /** 获取素材类型 */
    async getMaterialTypes() {
      const res = await getTypes()
      if (res.head.ret === 0) {
        console.log(res)
        this.materialTypes = res.data
        this.materialTypes.forEach(m => {
          m.disabled = false
        })
        this.currentId = this.materialTypes[0].id
        this.updateTabComponentMap()
        this.getListById()
      }
    },
    /** 更新组件映射 */
    updateTabComponentMap() {
      if (this.materialTypes && this.materialTypes.length > 0) {
        this.tabComponentMap = new Map()
        this.materialTypes.forEach(m => {
          this.tabComponentMap.set(m.name, m.com)
        })
      }
    },
    /** 获取素材列表 by Id */
    async getListById(id) {
      const labelTemp = (this.labelId && this.labelId !== 'all') ? this.labelId : ''
      const res = await fetchListByPage({
        id: this.currentId,
        pageno: this.page.no,
        pagesize: this.page.size,
        label: labelTemp,
        localeCode: this.localeCode
      })
      if (res.head.ret === 0) {
        this.list = res.data.result
        this.page.total = res.data.totalCount || 0
      } else {
        this.$message.error('获取素材列表数据失败')
      }
      this.checkedMaterialList = []
    },
    /** 获取素材详情 */
    async getDetail(id) {
      const res = await getMaterialDetail({
        id: id
      })
      if (res.head.ret === 0) {
        console.log('请求结果', res.data)
        this.currentForm = {}
        this.currentForm = res.data
        this.currentForm.id = id
        this.materialTypes.forEach(m => {
          if (m.id === this.currentId) {
            this.currentName = m.name
            this.currentTitile = m.zhTitle
          }
        })
        const com = this.tabComponentMap.get(this.currentName)
        this.currentTitile = this.currentTitile + '编辑'
        this.currentView = com
        this.showAdd = true
      }
    },
    /** 每页条数 */
    handleSizeChange(val) {
      this.page.size = val
      this.getListById()
    },
    /** 当前页 */
    handleCurrentChange(val) {
      this.page.no = val
      this.getListById()
    },
    /** 切换素材类型 */
    handelMaterialChange(tab) {
      // console.log('handelMaterialChange: ', this.currentId)
      this.localeCode = ''
      this.labelId = 'all'
      this.materialTypes.forEach(m => {
        if (m.id === this.currentId) {
          this.currentName = m.name
          this.currentTitile = m.zhTitle
        }
      })
      this.getListById()
    },
    handelMaterialLableChange(tab) {
      this.getListById()
    },
    /** 删除后更新 */
    notifyUpdate() {
      this.getListById()
    },
    /** 点击添加 */
    handleAdd() {
      this.materialTypes.forEach(m => {
        if (m.id === this.currentId) {
          this.currentName = m.name
          this.currentTitile = m.zhTitle
        }
      })
      this.currentForm = {}
      const com = this.tabComponentMap.get(this.currentName)
      this.currentTitile = this.currentTitile + '新增'
      this.currentView = com
      this.showAdd = true
      console.log(this.currentView)
    },
    handleBatchAdd() {
      this.materialTypes.forEach(m => {
        if (m.id === this.currentId) {
          this.currentName = m.name
          this.currentTitile = m.zhTitle
        }
      })
      this.currentForm = {}
      const com = this.tabComponentMap.get(this.currentName)
      this.currentTitile = this.currentTitile + '新增'
      this.currentView = com
      this.showBatchAdd = true
    },
    /** 增加分类 */
    handleAddType() {
      this.typeEditForm = {
        com: 'CommonForm',
        enTitle: '',
        name: '',
        zhTitle: '',
        // 固定分类和子分类
        type: 1,
        subType: 3
      }
      this.showEditType = true
    },
    /** 删除分类 */
    handleDeleteType() {
      this.$confirm('此操作将永久素材分类及所属素材，不可恢复, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        console.log('delete: ', this.tabComponentMap.get(this.currentName))
        console.log('delete: ', this.tabComponentMap)
        console.log('delete: ', this.currentName)
        if (this.tabComponentMap.get(this.currentName) !== 'CommonForm') {
          this.$message.warning('该分类非贴纸分类，不允许删除')
          return
        }

        if (this.list.length) {
          this.$message.warning('该分类下存在素材记录，请先删除记录')
          return
        }
        this.doDeleteType()
      }).catch(() => {
      })
    },
    async doDeleteType() {
      const res = await deleteType({ id: this.currentId })
      if (res.head.ret === 0) {
        this.$message({
          type: 'success',
          message: '删除成功!'
        })
        this.getMaterialTypes()
      } else {
        this.$message({
          type: 'error',
          message: '删除失败!'
        })
      }
    },
    /** 修改分类 */
    handleUpdateType() {
      this.typeEditForm = {}
      this.materialTypes.forEach(m => {
        if (m.id === this.currentId) {
          this.typeEditForm = { ...m }
        }
      })
      this.showEditType = true
    },
    /** 新增或修改分类 */
    async handleAddOrUpdateType() {
      if (!this.typeEditForm.enTitle && !this.typeEditForm.zhTitle) {
        this.$message.error('英文标题或中文标题不能为空')
        return
      }
      let res
      if (this.typeEditForm.id) {
        const data = {
          id: this.typeEditForm.id,
          com: this.typeEditForm.com,
          enTitle: this.typeEditForm.enTitle,
          zhTitle: this.typeEditForm.zhTitle
        }
        res = await updateMaterialType(data)
      } else {
        this.typeEditForm.name = this.typeEditForm.enTitle
        res = await addMaterialType(this.typeEditForm)
      }
      if (res.head.ret === 0) {
        // 刷新整个页面
        this.getMaterialTypes()
      } else {
        this.$message.error('新增或更新素材类型失败')
      }
      this.showEditType = false
    },
    handleImport() {
      const _this = this
      if (this.importMaterialResourceStr) {
        const params = JSON.parse(this.importMaterialResourceStr)
        importMaterialResource(params).then(res => {
          _this.importMaterialResourceShowFlag = false
          _this.getListById()
        })
      }
    },
    /** 切换选项卡 */
    handleClickTab(tab, event) {
      const com = this.tabComponentMap.get(this.currentName)
      this.currentView = com
    },
    /** 点击返回 */
    handleBack(val) {
      this.currentForm = {}
      this.showAdd = false
      this.showBatchAdd = false
      if (val === '1') {
        this.getListById()
      }
    },
    /** 子组件通知进入编辑 */
    async notifyEdit(item) {
      await this.getDetail(item.id)
      // this.materialTypes.forEach(m => {
      //   if (m.id === this.currentId) {
      //     this.currentName = m.name
      //   }
      // })
      // const com = tabComponentMap[this.currentName]
      // this.currentTitile = this.currentName + '编辑'
      // this.currentView = com
    },
    handleBatchDelete() {
      if (this.checkedMaterialList.length > 0) {
        this.$confirm('此操作将永久删除该记录, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          // 提示是否删除
          deleteRecord({
            id: this.checkedMaterialList.join(',')
          }).then(res => {
            if (res.head.ret === 0) {
              this.$message.success('删除成功')
            } else {
              this.$message.error('删除失败')
            }
            this.getListById()
          })
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
      } else {
        this.$message.error('请选择要删除的数据')
      }
    }

  }
}
</script>

<style lang="scss" scoped>
.app-container{
  .list-contatiner{
    .list-container-data{

      &-cards{
        margin-top: 20px;
        margin-bottom: 20px;
        display: flex;
        flex-flow: row wrap;
        justify-content: flex-start;
        &-item{
          width: 15%;
          margin-left: 1%;
          border: 1px solid grey;
          margin-bottom: 10px;
        }
      }
    }
  }
}

.checkboxclass {
    position: absolute;
    left: 0;
    top: 0;
    padding: 5px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    width: 100%;
}

.list-container-data-cards-item {
  position: relative;
}

</style>
