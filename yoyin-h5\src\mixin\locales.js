import { getLanguage } from '@/utils/common'
export default {
  mounted() {
    // let locale = 'zh_CN'
    // if (window.StarPrinterJS && .getLanguage()) {
    //   locale = window.StarPrinterJS.getLanguage()
    // } else {
    //   const params = getUrlParameter()
    //   locale = params && params.language
    // }
    this.$i18n.locale = getLanguage() || 'zh_CN'
    this.setTitle()
  },
  watch: {
    '$i18n.locale'() {
      this.setTitle()
    }
  },
  created() {
    window.setLanguage = (res) => this.setLanguage(res)
    window.setToken = (res) => this.setToken(res)
  },
  methods: {
    setLanguage(res) {
      this.$i18n.locale = res || 'zh_CN'
    },
    setToken(res) {
      window.localStorage.setItem('jwttoken', res)
    },
    setTitle() {
      // alert(this.$t('title.' + this.moduleKey))
      if (this.moduleKey && this.$t) {
        document.title = this.$t('title.' + this.moduleKey)
      }
    }
  }
}
