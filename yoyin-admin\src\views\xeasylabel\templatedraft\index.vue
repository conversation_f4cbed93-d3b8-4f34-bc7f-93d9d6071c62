<template>
  <div class="app-container">
    <div class="table-query-container">
      <span class="demonstration">分类:</span>
      <el-radio-group v-model="params.type" size="small" @change="handelTypeChange">
        <el-radio-button label="">全部</el-radio-button>
        <el-radio-button v-for="item in typeData" :key="item.id" :label="item.id">{{ item.name }}</el-radio-button>
      </el-radio-group>
      <p />
      <span class="demonstration">语种:</span>
      <el-radio-group v-model="params.localecode" size="small" @change="handelTypeChange">
        <el-radio-button label="">全部</el-radio-button>
        <el-radio-button v-for="item in localeData" :key="item.id" :label="item.id">{{ item.name }}</el-radio-button>
      </el-radio-group>
      <p />
      <!-- <span class="demonstration">纸张类型:</span>
      <el-radio-group v-model="params.paperType" size="small" @change="handelTypeChange">
        <el-radio-button label="">全部</el-radio-button>
        <el-radio-button v-for="item in paperTypeData" :key="item.id" :label="item.id">{{ item.name }}</el-radio-button>
      </el-radio-group> -->

      <el-button type="primary" @click="handleQuery">查询</el-button>
      <el-button v-permission="'btn_printtemplate_edit'" type="primary" @click="exportData">导出</el-button>
      <el-button v-permission="'btn_printtemplate_edit'" type="primary" @click="importData">导入</el-button>
    </div>
    <div class="table-container">
      <el-table
        ref="multipleTable"
        :data="list"
        tooltip-effect="dark"
        style="width: 100%"
        border
        max-height="700px"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <!-- <el-table-column prop="userId" label="用户Id" width="210" /> -->
        <el-table-column prop="pic" label="缩略图">
          <template slot-scope="scope">
            <img :src="scope.row.pic" style="width:120px; height:80px; object-fit:contain">
          </template>
        </el-table-column>
        <el-table-column prop="sortNum" label="排序" />
        <el-table-column prop="localeCode" :formatter="languageTypeFormatter" width="80" label="语种" />

        <el-table-column prop="type" :formatter="typeFormatter" width="80" label="分类" />
        <el-table-column prop="name" label="标题" />
        <el-table-column prop="content" label="内容" />
        <!-- <el-table-column prop="recommend" label="关键词" /> -->
        <!-- <el-table-column prop="printerType" :formatter="printerTypeFormatter" width="300" label="适应纸张" /> -->
        <el-table-column prop="paperSize" :formatter="printerTypeFormatter" width="300" label="适应纸张" />
        <el-table-column prop="draftsDto" :formatter="draftsDtoFormatter" width="200" label="模板内容(纸张类型)" />
        <el-table-column prop="isHot" label="是否推荐">
          <template slot-scope="scope">
            {{ scope.row.isHot!==1 ? '否' : '是' }}
          </template>
        </el-table-column>
        <el-table-column label="操作">
          <template slot-scope="scope">
            <el-button size="mini" type="primary" @click="handleEdit(scope.row)">编辑</el-button>
            <el-button v-permission="'btn_printtemplate_edit'" size="mini" type="danger" @click="handleDelete(scope.row.id)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="pagination-container" style="margin-top:20px;">
      <el-pagination
        :current-page="params.pageno"
        :page-sizes="[10, 20, 30, 40]"
        :page-size="params.pagesize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <el-dialog
      :title="'编辑模板'"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :visible.sync="showDialog"
      width="50%"
      center
    >
      <el-form ref="form" :model="templateDraft" :rules="rules" label-width="100px" class="edit-form">
        <el-form-item label="封面" prop="pic">
          <!-- <img :src="templateDraft.pic" class="avatar"> -->
          <SingleUpload ref="templateInfoListUrl" key="listUrl" key-word="listUrl" :init-url="isEdit ? templateDraft.pic: ''" @updateFile="updateFile" />
        </el-form-item>
        <el-form-item label="标题" prop="name">
          <el-input v-model="templateDraft.name" />
        </el-form-item>
        <el-form-item label="描述" prop="content">
          <el-input v-model="templateDraft.content" />
        </el-form-item>
        <el-form-item label="关键词" prop="recommend">
          <el-input v-model="templateDraft.recommend" />
        </el-form-item>
        <el-form-item label="语种" prop="localeCode">
          <!-- <el-radio-group v-model="templateDraft.localeCode">
            <el-radio-button v-for="item in localeData" :key="item.id" :label="item.id">{{ item.name }}</el-radio-button>
          </el-radio-group> -->

          <el-checkbox-group v-model="localeCodeStr">
            <el-checkbox-button v-for="item in localeData" :key="item.id" :label="item.id">{{ item.name }}</el-checkbox-button>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="分类" prop="type">
          <el-radio-group v-model="templateDraft.type">
            <el-radio-button v-for="item in typeData" :key="item.id" :label="item.id">{{ item.name }}</el-radio-button>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="适应纸张" prop="printerType">
          <el-checkbox-group v-model="paperSizeLabel">
            <el-checkbox-button v-for="item in paperSizeData" :key="item.id" :label="item.id">{{ item.name }}</el-checkbox-button>
          </el-checkbox-group>

        </el-form-item>
        <el-form-item label="纸张类型" prop="paperType">
          <el-checkbox-group v-model="paperTypeLabel">
            <el-checkbox-button v-for="item in paperTypeData" :key="item.id" :label="item.id">{{ item.name }}</el-checkbox-button>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="是否推荐" prop="isHot">
          <el-radio-group v-model="templateDraft.isHot">
            <el-radio-button label="0">否</el-radio-button>
            <el-radio-button label="1">是</el-radio-button>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="排序" prop="sortNum">
          <el-input
            v-model="templateDraft.sortNum"
            type="text"
          />
        </el-form-item>
        <el-form-item>
          <el-button @click="handleClose">返回</el-button>
          <el-button v-permission="'btn_printtemplate_edit'" type="primary" @click="submit">保存</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>

    <el-dialog
      :title="'设置显示开关'"
      :visible.sync="languageWin"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      width="550px"
      center
    >
      <el-form label-width="150px" class="edit-form" :inline="true">
        <el-form-item v-for="(item,index) in languageConfigData" :key="item.key" :label="item.name">
          <el-switch v-model="languageConfigDataValue[index]" @change="turnLanguageConfig(item)" />
        </el-form-item>
        <p />
        <el-form-item style="width: 100%; text-align: center;">
          <el-button @click="languageWin=false">关闭</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>

    <el-dialog
      :title="'导出数据'"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :visible.sync="showDialogExportData"
      width="50%"
      center
    >
      <el-form label-width="100px" class="edit-form">
        {{ exportDataValue }}
        <el-form-item>
          <el-button @click="handleCloseExport">返回</el-button>
          <el-button type="primary" @click="doCopy">复制</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>

    <el-dialog
      :title="'导入数据'"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :visible.sync="showDialogImportData"
      width="50%"
      center
    >
      <el-form label-width="100px" class="edit-form">
        <el-form-item label="导入数据">
          <el-input v-model="importDataValue" type="textarea" :rows="4" /></el-form-item>
        <el-form-item>
          <el-button @click="handleCloseImport">返回</el-button>
          <el-button type="primary" @click="doImportData">导入</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>
<script>
import SingleUpload from '../material/component/SingleUpload/index'
import { fetchListByPage, deleteTemplateDrafts, saveTemplateDrafts, turnConfig } from '@/api/xeasylabel/templatedraft'
import { fetchDictTypeList as dictFetchList } from '@/api/xeasylabel/dict'
import AliyunOSS from '@/utils/aliyunOSS'
const oss = new AliyunOSS()
export default ({
  name: 'TemplateDraft',
  components: {
    SingleUpload
  },
  data() {
    return {
      languageWin: false,
      languageConfigData: [],
      languageConfigDataValue: [],
      printerTypeLabel: ['-1'],
      paperTypeLabel: [],
      paperSizeLabel: [],
      localeCodeStr: ['zh_CN'],
      typeData: [],
      localeData: [{
        'id': 'zh_CN',
        'name': '简体'
      }],
      printerTypeData: [{
        'id': '-1',
        'name': '15mm标签连续纸'
      }, {
        'id': '308',
        'name': '22×14mm'
      }, {
        'id': '420',
        'name': '30×14mm'
      }, {
        'id': '560',
        'name': '40×14mm'
      }, {
        'id': '700',
        'name': '50×14mm'
      }, {
        'id': '1500',
        'name': '50×30mm'
      }, {
        'id': '2000',
        'name': '50×40mm'
      }, {
        'id': '3750',
        'name': '50×75mm'
      }, {
        'id': '1680',
        'name': '56×30mm(bq1)'
      }, {
        'id': '1500 ',
        'name': '50×30mm(bq1)'
      }, {
        'id': '1200',
        'name': '40×30mm(bq1)'
      }, {
        'id': '600',
        'name': '20×30mm(bq1)'
      }, {
        'id': '2400',
        'name': '40×60mm(bq1)'
      }, {
        'id': '375',
        'name': '25×15mm(bq1)'
      }, {
        'id': '56',
        'name': '56mm连续纸(bq1)'
      }],
      paperSizeData: [],
      paperTypeData: [{
        'id': '1',
        'name': '连续纸'
      }, {
        'id': '2',
        'name': '间隙纸'
      }],
      // 查询参数
      params: {
        type: '',
        ishot: '0',
        pagemo: 1,
        pagesize: 10
      },
      /** 列表 */
      list: [],
      /** 校验规则 */
      rules: {
        // name: [
        //   { required: true, message: '请输入标题', trigger: 'blur' }
        // ],
        content: [
          { required: true, message: '请输入描述', trigger: 'blur' }
        ]
      },
      /** 总条数 */
      total: 0,
      templateDraft: {},
      /** 勾选数据 */
      multipleSelection: [],
      showDialog: false,
      showDialogExportData: false,
      showDialogImportData: false,
      exportDataValue: '',
      importDataValue: '',
      uploadFiles: { listUrl: {}}
    }
  },
  computed: {
    isEdit() {
      if (this.templateDraft && this.templateDraft.id) {
        return true
      } else {
        return false
      }
    }
  },
  mounted() {
    this.initPaperSizeData()
    this.getList()
    this.initTemplateTypeData()
    // this.getLanguageConfigData()
  },
  methods: {
    /** 子组件上传文件 */
    updateFile(key, file) {
      this.uploadFiles[key] = {
        file,
        key
      }
    },
    async initPaperSizeData() {
      const res = await dictFetchList({
        type: 'paper_size'
      })
      this.paperSizeData = []
      res.data.forEach(item => {
        this.paperSizeData.push({
          'id': item.value,
          'name': item.label
        })
      })
    },
    async initTemplateTypeData() {
      const res = await dictFetchList({
        type: 'template_type'
      })
      this.typeData = []
      res.data.forEach(item => {
        this.typeData.push({
          'id': item.value,
          'name': item.label
        })
      })
      // console.log(res)
    },
    turnLanguageConfig(item) {
      const parms = {
        language: item.id
      }
      turnConfig(parms).then(res => {
        this.$message.success('操作成功')
      })
    },
    // async getLanguageConfigData() {
    //   const res = await getLanguageConfig()
    //   if (res.head.ret === 0) {
    //     this.languageConfigData = res.data
    //     this.languageConfigData.forEach(item => {
    //       this.languageConfigDataValue.push(item.flag)
    //     })
    //   }
    // },
    /** 获取列表 */
    async getList() {
      const res = await fetchListByPage(this.params)
      if (res.head.ret === 0) {
        this.list = res.data.result
        this.total = res.data.totalCount
      }
    },
    /** 切换每页条数 */
    handleSizeChange(val) {
      this.params.pagesize = val
      this.getList()
    },
    /** 切换当前页 */
    handleCurrentChange(val) {
      this.params.pageno = val
      this.getList()
    },
    /** 查询 */
    handleQuery() {
      this.getList()
    },
    handelTypeChange() {
      this.params.pageno = 1
      this.getList()
    },
    /** 删除 */
    async handleDelete(id) {
      const res = await deleteTemplateDrafts({ id })
      if (res.head.ret === 0) {
        this.$message.success('保存成功')
      } else {
        this.$message.error('保存失败')
      }
      this.getList()
    },
    // 模板分类
    typeFormatter(row, column) {
      const type = row.type.trim()
      let result = '未知'
      this.typeData.forEach(item => {
        if (item.id === type) {
          result = item.name
        }
      })

      return result
      // if (type === 'living') {
      //   return '居家收纳'
      // } else if (type === 'kitchen') {
      //   return '厨房收纳'
      // } else if (type === 'office') {
      //   return '办公收纳'
      // } else {
      //   return '未知'
      // }
    },

    printerTypeFormatter(row, column) {
      const type = row.paperSize ? row.paperSize.trim() + ';' : ''
      let result = ''
      console.log(type)
      console.log(this.paperSizeData)
      this.paperSizeData.forEach(item => {
        if (type && type.indexOf(item.id + ';') > -1) {
          result += item.name + ';'
        }
      })
      return result
    },

    // printerTypeFormatter(row, column) {
    //   const type = row.printerType.trim() + ','
    //   let result = ''
    //   this.printerTypeData.forEach(item => {
    //     if (type && type.indexOf(item.id + ',') > -1) {
    //       result += item.name + ';'
    //     }
    //   })
    //   return result
    // },

    draftsDtoFormatter(row, column) {
      if (row.draftsDto && row.draftsDto.paperObj) {
        return row.draftsDto.paperObj.paperWidth + '*' + row.draftsDto.paperObj.paperLength + '  paperType=' + row.draftsDto.paperObj.paperType
      }
      return ''
    },

    languageTypeFormatter(row) {
      // const localeCode = row.localeCode.trim()
      // let result = ''
      // this.localeData.forEach(item => {
      //   if (item.id === localeCode) {
      //     result = item.name
      //   }
      // })
      // return result;

      const localeCode = row.localeCode.trim()
      let result = ''
      this.localeData.forEach(item => {
        if (localeCode && localeCode.indexOf(item.id) > -1) {
          result += item.name + ';'
        }
      })
      return result
    },
    /** 勾选事件 */
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    /** 清空勾选 */
    handleClearSelection() {
      this.$refs.multipleTable.clearSelection()
    },
    handleEdit(row) {
      this.templateDraft = JSON.parse(JSON.stringify(row))
      // if (this.templateDraft.printerType) {
      //   this.printerTypeLabel = this.templateDraft.printerType.split(';')
      // } else {
      //   this.printerTypeLabel = []
      //   this.printerTypeLabel.push('-1')
      // }
      if (this.templateDraft.pic) {
        this.uploadFiles.listUrl.pic = this.templateDraft.pic
      } else {
        this.uploadFiles.listUrl.pic = ''
        this.uploadFiles.listUrl = {}
        this.$refs.paperInfoListUrl.clearFile()
      }

      if (this.templateDraft.paperSize) {
        this.paperSizeLabel = this.templateDraft.paperSize.split(';')
      } else {
        this.paperSizeLabel = []
        this.paperSizeLabel.push('15')
      }

      if (this.templateDraft.paperType) {
        this.paperTypeLabel = this.templateDraft.paperType.split(';')
      } else {
        this.paperTypeLabel = []
      }

      if (this.templateDraft.localeCode) {
        this.localeCodeStr = this.templateDraft.localeCode.split(';')
      } else {
        this.localeCodeStr = []
        this.localeCodeStr.push('zh_CN')
      }

      this.showDialog = true
    },
    handleClose() {
      this.showDialog = false
    },
    submit() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          const upFiles = []
          const picDto = {}

          Object.keys(this.uploadFiles).forEach(key => {
            const item = this.uploadFiles[key]
            if (item.file) {
              item.file.key = key
              upFiles.push(item.file)
            } else {
              picDto[key] = { pic: item.pic }
            }
          })

          // this.templateDraft.printerType = this.printerTypeLabel.join(';')
          this.templateDraft.paperSize = this.paperSizeLabel.join(';')
          this.templateDraft.localeCode = this.localeCodeStr.join(';')
          this.templateDraft.paperType = this.paperTypeLabel.join(';')

          // 上传
          oss.uploadFiles(upFiles, (results, error) => {
            if (error) {
              this.$message.error('文件上传失败，请检查')
              return
            }
            if (results) {
              results.forEach(res => {
                picDto[res.key] = { pic: res.name }
              })
            }

            this.templateDraft.pic = picDto.listUrl.pic

            saveTemplateDrafts(this.templateDraft).then(res => {
              this.showDialog = false
              this.$message.success('保存成功')
              this.getList()
            })
          })
        } else {
          console.log('submit error')
          return -1
        }
      })
    },
    exportData() {
      if (this.multipleSelection.length === 0) {
        this.$message.error('请选择记录')
        return
      }

      const tempRows = JSON.parse(JSON.stringify(this.multipleSelection))
      tempRows.forEach(element => {
        delete element.id
      })

      this.exportDataValue = JSON.stringify(tempRows)
      this.showDialogExportData = true

      // this.$copyText(abc).then(function(e) {
      //   console.log(e)
      //   alert('Copied=' + e.text)
      // }, function(e) {
      //   alert('Can not copy', e.text)
      //   console.log(e)
      // })
    },
    handleCloseExport() {
      this.showDialogExportData = false
    },
    doCopy() {
      const that = this
      this.$copyText(this.exportDataValue).then(function(e) {
        that.$message.success('复制成功')
      }, function(e) {
        alert('Can not copy', e.text)
        console.log(e)
      })
    },
    importData() {
      this.showDialogImportData = true
    },
    handleCloseImport() {
      this.showDialogImportData = false
    },
    async doImportData() {
      if (this.importDataValue) {
        const datas = JSON.parse(this.importDataValue)
        for (let index = 0; index < datas.length; index++) {
          await saveTemplateDrafts(datas[index])
        }
      }
      this.$message.success('导入成功')
      this.showDialogImportData = false
      this.getList()
    }
  }
})
</script>
