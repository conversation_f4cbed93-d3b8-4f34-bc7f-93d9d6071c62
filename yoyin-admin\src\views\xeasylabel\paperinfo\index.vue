<!--
 * @Description: In User Settings Edit
 * @Author: your name
 * @Date: 2019-10-16 11:28:20
 * @LastEditTime: 2019-10-22 17:03:31
 * @LastEditors: Please set LastEditors
 -->
<template>
  <div class="app-container">
    <div class="control-container">
      <el-radio-group v-model="rfidFlag" @change="settingTabelListData">
        <el-radio-button label="1">带RFID</el-radio-button>
        <el-radio-button label="0">不带RFID</el-radio-button>
      </el-radio-group>
      <el-button v-permission="'btn-xeasylabel-paperinfo-edit'" type="success" @click="addVersion">新增</el-button>
      <p />
      耗材类型：<el-radio-group v-model="paperType" @change="settingTabelListData">
        <el-radio-button v-for="item in paperTypeData" :key="item.id+item.label" :label="item.id">{{ item.label }}</el-radio-button>
      </el-radio-group>

    </div>
    <div class="control-container">
      <el-row style="line-height: 40px;">
        <el-col :span="30">宽度：</el-col>
        <el-col :span="3"><el-input v-model="queryHeight" @change="settingTabelListData" /></el-col>
        <el-col :span="30" :offset="1">长度：</el-col>
        <el-col :span="3"><el-input v-model="queryWidth" @change="settingTabelListData" /></el-col>
        <el-col :span="30" :offset="1"><el-button type="success" @click="settingTabelListData">查询</el-button></el-col>
      </el-row>
    </div>
    <!-- <div class="choose-container">
      <span>
        已选中
        <span>{{ multipleSelection.length }}</span> 项
      </span>
      <a @click="clearSelection">清空</a>
      <el-button type="primary" size="small" :disabled="multipleSelection.length == 0" style="margin-left: 20px;" @click="handleMultiModify">批量修改</el-button>
      <el-button type="primary" size="small" :disabled="multipleSelection.length == 0" style="margin-left: 20px;" @click="handleMultiDel">批量删除</el-button>
    </div> -->
    <div class="table-container">
      <el-table
        ref="multipleTable"
        :data="tableList"
        tooltip-effect="dark"
        style="width: 100%"
        border
        @selection-change="handleSelectionChange"
      >
        <el-table-column prop="sortNum" label="排序" width="50" align="center" />
        <el-table-column label="耗材ID" width="120" align="center">
          <template slot-scope="scope">{{ scope.row.paperId }}</template>
        </el-table-column>
        <el-table-column label="耗材规格" width="120" align="center">
          <template slot-scope="scope">{{ formatWHInfo(scope.row) }}</template>
        </el-table-column>
        <el-table-column prop="gapWidth" label="缝隙(mm)" width="120" align="center" />
        <el-table-column prop="colorCode" label="图像颜色" width="120" align="center">
          <template slot-scope="scope">
            <div :style="settingBg(scope.row)">
              {{ scope.row.colorCode }}
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="printerType" label="适配机型" width="120" align="center" />
        <!-- <el-table-column prop="needForceUpdate" label="强制升级" width="120">
          <template slot-scope="scope">{{ scope.row.needForceUpdate==0?"否":"是" }}</template>
        </el-table-column> -->
        <el-table-column prop="zhTitle" label="列表名称" align="center" :formatter="formatListName" width="200" />
        <el-table-column prop="imageUrl" label="列表图" width="160" align="center">
          <template slot-scope="scope">
            <img v-if="scope.row.imageUrl" style="width:160px; height:80px; object-fit:contain" :src="scope.row.imageUrl">
          </template>
        </el-table-column>
        <el-table-column prop="resUrl" label="资源图" width="160" align="center">
          <template slot-scope="scope">
            <img v-if="scope.row.resUrl" style="width:160px; height:80px; object-fit:contain" :src="scope.row.resUrl">
          </template>
        </el-table-column>
        <el-table-column prop="isNew" label="展示new" width="120" align="center">
          <template slot-scope="scope">
            {{ scope.row.isNew===0 ? '否' : '是' }}
          </template>
        </el-table-column>
        <el-table-column label="操作" fixed="right" width="160" align="center">
          <template slot-scope="scope">
            <el-button size="mini" @click="handleEdit(scope.$index, scope.row)">编辑</el-button>
            <!-- <el-button size="mini" @click="handleOnlyFile(scope.$index, scope.row)">仅上传文件</el-button> -->
            <el-button
              v-permission="'btn-xeasylabel-paperinfo-edit'"
              size="mini"
              type="danger"
              @click="handleDeleteRow(scope.$index, scope.row)"
            >删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <!-- <div class="pagination-container" style="margin-top:20px;">
      <el-pagination
        :current-page="currentPage"
        :page-sizes="[10, 20, 30, 40]"
        :page-size="currentSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div> -->
    <!-- 弹框 -->
    <div class="dialog-container">
      <el-dialog
        :title="form.id === '' ? '新增纸张耗材信息' : '编辑纸张耗材信息' "
        :visible.sync="showDialog"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        width="1000px"
        center
      >
        <el-form
          ref="ruleForm"
          :model="form"
          :rules="rules"
          label-width="120px"
          class="demo-ruleForm"
          destroy-on-close="true"
        >
          <el-form-item label="耗材类型" prop="printInfoType">
            <!-- <el-input v-model="form.printerModel" /> -->
            <el-radio-group v-model="paperType">
              <el-radio-button v-for="item in paperTypeData" :key="item.id+item.label" :label="item.id">{{ item.label }}</el-radio-button>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="耗材ID" prop="paperId">
            <el-input v-model="form.paperId" />
          </el-form-item>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="面纸宽度(mm)" prop="height">
                <el-input v-model="form.height" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="单张长度(mm)" prop="width">
                <el-input v-model="form.width" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="缝隙长度(mm)" prop="gapWidth">
                <el-input v-model="form.gapWidth" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-form-item label="图像颜色" prop="printerType">
            <el-col :span="2">
              <el-color-picker v-model="form.colorCode" />
            </el-col>
            <el-col :span="4">
              <el-input v-model="form.colorCode" />
            </el-col>
            <el-col :span="24" style="padding: 10px">
              快捷选择：
              <el-radio-group v-model="colorSelectCode">
                <el-radio-button v-for="item in colorSelectList" :key="item.value" :label="item.key" @click.native="colorSelect(item)">{{ item.value }}</el-radio-button>
              </el-radio-group>
            </el-col>
          </el-form-item>

          <el-form-item label="打印色值" prop="printColorCode">
            <el-col :span="2">
              <el-color-picker v-model="form.printColorCode" />
            </el-col>
            <el-col :span="4">
              <el-input v-model="form.printColorCode" />
            </el-col>
            <el-col v-show="paperType==='21' || paperType==='22'" :span="18">
              <el-form-item label="展示方式" prop="direction">
                <el-radio-group v-model="form.direction">
                  <el-radio-button label="1">面纸宽度 * 单张长度</el-radio-button>
                  <el-radio-button label="0">单张长度 * 面纸宽度</el-radio-button>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-form-item>

          <el-form-item label="适配打印机" prop="printerType">
            <!-- <div v-for="item in printerTypeConstantList" :key="item.value">
              {{ item.value }}
            </div> -->
            <el-checkbox-group v-model="printerType">
              <el-checkbox-button v-for="item in printerTypeConstantList" :key="item.value" :label="item.value">{{ item.value }}</el-checkbox-button>
            </el-checkbox-group>
          </el-form-item>

          <el-form-item label="RFID配置" prop="rfidFlag">
            <el-radio-group v-model="form.rfidFlag">
              <el-radio-button label="0">不带RFID</el-radio-button>
              <el-radio-button label="1">带RFID</el-radio-button>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="排序字段" prop="sortNum">
            <el-col :span="4">
              <el-input v-model="form.sortNum" />
            </el-col>
            <el-col :span="4">
              &nbsp;&nbsp;(由小到大排序)
            </el-col>
          </el-form-item>

          <el-divider content-position="center">打印范围（单位mm）</el-divider>
          <el-row :gutter="20">
            <el-col :span="5">
              <el-form-item label="X" prop="printX">
                <el-input v-model="form.printX" />
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item label="Y" prop="printY">
                <el-input v-model="form.printY" />
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item label="W" prop="printW">
                <el-input v-model="form.printW" />
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item label="H" prop="printH">
                <el-input v-model="form.printH" />
              </el-form-item>
            </el-col>
          </el-row>

          <el-divider />
          <div class="form-title">APP耗材显示内容</div>

          <el-form-item label="列表名称">
            <div class="form-inline">
              名称：<el-input v-model="form.zhTitle" style="width: 200px; margin-right: 50px" /> 代码：<el-input v-model="form.enTitle" style="width: 200px" />（用于国际化）
            </div>
            <div style="padding: 10px">
              <p />快捷选择：
              <el-radio-group v-model="colorI18n">
                <el-radio-button v-for="item in colorTypeAndI18NCodeList" :key="item.value" :label="item.key" @click.native="settingColor18NValue(item)">{{ item.value }}</el-radio-button>
              </el-radio-group>
            </div>
          </el-form-item>
          <el-row>
            <el-col :span="11">
              <el-form-item label="预览图：">
                <SingleUpload ref="paperInfoListUrl" key="listUrl" key-word="listUrl" :init-url="isEdit ? form.imageUrl: ''" @updateFile="updateFile" />
              </el-form-item>
            </el-col>
            <el-col :span="11">
              <el-form-item label="资源图：">
                <SingleUpload ref="paperInfoResUrl" key="resUrl" key-word="resUrl" :init-url="isEdit ? form.resUrl: '' " @updateFile="updateFile" />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row>
            <el-col :span="8">
              <el-form-item label="是否展示new" prop="shareFlag">
                <el-radio-group v-model="form.isNew">
                  <el-radio-button label="0">不展示</el-radio-button>
                  <el-radio-button label="1">展示</el-radio-button>
                </el-radio-group>
              </el-form-item>
            </el-col>

            <el-col :span="11">
              <el-form-item label="期限" prop="showtimeEnd">
                <el-date-picker
                  v-model="form.showtimeEnd"
                  type="datetime"
                  :value-format="timeFormat"
                  placeholder="选择日期时间"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-form-item>
            <el-button @click="showDialog = false">返回</el-button>
            <el-button v-permission="'btn-xeasylabel-paperinfo-edit'" type="primary" :loading="loading" @click="submitForm">保存</el-button>
          </el-form-item>
        </el-form>
      </el-dialog>
    </div>
  </div>
</template>
<script>
import SingleUpload from '../material/component/SingleUpload/index'
import { fetchListByPage, deleteRecord, saveRecord } from '@/api/xeasylabel/paperinfo.js'
import AliyunOSS from '@/utils/aliyunOSS'
const oss = new AliyunOSS()
export default {
  components: {
    SingleUpload
  },
  data() {
    return {
      colorI18n: '',
      timeFormat: 'yyyy-MM-dd HH:mm:ss',
      rfidFlag: 1,
      queryHeight: '',
      queryWidth: '',
      paperType: '12',
      colorSelectCode: '',
      printColorSelectCode: '',
      paperTypeData: [
        { 'id': '12', 'label': '连续普通纸（LP）' },
        { 'id': '11', 'label': '连续合成纸（LH）' },
        { 'id': '22', 'label': '间隙普通纸（JP）' },
        { 'id': '21', 'label': '间隙合成纸（JH）' }
      ],
      printerModel: '',
      currentPage: 1,
      currentSize: 10,
      total: 100,
      dataList: [],
      multipleSelection: [],
      // 弹框
      showDialog: false,
      // 批量
      showMuliDialog: false,
      // 仅上传文件模式
      onlyUpload: false,
      printerType: [],
      // 表单
      form: {
        id: '',
        channel: '',
        url: '',
        param: '',
        version: '',
        // printerType: [],
        remark: '',
        direction: 0
      },
      // 表单
      multiForm: {
        version: '',
        remark: ''
      },
      // 检验规则
      rules: {
        channel: [
          { required: true, message: '请输入渠道', trigger: 'blur' },
          { min: 3, max: 20, message: '长度在 3 到 20 个字符', trigger: 'blur' }
        ],
        url: [{ required: true, message: '请选择文件', trigger: 'blur' }],
        version: [
          { required: true, message: '请输入版本号如1.1.2', trigger: 'blur' }
        ]
      },
      multiFormRules: {
        version: [
          { required: true, message: '请输入版本号如1.1.2', trigger: 'blur' }
        ]
      },
      fileList: [],
      printerTypeConstantList: [
        { 'value': 'mp2', 'key': 'mp2' },
        { 'value': 'mp3-c', 'key': 'mp3-c' },
        { 'value': 'mp3', 'key': 'mp3' },
        { 'value': 'cp03', 'key': 'cp03' },
        { 'value': 'cp04', 'key': 'cp04' },
        { 'value': 'cp4', 'key': 'cp4' },
        { 'value': 'nt2', 'key': 'nt2' }
      ],
      colorTypeAndI18NCodeList: [
        { 'value': '白色', 'key': 'paper_info_white' },
        { 'value': '香蕉黄', 'key': 'paper_info_yellow_banana' },
        { 'value': '涧石蓝', 'key': 'paper_info_blue_vermilion' },
        { 'value': '竹叶绿', 'key': 'paper_info_green_bamboo' },
        { 'value': '樱花粉', 'key': 'paper_info_pink_sakura' },
        { 'value': '薰衣草紫', 'key': 'paper_info_purple_lavender' },
        { 'value': '浅橙色', 'key': 'paper_info_orange_light' },
        { 'value': '草莓多多', 'key': 'paper_info_strawberry' },
        { 'value': '蓝色气泡', 'key': 'paper_info_blue_bubble' },
        { 'value': '黄色爱心', 'key': 'paper_info_yellow_love' },
        { 'value': '粉色爱心', 'key': 'paper_info_pink_love' },
        { 'value': '白底蓝字', 'key': 'paper_info_white_bluefont' }
      ],
      colorSelectList: [
        { 'value': '白色', 'key': '#ffffff' },
        { 'value': '香蕉黄', 'key': '#ffe37a' },
        { 'value': '蓝涧石蓝', 'key': '#c0ecf3' },
        { 'value': '竹叶绿', 'key': '#caf2bf' },
        { 'value': '樱花粉', 'key': '#ffd7d5' },
        { 'value': '薰衣草紫', 'key': '#d6bfdd' },
        { 'value': '浅橙色', 'key': '#fdbe87' }
      ],
      file: undefined,
      loading: false,
      showUrl: false,
      printerTypeList: {},
      uploadFiles: { listUrl: {}, resUrl: {}},
      tableList: []
    }
  },

  computed: {
    isEdit() {
      if (this.form && this.form.id) {
        return true
      } else {
        return false
      }
    }
  },
  mounted() {
    this.getList()
  },
  methods: {
    /** 子组件上传文件 */
    updateFile(key, file) {
      this.uploadFiles[key] = {
        file,
        key
      }
    },
    formatListName(row) {
      let result = ''
      result += row.type === 1 ? 'L' : 'J'
      result += row.material === 1 ? 'H' : 'P'
      result += ' '

      const labelName = row.direction === 1 ? row.height + '*' + row.width + 'mm' : row.width + '*' + row.height + 'mm'

      result += row.type === 1 ? row.height + 'mm' : labelName
      result += ' ' + row.zhTitle
      return result
    },
    settingBg(row) {
      return { backgroundColor: row.colorCode }
    },
    formatWHInfo(row) {
      if (row.type === 1) {
        return row.height + 'mm'
      } else {
        return row.height + 'mm*' + row.width + 'mm'
      }
    },
    settingTabelListData() {
      if (this.dataList != null && this.dataList.length > 0) {
        const result = []
        const type = Number.parseInt(this.paperType.charAt(0))
        const material = Number.parseInt(this.paperType.charAt(1))
        const rfidFlag = Number.parseInt(this.rfidFlag)

        // let paperType = this.paperType
        this.dataList.forEach(item => {
          if (item.type === type && item.material === material && item.rfidFlag === rfidFlag) {
            let tmp = true
            if (this.queryHeight && this.queryHeight !== item.height + '') {
              tmp = false
            }
            if (this.queryWidth && this.queryWidth !== item.width + '') {
              tmp = false
            }
            if (tmp) {
              result.push(item)
            }
          }
        })
        this.tableList = result
      } else {
        this.tableList = []
      }
    },
    settingColor18NValue(obj) {
      this.form.zhTitle = obj.value
      this.form.enTitle = obj.key
    },
    colorSelect(obj) {
      this.form.colorCode = obj.key
    },
    /** 添加 */
    addVersion() {
      this.form = {}
      this.showDialog = true
      this.fileList = []
      this.file = undefined
      this.showUrl = false
      this.onlyUpload = false
      this.uploadFiles = { listUrl: {}, resUrl: {}}
      this.$refs.paperInfoListUrl.clearFile()
      this.$refs.paperInfoResUrl.clearFile()
    },
    /** 查询 */
    queryVersion() {
      this.getList()
    },
    /** 切换每页条数 */
    handleSizeChange(val) {
      console.log(`每页 ${val} 条`)
      this.currentSize = val
      this.getList()
    },
    /** 切换当前页 */
    handleCurrentChange(val) {
      console.log(`当前页: ${val}`)
      this.currentPage = val
      this.getList()
    },
    /** 获取表数据 */
    async getList() {
      const params = {
        // pageno: this.currentPage,
        // pagesize: this.currentSize,
        // printerModel: this.printerModel
      }
      const res = await fetchListByPage(params)
      if (res.head.ret === 0) {
        this.dataList = res.data
        this.settingTabelListData()
      }
    },
    clearSelection() {
      this.$refs.multipleTable.clearSelection()
    },
    /** 勾选事件 */
    handleSelectionChange(val) {
      this.multipleSelection = val
      console.log('handleSelectionChange: ' + this.multipleSelection)
    },
    /** 编辑 */
    handleEdit(index, row) {
      this.form = Object.assign({}, row)
      // this.form = row
      this.showDialog = true
      this.fileList = []
      this.file = undefined
      this.showUrl = true
      this.onlyUpload = false
      if (this.form.imageUrl) {
        this.uploadFiles.listUrl.pic = this.form.imageUrl
      } else {
        this.uploadFiles.listUrl.pic = ''
        this.uploadFiles.listUrl = {}
        this.$refs.paperInfoListUrl.clearFile()
      }

      if (this.form.resUrl) {
        this.uploadFiles.resUrl.pic = this.form.resUrl
      } else {
        this.uploadFiles.resUrl.pic = ''
        this.uploadFiles.resUrl = {}
        this.$refs.paperInfoResUrl.clearFile()
      }
      if (this.form.printerType) {
        this.printerType = this.form.printerType.split(';')
      } else {
        this.printerType = []
      }
    },
    /** 编辑 仅上传文件 */
    handleOnlyFile(index, row) {
      this.form = row
      this.showDialog = true
      this.fileList = []
      this.file = undefined
      this.showUrl = true
      this.onlyUpload = true
    },

    /** 删除单行 */
    async handleDeleteRow(index, row) {
      const params = {
        id: row.id
      }
      this.$confirm('此操作将永久删除该记录, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          deleteRecord(params).then(res => {
            if (res.head.ret === 0) {
              this.$message({
                type: 'success',
                message: '删除成功!'
              })
              this.getList()
            }
          })
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    },
    /** 删除单行 */
    async handleMultiDel() {
      this.$confirm('此操作将永久删除记录, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          let i = 0
          const len = this.multipleSelection.length
          for (i = 0; i < len; i++) {
            const params = {
              id: this.multipleSelection[i].id
            }
            deleteRecord(params).then(res => {
              if (res.head.ret === 0) {
                this.$message({
                  type: 'success',
                  message: '删除成功!'
                })
                this.getList()
              }
            })
          }
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    },

    // 表单编辑
    /** 保存 */
    submitForm() {
      if (this.printerType === null || this.printerType.length === 0) {
        this.$message.error('请选择适配机型')
        return
      }
      const upFiles = []
      const picDto = {}
      Object.keys(this.uploadFiles).forEach(key => {
        const item = this.uploadFiles[key]
        if (item.file) {
          item.file.key = key
          upFiles.push(item.file)
        } else {
          picDto[key] = { pic: item.pic }
        }
      })
      // 上传
      oss.uploadFiles(upFiles, (results, error) => {
        if (error) {
          this.$message.error('文件上传失败，请检查')
          return
        }
        if (results) {
          results.forEach(res => {
            picDto[res.key] = { pic: res.name }
          })
        }

        this.form.printerType = this.printerType.join(';')

        this.form.type = this.paperType.charAt(0)
        this.form.material = this.paperType.charAt(1)

        this.form.resUrl = picDto.resUrl.pic
        this.form.imageUrl = picDto.listUrl.pic

        // 保存

        saveRecord(this.form).then(res => {
          if (res.head.ret === 0) {
            this.$message.success('保存成功')
            this.showDialog = false
            this.getList()
          } else {
            this.$message.error(res.head.msg)
          }
        })
      })
    },
    /** 点击批量修改 */
    handleMultiModify() {
      this.showMuliDialog = true
    },
    /** 批量保存 */
    submitMultiForm() {
      this.$refs['ruleMultiForm'].validate(valid => {
        if (valid) {
          this.loading = true
          console.log('this.multiForm: ', this.multiForm)
          this.doMultiSave()
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    async doMultiSave() {
      let i = 0
      const len = this.multipleSelection.length
      let count = 0
      for (i = 0; i < len; i++) {
        const data = { ...this.multipleSelection[i] }
        data.version = this.multiForm.version
        data.remark = this.multiForm.remark
        const res = await saveRecord(data)
        if (res.head.ret === 0) {
          count++
        }
      }
      this.$message.success('保存成功[' + count + ']条, 共' + len + '条')
      this.loading = false
      this.showMuliDialog = false
      this.getList()
    },

    /** 移除之前 */
    handleRemove(file, fileList) {
      console.log('handleRemove')
      this.showUrl = true
    },
    /** 上传前 */
    beforeUpload(file) {
      return false
    },
    changeFile(file) {
      console.log('changeFile')
      console.log(file)
      this.file = file.raw
      this.fileList.pop()
      this.fileList.push({
        name: file.name,
        url: file.name
      })
      this.showUrl = false
    }
  }
}
</script>

<style lang="scss" scoped>
.app-container {
  margin: 20px;
  .control-container {
    margin-bottom: 20px;
  }
  .choose-container {
    margin-bottom: 20px;
    background-color: #e6f7ff;
    border: 1px solid #91d5ff;

    span {
      margin-left: 20px;
      line-height: 48px;
    }
    a {
      margin-left: 20px;
      color: #1890ff;
      text-decoration: none;
      background-color: transparent;
      outline: none;
      cursor: pointer;
      transition: color 0.3s;
    }
  }
  .form-title {
        font-size: 22px;
    line-height: 60px;
    padding-left: 40px;
    font-weight: 700;
  }
  .form-inline {
    display: flex;
  }
}
</style>
