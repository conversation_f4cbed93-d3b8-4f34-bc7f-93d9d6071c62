<template>
  <div v-if="visiable" class="edit-dialog">
    <el-dialog
      ref="dialog"
      :show-close="false"
      :title="form.id === '' ? '新增' : '编辑' "
      :visible.sync="visiable"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      width="80%"
      center
    >
      <el-form ref="form" :model="form" :rules="rules" label-width="100px" class="edit-form">
        <el-form-item label="类型" prop="type">
          <el-radio-group v-model="helpType">
            <el-radio-button label="video">教程</el-radio-button>
            <el-radio-button label="text">常见问题</el-radio-button>
            <el-radio-button label="pdf">说明书</el-radio-button>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="类型" prop="paperType">
          <el-radio-group v-model="printerPaperType" @change="printerTypeLabel=[]">
            <el-radio-button label="1">口袋打印机</el-radio-button>
            <el-radio-button label="2">A4打印机</el-radio-button>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="打印机型号" prop="printerType">
          <el-checkbox-group v-model="printerTypeLabel">
            <el-checkbox-button v-for="item in printerTypeConstantList" v-show="printerPaperType===item.type" :key="item.value" :label="item.value">{{ item.name }}</el-checkbox-button>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="标题" prop="title">
          <el-input v-model="form.title" />
        </el-form-item>
        <!-- <el-form-item label="地区语言" prop="localLanguageCode">
          <el-checkbox-group v-model="localLanguageCodeLabel">
            <el-checkbox-button v-for="item in localeData" :key="item.id" :label="item.id">{{ item.name }}</el-checkbox-button>
          </el-checkbox-group>
        </el-form-item> -->
        <el-form-item label="封面" prop="coverUrl">
          <el-upload
            class="avatar-uploader"
            action=""
            :show-file-list="false"
            :on-change="changePic"
            :before-upload="beforeUpload"
            accept="image/png, image/jpeg"
          >
            <img v-if="form.coverUrl" :src="form.coverUrl" class="avatar">
            <i v-else class="el-icon-plus avatar-uploader-icon" />
          </el-upload>
          <img v-show="false" :src="testUrl" class="avatar">
        </el-form-item>
        <el-form-item label="url地址" prop="version">
          <el-input
            v-model="form.url"
            type="text"
            maxlength="200"
          />
        </el-form-item>
        <el-form-item label="排序" prop="sortNum">
          <el-input
            v-model="form.sortNum"
            type="text"
          />
        </el-form-item>
        <el-form-item>
          <el-button @click="handleClose">返回</el-button>
          <el-button v-permission="'btn-menuSystemsetting-help-edit'" type="primary" @click="submit">保存</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import { addItem, updateItem } from '@/api/systemhelp/systemhelp'
import { printerTypeConstantList } from '@/consts/index'
import AliyunOSS from '@/utils/aliyunOSS'
const oss = new AliyunOSS()
export default {
  name: 'EditDialog',
  props: {
    visiable: {
      type: Boolean,
      required: true,
      default: false
    },
    formData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      /** 表单 */
      form: {
        paperType: '1',
        type: 'video'
      },
      /** 校验规则 */
      rules: {
        title: [
          { required: true, message: '请输入标题', trigger: 'blur' }
        ],
        url: [
          { required: true, message: '请输入h5存放地址', trigger: 'blur' }
        ]
      },
      /** 待上传的文件 */
      upFile: undefined,
      testUrl: '',
      /** 时间格式 */
      timeFormat: 'yyyy-MM-dd HH:mm:ss',
      showDialog: false,
      printerPaperType: '1',
      printerTypeLabel: [],
      helpType: 'video',
      printerTypeConstantList: printerTypeConstantList
      // localLanguageCodeLabel: [],
      // localeData: [{ 'id': 'zh_CN', 'name': '简体' },
      //   { 'id': 'zh_TW', 'name': '繁体' },
      //   { 'id': 'en_US', 'name': '英语' },
      //   { 'id': 'ko_KR', 'name': '韩语' },
      //   { 'id': 'ja_JP', 'name': '日语' },
      //   { 'id': 'ru_RU', 'name': '俄语' },
      //   { 'id': 'fr_FR', 'name': '法语' },
      //   { 'id': 'de_DE', 'name': '德语' },
      //   { 'id': 'it_IT', 'name': '意大利语' },
      //   { 'id': 'es_LA', 'name': '西班牙(拉美)' },
      //   { 'id': 'es_ES', 'name': '西班牙(欧洲)' }]
    }
  },
  computed: {
    printerTypeList: function() {
      return this.printerTypeConstantList.filter(item => {
        return item.type === this.printerPaperType
      })
    }
  },
  watch: {
    formData: function(val) {
      this.upFile = undefined
      this.form = { ...val }

      // if (this.form.localLanguageCode) {
      //   this.localLanguageCodeLabel = this.form.localLanguageCode.split(',')
      // } else {
      //   this.localLanguageCodeLabel = []
      // }

      if (val && (this.form.type === null || this.form.type === '' || this.form.type === undefined)) {
        this.form.type = 'video'
      } else {
        this.helpType = this.form.type
      }

      // if (this.form.printerType && this.form.printerType.search('BQ1') !== -1 && this.form.printerType.search('TP6') !== -1 && this.form.printerType.search('TP6-S') !== -1) {
      //   this.printerPaperType = '1'
      // } else {
      //   this.printerPaperType = '2'
      // }
      if (this.form.printerType) {
        const tempPrinterTypeLabel = this.form.printerType.split(',')
        tempPrinterTypeLabel.forEach(item => {
          let temp = item
          if (temp !== 'tp1-y' && temp !== 'tp2-y') {
            temp = (temp === 'tp1' || temp === 'tp1-y') ? 'tp1,tp1-y' : temp
            temp = (temp === 'tp2' || temp === 'tp2-y') ? 'tp2,tp2-y' : temp
            this.printerTypeLabel.push(temp)
          }
        })
      } else {
        this.printerTypeLabel = []
      }
    }
  },
  mounted() {
    this.show = this.visiable
  },
  methods: {
    init(printerType) {
      console.log('printerType=', printerType)
      if (printerType) {
        this.printerTypeLabel = printerType.split(',')
      } else {
        this.printerTypeLabel = []
      }
      if (printerType && (printerType.indexOf('BQ1') > -1 || printerType.indexOf('TP6') > -1 || printerType.indexOf('TP6-S') > -1)) {
        this.printerPaperType = '2'
      } else {
        this.printerPaperType = '1'
      }
      if (this.form.type === null || this.form.type === '' || this.form.type === undefined) {
        this.from.type = 'video'
      } else {
        this.helpType = this.form.type
      }
      console.log('printerTypeLabel=', this.printerTypeLabel)
    },
    /** 返回 */
    handleClose(code) {
      this.$emit('closeDialog', code)
    },
    /** 提交 */
    submit() {
      // this.addUpdateRecord()

      // 处理打印机型号
      if (this.printerTypeLabel) {
        this.form.printerType = this.printerTypeLabel.join(',')
      } else {
        this.form.printerType = ''
      }

      // 处理地区语言
      // if (this.localLanguageCodeLabel) {
      //   this.form.localLanguageCode = this.localLanguageCodeLabel.join(',')
      // } else {
      //   this.form.localLanguageCode = ''
      // }

      this.form.type = this.helpType

      if (this.upFile) {
        oss.uploadFile(this.upFile, (result, error) => {
          if (error) {
            this.$message.error('上传文件失败')
            return -1
          }
          if (result) {
            console.log('上传完图片后返回的数据', result)
            this.form.coverUrl = result.name
            this.addUpdateRecord()
          }
        })
      } else {
        // 直接保存
        this.addUpdateRecord()
      }
    },
    /** 选择图片*/
    changePic(file) {
      this.upFile = file.raw
      this.form.coverUrl = URL.createObjectURL(file.raw)
      this.testUrl = URL.createObjectURL(file.raw)
      return false
    },
    /** 上传前 */
    beforeUpload(file) {
      return false
    },
    async addUpdateRecord() {
      // 新增
      if (!this.form.id) {
        this.form.id = ''
        // 新增
        const res = await addItem(this.form)
        if (res.head.ret === 0) {
          this.$message.success('保存成功')
          this.handleClose('1')
        }
      } else {
        // 更新
        const res = await updateItem(this.form)
        if (res.head.ret === 0) {
          this.$message.success('保存成功')
          this.handleClose('1')
        }
      }
    }
  }

}
</script>

<style lang='scss' scoped>
  .avatar-uploader .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }
  .avatar-uploader .el-upload:hover {
    border-color: #409EFF;
  }
  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 178px;
    height: 178px;
    line-height: 178px;
    text-align: center;
  }
  .avatar {
    width: 178px;
    height: 178px;
    display: block;
  }
</style>
