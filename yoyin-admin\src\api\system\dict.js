import request from '@/utils/request'

// 接口URL定义
const DICT_LIST_URL = '/platform/gam/community/v1/dict/list'
const DICT_PAGELIST_URL = '/platform/gam/community/v1/dict/pagelist'
const DICT_SAVE_URL = '/platform/gam/community/v1/dict/save'
const DICT_DELETE_URL = '/platform/gam/community/v1/dict/delete'

// 获取字典分页列表（用于字典管理页面）
export function fetchDictPageList(params) {
  return request({
    url: DICT_PAGELIST_URL,
    method: 'get',
    params
  })
}

// 获取字典列表（用于下拉选择，返回全部数据）
export function fetchDictList(params) {
  return request({
    url: DICT_LIST_URL,
    method: 'get',
    params
  })
}

// 保存或更新字典
export function saveDictItem(data) {
  return request({
    url: DICT_SAVE_URL,
    method: 'post',
    data
  })
}

// 删除字典
export function deleteDictItem(id) {
  return request({
    url: DICT_DELETE_URL,
    method: 'post',
    data: { id }
  })
}


