<!--
 * @Description: In User Settings Edit
 * @Author: your name
 * @Date: 2019-10-16 11:28:20
 * @LastEditTime: 2019-10-16 11:38:48
 * @LastEditors: Please set LastEditors
 -->
<template>
  <div class="app-container">
    <div class="table-query-container">
      <el-button @click="handleAdd">新增</el-button>
    </div>

    <div class="table-container">
      <el-table
        ref="multipleTable"
        :data="list"
        tooltip-effect="dark"
        style="width: 100%"
        border
      >
        <el-table-column label="排序">
          <template slot-scope="scope">{{ scope.row.sort }}</template>
        </el-table-column>
        <el-table-column label="id" width="55">
          <template slot-scope="scope">{{ scope.row.id }}</template>
        </el-table-column>
        <el-table-column label="pId" prop="pId" />
        <el-table-column label="名称">
          <template slot-scope="scope">{{ scope.row.name }}</template>
        </el-table-column>
        <el-table-column prop="type" label="类型" />
        <el-table-column prop="subType" label="父类型" />
        <el-table-column prop="hasContent" label="是否有内容" />
        <el-table-column prop="showName" label="显示资源别名" />
        <el-table-column label="icon">
          <template slot-scope="scope">{{ scope.row.icon }}
          </template>
        </el-table-column>
        <el-table-column label="position">
          <template slot-scope="scope">{{ scope.row.position }}
          </template>
        </el-table-column>
        <el-table-column label="模板组件名">
          <template slot-scope="scope">{{ scope.row.com }}
          </template>
        </el-table-column>
        <el-table-column label="zhTitle">
          <template slot-scope="scope">{{ scope.row.zhTitle }}
          </template>
        </el-table-column>
        <el-table-column label="enTitle">
          <template slot-scope="scope">{{ scope.row.enTitle }}
          </template>
        </el-table-column>

        <el-table-column label="操作">
          <template slot-scope="scope">
            <el-button size="mini" @click="handleEdit(scope.$index, scope.row)">编辑</el-button>
            <el-button
              size="mini"
              type="danger"
              @click="handleDeleteRow(scope.$index, scope.row)"
            >删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <el-dialog :append-to-body="true" title="编辑" :visible.sync="showDialog" center :show-close="true" :destroy-on-close="true">
      <el-form ref="form" :model="form" :rules="rules" label-width="100px" class="edit-form">
        <el-form-item label="排序" prop="sort">
          <el-input v-model="form.sort" />
        </el-form-item>
        <el-form-item label="pId" prop="pId">
          <el-input v-model="form.pId" />
        </el-form-item>
        <el-form-item label="名称" prop="name">
          <el-input v-model="form.name" />
        </el-form-item>
        <el-form-item label="类型" prop="type">
          <el-input v-model="form.type" />
        </el-form-item>
        <el-form-item label="副类型" prop="subType">
          <el-input v-model="form.subType" />
        </el-form-item>
        <el-form-item label="是否有内容" prop="hasContent">
          <el-input v-model="form.hasContent" />
        </el-form-item>
        <el-form-item label="显示资源别名" prop="showName">
          <el-input v-model="form.showName" />
        </el-form-item>
        <!-- <el-form-item label="icon" prop="icon">
          <el-input v-model="form.icon" />
        </el-form-item> -->
        <el-form-item label="模板组件名" prop="com">
          <el-input v-model="form.com" />
        </el-form-item>
        <el-form-item label="zhTitle" prop="zhTitle">
          <el-input v-model="form.zhTitle" />
        </el-form-item>
        <el-form-item label="enTitle" prop="enTitle">
          <el-input v-model="form.enTitle" />
        </el-form-item>
        <el-form-item>
          <el-button @click="closeDialog">返回</el-button>
          <el-button type="primary" @click="submit">保存</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>
<script>
import { findListTab, deleteType, addOrUpdateMaterialType } from '@/api/community/material'
export default {
  data() {
    return {
      // 查询参数
      params: {
        startdate: '',
        enddate: '',
        pageno: 1,
        pagesize: 10,
        column: 'home'
      },
      /** 列表 */
      list: [],
      /** 总条数 */
      total: 0,
      /** 消息体 */
      form: { },
      /** 勾选数据 */
      multipleSelection: [],
      /** 时间段 */
      timeRange: '',
      /** 时间格式 */
      timeFormat: 'yyyy-MM-dd',
      /** 显示编辑框 */
      showDialog: false
    }
  },
  mounted() {
    this.getList()
  },
  methods: {
    /** 获取列表 */
    async getList() {
      const res = await findListTab(this.params)
      if (res.head.ret === 0) {
        this.list = res.data
      }
    },
    /** 编辑 */
    handleEdit(index, row) {
      this.form = row
      this.showDialog = true
    },
    /** 删除单行 */
    async handleDeleteRow(index, row) {
      const params = {
        id: row.id
      }
      this.$confirm('此操作将永久删除该记录, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteType(params).then(res => {
          if (res.head.ret === 0) {
            this.$message({
              type: 'success',
              message: '删除成功!'
            })
            this.getList()
          }
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
    },
    /** 新增 */
    handleAdd() {
      this.form = {}
      this.showDialog = true
    },
    /** 保存 */
    async submit() {
      // 新增
      if (!this.form.id) {
        this.form.id = ''
      }
      // 更新
      const res = await addOrUpdateMaterialType(this.form)
      if (res.head.ret === 0) {
        this.$message.success('保存成功')
        this.closeDialog()
        this.getList()
      }
    },
    /** 关闭弹框 */
    closeDialog(code) {
      this.showDialog = false
      this.form = {}
    }
  }
}
</script>

<style lang="scss" scoped>

</style>
