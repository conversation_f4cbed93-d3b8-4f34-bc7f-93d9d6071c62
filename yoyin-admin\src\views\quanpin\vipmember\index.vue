<template>
  <div class="app-container">
    <div class="table-query-container">
      <span class="demonstration" style="padding-right: 10px">用户账号/昵称/ID:</span>
      <el-select
        v-model="params.createUserId"
        filterable
        remote
        clearable
        reserve-keyword
        placeholder="请输入关键词"
        :remote-method="queryUserList"
        :loading="loading"
      >
        <el-option
          v-for="item in userQueryList"
          :key="item.id"
          :label="item.value"
          :value="item.id"
        />
      </el-select>

      <span class="demonstration" style="padding-right: 10px">会员状态</span>
      <el-select v-model="params.memberStatus" placeholder="请选择">
        <el-option label="全部" value="" />
        <el-option label="服务中" value="服务中" />
        <el-option label="已过期" value="已过期" />
      </el-select>
      <span class="demonstration" style="padding-right: 10px">会员有效期:</span>
      <el-date-picker
        v-model="timeRange"
        :value-format="timeFormat"
        :format="timeFormat"
        :unlink-panels="true"
        type="datetimerange"
        :clearable="false"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        @change="changeTimeRange"
      />
      <el-button type="primary" @click="getList">查询</el-button>
    </div>

    <div class="table-container">
      <el-table
        ref="multipleTable"
        :data="list"
        tooltip-effect="dark"
        style="width: 100%"
        border
        @sort-change="sortChange"
      >
        <el-table-column label="头像">
          <template slot-scope="scope">
            <img :src="scope.row.userPic" style="width:60px; height:60px;">
          </template>
        </el-table-column>
        <el-table-column label="用户id">
          <template slot-scope="scope">{{ scope.row.userCode }}</template>
        </el-table-column>
        <el-table-column label="用户账号">
          <template slot-scope="scope">{{ scope.row.userAccount }}</template>
        </el-table-column>
        <el-table-column label="用户昵称">
          <template slot-scope="scope">{{ scope.row.userName }}</template>
        </el-table-column>
        <el-table-column label="会员有效期">
          <template slot-scope="scope">{{ scope.row.limitDate }}</template>
        </el-table-column>
        <el-table-column label="会员状态">
          <template slot-scope="scope">{{ scope.row.memberStatus }}</template>
        </el-table-column>
        <el-table-column label="会员时长(天)">
          <template slot-scope="scope">{{ scope.row.limitCount }}</template>
        </el-table-column>
        <el-table-column label="获取卷量">
          <template slot-scope="scope">{{ scope.row.fatchCount }}</template>
        </el-table-column>
        <el-table-column label="打印总数" sortable="true" property="printCount">
          <template slot-scope="scope">{{ scope.row.printCount }}</template>
        </el-table-column>
        <!-- <el-table-column label="操作" width="230" align="center">
          <template slot-scope="scope" />
        </el-table-column> -->
      </el-table>
    </div>
    <div class="pagination-container" style="margin-top:20px;">
      <el-pagination
        :current-page="params.pageNo"
        :page-sizes="[10, 20, 30, 40]"
        :page-size="params.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>
<script>

import { findUserInfoByKeyword } from '@/api/user/list'
import { fetchVipListByPage } from '@/api/quanpin/membervip'

export default {
  data() {
    return {
      timeRange: [],
      // 查询参数
      params: {
        pageNo: 1,
        pageSize: 10,
        type: ''
      },
      /** 列表 */
      list: [],
      userQueryList: [],
      /** 总条数 */
      total: 0,
      showDialog: false,
      /** 时间格式 */
      timeFormat: 'yyyy-MM-dd',
      loading: false
    }
  },
  mounted() {
    this.getList()
  },
  methods: {
    /** 获取列表 */
    async getList() {
      const res = await fetchVipListByPage(this.params)
      if (res.head.ret === 0) {
        this.list = res.data.result
        this.total = res.data.totalCount
      }
    },
    /** 切换每页条数 */
    handleSizeChange(val) {
      this.params.pageSize = val
      this.getList()
    },
    /** 切换当前页 */
    handleCurrentChange(val) {
      this.params.pageNo = val
      this.getList()
    },
    /** 选择时间段 */
    changeTimeRange(val) {
      if (val) {
        this.params.payBeginTime = val[0]
        this.params.payEndTime = val[1]
      }
    },
    queryUserList(e) {
      const params = {
        keyword: e
      }
      findUserInfoByKeyword(params).then(res => {
        if (res.head.ret === 0 && res.data.length > 0) {
          this.userQueryList = res.data
        }
        // if (res.head.ret === 0 && res.data.result.length > 0) {
        //   this.userQueryList = res.data.result
        // } else {
        //   delete params['codeId']
        //   params['nickName'] = e
        //   fetchUserListByPage(params).then(res2 => {
        //     if (res2.head.ret === 0) {
        //       this.userQueryList = res2.data.result
        //     }
        //   })
        // }
      })
    },
    sortChange(e) {
      console.log('点击了sortChange', e)
    }
  }
}
</script>
<style lang="scss" scoped>

</style>
