<!--
 * @Description: In User Settings Edit
 * @Author: your name
 * @Date: 2019-10-16 11:28:20
 * @LastEditTime: 2019-10-16 11:38:48
 * @LastEditors: Please set LastEditors
 -->
<template>
  <div class="app-container">
    <div class="table-query-container">
      <span class="demonstration">有效期</span>
      <el-date-picker
        v-model="timeRange"
        :value-format="timeFormat"
        :format="timeFormat"
        :unlink-panels="true"
        type="datetimerange"
        :clearable="false"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        @change="changeTimeRange"
      />
      <el-button type="primary" @click="handleQuery">查询</el-button>
      <el-button v-permission="'btn-menu2cunkuan-knowledge-edit'" @click="handleAdd">新增</el-button>
      <el-button v-permission="'btn-menu2cunkuan-knowledge-edit'" @click="exportList">导出</el-button>
    </div>
    <div style="padding-bottom: 15px">
      <el-radio-group v-model="params.subject" @change="getList">
        <el-radio-button label="">所有</el-radio-button>
        <el-radio-button v-for="item in subjectList" :key="item.id" :label="item.id">{{ item.name }}</el-radio-button>
      </el-radio-group>
    </div>
    <div class="dialog-container">
      <EditDialog :visiable="showDialog" :form-data="banner" @closeDialog="closeDialog" />
    </div>

    <!-- <div class="table-select-container">
      <div class="select-count">当前选择<span style="color: #409EFF"> {{ multipleSelection.length }} </span>项
        <a @click="handleClearSelection">清空</a>
        <a v-if="multipleSelection.length > 0" @click="handelMultiDel">删除</a>
      </div>
    </div> -->
    <div class="table-container">
      <el-table
        ref="multipleTable"
        :data="list"
        tooltip-effect="dark"
        style="width: 100%"
        border
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column label="封面">
          <template slot-scope="scope">
            <a :href="scope.row.headUrl" target="_blank">
              <el-avatar shape="square" :size="80" :src="scope.row.headUrl" />
            </a>
          </template>
        </el-table-column>
        <el-table-column label="标题">
          <template slot-scope="scope">{{ scope.row.title }}</template>
        </el-table-column>
        <el-table-column label="图片内容">
          <template slot-scope="scope">
            <a :href="'https://m.starpany.cn/test/h5/feed/views/knowledge-preview.html?id='+scope.row.id" target="_blank">
              预览
            </a>
          </template>
        </el-table-column>
        <el-table-column label="科目">
          <template slot-scope="scope">{{ scope.row.subject }}</template>
        </el-table-column>
        <el-table-column label="年级">
          <template slot-scope="scope">
            {{ (scope.row.gradeLevel && scope.row.gradeLevel.indexOf('xx')!=-1) ? '小学&nbsp;' : '' }}
            {{ (scope.row.gradeLevel && scope.row.gradeLevel.indexOf('cz')!=-1) ? '初学&nbsp;' : '' }}
            {{ (scope.row.gradeLevel && scope.row.gradeLevel.indexOf('gz')!=-1) ? '高学&nbsp;' : '' }}
            {{ (scope.row.gradeLevel === undefined || scope.row.gradeLevel === null || scope.row.gradeLevel === '') ? '无' : '' }}
          </template>
        </el-table-column>
        <el-table-column label="排序号">
          <template slot-scope="scope">{{ scope.row.sortNum }}</template>
        </el-table-column>
        <el-table-column label="点击次数">
          <template slot-scope="scope">{{ scope.row.clickNum }}</template>
        </el-table-column>
        <el-table-column label="分享次数">
          <template slot-scope="scope">{{ scope.row.shareNum }}</template>
        </el-table-column>
        <el-table-column label="打印册数">
          <template slot-scope="scope">{{ scope.row.printNum }}</template>
        </el-table-column>
        <el-table-column label="操作">
          <template slot-scope="scope">
            <el-button size="mini" @click="handleEdit(scope.$index, scope.row)">编辑</el-button>
            <el-button
              v-permission="'btn-menu2cunkuan-knowledge-edit'"
              size="mini"
              type="danger"
              @click="handleDeleteRow(scope.$index, scope.row)"
            >删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="pagination-container" style="margin-top:20px;">
      <el-pagination
        :current-page="params.pageno"
        :page-sizes="[10, 20, 30, 40]"
        :page-size="params.pagesize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>
<script>
import { fetchListByPage, deleteRecord, getSubject, exportKnowledgeList } from '@/api/community/knowledge'
import EditDialog from './component/EditDialog'
export default {
  components: {
    EditDialog
  },
  data() {
    return {
      // 查询参数
      params: {
        pageno: 1,
        pagesize: 10
      },
      /** 列表 */
      list: [],
      /** 总条数 */
      total: 0,
      /** 消息体 */
      banner: { },
      /** 勾选数据 */
      multipleSelection: [],
      /** 时间段 */
      timeRange: '',
      /** 时间格式 */
      timeFormat: 'yyyy-MM-dd',
      /** 显示编辑框 */
      showDialog: false,
      /** 科目列表 */
      subjectList: []
    }
  },
  mounted() {
    this.getList()
    this.getSubjectList()
  },
  methods: {
    /** 获取列表 */
    async getList() {
      const res = await fetchListByPage(this.params)
      if (res.head.ret === 0) {
        this.list = res.data.result
        this.total = res.data.totalCount
      }
    },
    async getSubjectList() {
      const res = await getSubject()
      if (res.head.ret === 0) {
        this.subjectList = res.data
      }
    },
    /** 切换每页条数 */
    handleSizeChange(val) {
      this.params.pagesize = val
      this.getList()
    },
    /** 切换当前页 */
    handleCurrentChange(val) {
      this.params.pageno = val
      this.getList()
    },
    /** 编辑 */
    handleEdit(index, row) {
      this.banner = row
      this.showDialog = true
    },
    /** 删除单行 */
    async handleDeleteRow(index, row) {
      const params = {
        id: row.id
      }
      this.$confirm('此操作将永久删除该记录, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteRecord(params).then(res => {
          if (res.head.ret === 0) {
            this.$message({
              type: 'success',
              message: '删除成功!'
            })
            this.getList()
          }
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
    },
    /** 多选删除 */
    handelMultiDel() {
      this.$confirm('此操作将永久删除选中记录, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const rows = [...this.multipleSelection]
        rows.forEach(item => {
          const params = { id: item.id }
          deleteRecord(params).then(res => {
            if (res.head.ret === 0) {
              this.$message.success('删除成功')
              this.getList()
            }
          })
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
    },
    /** 勾选事件 */
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    /** 清空勾选 */
    handleClearSelection() {
      this.$refs.multipleTable.clearSelection()
    },
    /** 跳转类型 转换 */
    jumpTypeFormat(row, column) {
      const code = parseInt(row.jumpType)
      if (code === 100) {
        return '无跳转'
      } else if (code === 101) {
        return 'H5'
      } else if (code === 102) {
        return '消息中心'
      } else if (code === 103) {
        return '纸条消息'
      } else if (code === 104) {
        return '共享打印'
      }
      return '未知类型'
    },
    /** 栏目类型 转换 */
    columnTypeFormat(row, column) {
      const code = row.column
      if (code === 'home') {
        return '首页'
      } else if (code === 'other') {
        return '其他'
      }
      return '未知类型'
    },
    /** 选择时间段 */
    changeTimeRange(val) {
      this.params.startdate = val[0]
      this.params.enddate = val[1]
    },
    /** 查询 */
    handleQuery() {
      this.getList()
    },
    /** 新增 */
    handleAdd() {
      this.banner = {}
      this.showDialog = true
    },
    /** 关闭弹框 */
    closeDialog(code) {
      this.showDialog = false
      this.banner = {}
      // 保存code
      if (code === '1') {
        this.getList()
      }
    },
    async exportList() {
      const loading = this.$loading({
        lock: true,
        text: 'Loading',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })

      const res = await exportKnowledgeList(this.params)
      if (res.head.ret === 0) {
        const a = document.createElement('a')
        a.href = res.data // 这里的请求方式为get，如果需要认证，接口上需要带上token
        a.click()
      }
      loading.close()
    }
  }
}
</script>

<style lang="scss" scoped>

</style>
