<template>
  <div class="app-container">
    <div class="table-query-container">
      <div class="query-code-btn">
        <span class="demonstration">有效期</span>
        <el-date-picker
          v-model="activityDate"
          type="daterange"
          align="right"
          unlink-panels
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd"
          :picker-options="pickerOptions"
          @change="handleChangeAtivityDate"
        />
        前几名
        <el-input-number v-model="rankTop" />
        <el-button size="medium" style="margin-left:20px;" @click="getList">查询</el-button>
        <el-button v-permission="'btn-menuCoin-statisticscoin-edit'" size="medium" style="margin-left:20px;" @click="exportx">导出</el-button>
      </div>
    </div>
    <div class="table-container">
      <el-table
        ref="multipleTable"
        :data="list"
        tooltip-effect="dark"
        style="width: 100%"
        border
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column label="名次" width="55">
          <template slot-scope="scope">{{ scope.row.rank }}</template>
        </el-table-column>
        <el-table-column label="用户">
          <template slot-scope="scope">{{ scope.row.userInfoDto.name }}</template>
        </el-table-column>
        <el-table-column label="用户id">
          <template slot-scope="scope">{{ scope.row.userInfoDto.codeId }}</template>
        </el-table-column>
        <el-table-column label="新增积分数量">
          <template slot-scope="scope">{{ scope.row.coinCount }}</template>
        </el-table-column>
      </el-table>
    </div>
    <div class="dialog-container">
      <el-dialog :visible.sync="showDialog" title="发送奖励消息">
        <el-form ref="form" :model="form" label-width="120px">
          <el-form-item label="奖励金额">
            <el-input-number v-model="form.num" />
          </el-form-item>
          <el-form-item label="官方消息内容">
            <el-input v-model="form.info" type="textarea" />
          </el-form-item>
          <el-form-item>
            <el-button @click="closeDialog">关闭</el-button>
            <el-button v-permission="'btn-menuCoin-statisticscoin-edit'" type="primary" @click="submit">保存</el-button>
          </el-form-item>
        </el-form>
      </el-dialog>
    </div>
  </div>
</template>
<script>
import { exportRankListByPage, fetchRankListByPage, giveCoinCurrRecord } from '@/api/coin/analysis'
import { parseTime } from '@/utils/index'
// import EditDialog from './component/EditDialog'
export default {
  name: 'GoodsList',
  components: {
    // EditDialog
  },
  data() {
    return {
      // 查询参数
      params: {
        pageno: 1,
        pagesize: 10
      },
      /** 列表 */
      list: [],
      /** 总条数 */
      total: 0,
      rankTop: 10,
      showDialog: false,
      goodsObj: {},
      form: {
        num: 100,
        info: '',
        userId: ''
      },
      sendUserArr: {},
      activityDate: [],
      pickerOptions: {
        shortcuts: [{
          text: '最近一周',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近一个月',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近三个月',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
            picker.$emit('pick', [start, end])
          }
        }]
      }
    }
  },
  mounted() {
    const end = new Date('2021-02-18')
    const start = new Date('2021-02-08')
    // start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
    this.activityDate = [parseTime(start, '{y}-{m}-{d}'), parseTime(end, '{y}-{m}-{d}')]
    this.getList()
  },
  methods: {
    /** 获取列表 */
    async getList() {
      const parms = {
        beginDate: this.activityDate[0],
        endDate: this.activityDate[1],
        top: this.rankTop
      }
      const res = await fetchRankListByPage(parms)
      if (res.head.ret === 0) {
        this.list = res.data
        // this.total = res.data.totalCount
      }
    },
    submit() {
      if (this.form.num === 0) {
        this.$message.error('请输入积分数量')
        return
      }
      if (this.form.info === '' || this.form.info.trim() === '') {
        this.$message.error('请输入官方消息内容')
        return
      }
      this.form.userIds = ''
      this.sendUserArr.forEach(element => {
        this.form.userIds += element.userInfoDto.userId + ';'
      })
      this.$confirm('此操作将奖励并发送官方消息给用户, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const loading = this.$loading({
          lock: true,
          text: '提交中',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        })
        giveCoinCurrRecord(this.form)
        setTimeout(() => {
          loading.close()
          this.closeDialog()
          this.$message({
            type: 'success',
            message: '操作成功!'
          })
        }, 3000)
      }).catch(() => {

      })
    },
    /** 切换每页条数 */
    handleSizeChange(val) {
      this.params.pagesize = val
      this.getList()
    },
    /** 切换当前页 */
    handleCurrentChange(val) {
      this.params.pageno = val
      this.getList()
    },
    handleEdit(index, row) {
      this.goodsObj = row
      this.showDialog = true
    },
    handleAdd() {
      this.goodsObj = {}
      this.showDialog = true
    },
    handleChangeAtivityDate(val) {
      this.activityDate = val
      this.getList()
    },
    showSendDialog() {
      // 判断是否有选中记录
      if (this.sendUserArr.length > 0) {
        this.showDialog = true
      } else {
        this.$message.error('请选择要发送的用户')
      }
    },
    handleSelectionChange(val) {
      this.sendUserArr = val
    },
    closeDialog() {
      this.showDialog = false
    },
    async exportx() {
      const parms = {
        beginDate: this.activityDate[0],
        endDate: this.activityDate[1],
        top: this.rankTop
      }
      const res = await exportRankListByPage(parms)
      if (res.head.ret === 0) {
        const a = document.createElement('a')
        a.href = res.data // 这里的请求方式为get，如果需要认证，接口上需要带上token
        a.click()
      }
    }
  }
}
</script>
<style lang="scss" scoped>

</style>
