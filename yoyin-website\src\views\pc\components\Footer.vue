<template>
  <div class="contents">
      <div class="footer-connect">
        <div class="first-img">
          <div>
            <div class="imageDialogClass left1" v-show="index===1">
              <img src="../../../assets/qrcode-wx.png">
            </div>
            <img src="../../../assets/footer-1.png" @click="showZyc(1)" @mouseover="showZyc(1)" @mouseleave="showZyc(0)">
          </div>
          <img src="../../../assets/footer-2.png" style="cursor: pointer;" @click="nav">
          <div>
            <div class="imageDialogClass left3"  v-show="index===3">
              <img src="../../../assets/qrcode-dy.png">
            </div>
            <img src="../../../assets/footer-3.png" @click="showZyc(3)" @mouseover="showZyc(3)" @mouseleave="showZyc(0)">
          </div>
        </div>
        <div class="secend-img">
          <div class="imageDialogClass left4"  v-show="index===4">
            <img src="../../../assets/qrcode-wx-zhang.png">
          </div>
          <img src="../../../assets/footer-4.png"  @mouseover="showZyc(4)" @mouseleave="showZyc(0)">
        </div>
        <div class="three-img">
          <div class="imageDialogClass left5" v-show="index===5">
            <img src="../../../assets/qrcode-wx-yoyo.png">
          </div>
          <img src="../../../assets/footer-5.png" @click="showZyc(5)" @mouseover="showZyc(5)" @mouseleave="showZyc(0)">
        </div>
      </div>
      <div class="fflex">
        <div class="line">柚印官方网站</div>
        <div class="line"><a style="color:#000000;text-decoration:none" href="https://beian.miit.gov.cn/" target="_blank">Copyright © 2019 珠海移科智能科技有限公司 粤ICP备18108396号</a></div>
        <div class="line">yoyin.net</div>
      </div>
  </div>
</template>

<script>
export default {
  name: 'Footer',
  data () {
    return {
      imgUrl: '',
      showZY: false,
      index: 0,
      imageDialogHeight: {
        offsetHeight: '1000px'
      }
    }
  },
  mounted () {
    // console.log('weiweiwei')
    // this.showZyc()
  },
  methods: {
    nav () {
      // document.location.href
      window.open('https://youyinbgyp.tmall.com/index.htm?spm=a220o.1000855.w5002-23962026762.2.4d0cf2eb5Ir5sG', '_blank')
    },
    showZyc (index) {
      this.index = index
      // this.imageDialogHeight = {'offset'}
    }
  }
}
</script>

<style scoped>
.contents {
  padding-top: 160px;
  position: relative;
}
/* .contents {
  height: 294px;
  background: #FFFFFF;
  border-radius: 0px 0px 0px 0px;
  opacity: 1;
  border: 1px solid;
} */

.fflex {
    display:flex;
    justify-content: center;
    align-items: center;
    /* padding-top: 100px; */
}
.line {
    line-height: 70px;
    font-size: 14px;
    padding-left:30px;
}
.footer-connect {
    height: 130px;
    border-top: 1px solid #CCCCCC;
    border-bottom: 1px solid #CCCCCC;
    display:flex;
    justify-content: center;
    align-items: center;
    padding: 48px 0px;
    min-width: 1200px;
}

.first-img img {
  width: 96px;
  height: 129px;
}
.first-img {
  position: relative;
  display: flex;
  min-width: 390px;
  text-align: left;
}

.secend-img {
  position: relative;
  border-left: 1px solid #CCCCCC;
  border-right: 1px solid #CCCCCC;
  min-width: 450px;
}

.secend-img img {
  width: 270px;
  height: 85px;
}

.three-img {
  position: relative;
  min-width: 360px;
  text-align: right;
}

.zhezh{
  position: absolute;
  top: 0%;
  left: 0%;
  right: 0;
  bottom: 0%;
  width: 100%;
  height: 100%;
  background-color: black;
  z-index: 999;
  /*-moz-opacity: 0.1;*/
  opacity: 0.8;
}

.imageDialogClass {
  width: 237px;
  height: 248px;
  position: absolute;
  top: -220px;
}
.imageDialogClass img{
  width: 100%;
  height: 100%;
}
.left1 {
  left: -75px;
}

.left3 {
  left: 125px;
}

.left4 {
  left: 200px;
  top: -240px;
}
.left5 {
  left: 200px;
  top: -240px;
}
</style>
