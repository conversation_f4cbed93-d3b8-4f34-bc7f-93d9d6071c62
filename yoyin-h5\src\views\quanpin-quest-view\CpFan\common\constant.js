const cfgDict = {
  questionShowPart: {
    // [hearing听力, stem题干,opt选项,child子试题，answer答案a，analysis解析ay，attr属性at]
    // ant答案渲染模式:0获取没有：(1,普通 ,单选择)(2:填空（备选 答案）)(3：判断题)（4，多选 至少2-4）
    // some 统一知识点 attribute 放在 tag中
    // nochStem 1:小题 没有题干 :完形填空  -比如 7选5类 2:没有题干 和选项 只有答案
    // nocha：小题没有 标定属性
    // opt选项:o ,answer答案:a  0：不需要选项 和答案  1：需要选项和答案 2：根据 seven_answer seven_option 的选项和答案数量
    // 0 不显示 1：显示 2：(条件显示 level==1) 3:(条件显示 level!=1)
    // nns: not need stem :1，自己的内容 不验证 题干和 选项 答案
    11: { name: '单选题', h: 0, s: 1, o: 1, c: 0, a: 1, ay: 1, at: 1 },
    12: { name: '多选题', h: 0, s: 1, o: 1, c: 0, a: 1, ay: 1, at: 1, ant: 4 },
    13: { name: '填空题', h: 0, s: 1, o: 0, c: 0, a: 1, ay: 1, at: 1, ant: 2 },
    14: { name: '判断题', h: 0, s: 1, o: 0, c: 0, a: 1, ay: 1, at: 1, ant: 3 },
    15: { name: '简答题', h: 0, s: 1, o: 0, c: 0, a: 1, ay: 1, at: 1 },
    16: {
      name: '多题综合类',
      h: 0,
      s: 1,
      nns: 1,
      o: 0,
      c: 1,
      a: 0,
      ay: 0,
      at: 0,
    },
    17: {
      name: '多题综合类（同一知识点）',
      nns: 1,
      h: 0,
      s: 1,
      o: 0,
      c: 1,
      a: 0,
      ay: 0,
      at: 2,
      some: 1,
    },
    18: { name: '解答题', h: 0, s: 1, o: 0, c: 0, a: 1, ay: 1, at: 1 },
    19: {
      name: '听力题',
      h: 1,
      s: 1,
      nns: 1,
      o: 0,
      c: 1,
      a: 0,
      ay: 0,
      at: 2,
      nochStem: 1,
      some: 1,
    },
    20: {
      name: '听力单题-填空题',
      h: 0,
      s: 1,
      o: 0,
      c: 0,
      a: 1,
      ay: 1,
      at: 1,
      ant: 2,
    },
    21: { name: '听力单题-判断题', h: 0, s: 1, o: 0, c: 0, a: 1, ay: 1, at: 1 },
    22: { name: '听力单题-选择题', h: 0, s: 1, o: 1, c: 0, a: 1, ay: 1, at: 1 },
    23: {
      name: '完形填空',
      h: 0,
      s: 1,
      o: 0,
      c: 1,
      a: 0,
      ay: 0,
      at: 2,
      nochStem: 1,
      some: 1,
    },
    24: {
      name: '综合类填空',
      h: 0,
      s: 1,
      o: 0,
      c: 1,
      a: 0,
      ay: 0,
      at: 2,
      nochStem: 2,
      some: 1,
    },
    25: {
      name: '7选5类',
      h: 0,
      s: 1,
      o: 1,
      c: 1,
      a: 0,
      ay: 0,
      at: 2,
      nochStem: 2,
      some: 1,
    },
    26: { name: '作文', h: 0, s: 1, o: 0, c: 0, a: 1, ay: 1, at: 1 },
    27: {
      name: '选择填充题',
      h: 0,
      s: 1,
      o: 1,
      c: 0,
      a: 1,
      ay: 1,
      at: 1,
      ant: 2,
    },
    28: {
      name: '四选二类',
      h: 0,
      s: 1,
      o: 2,
      c: 0,
      a: 2,
      ay: 1,
      at: 1,
      ant: 2,
    },
    29: {
      name: '单词辨音模板',
      h: 0,
      s: 1,
      o: 0,
      c: 0,
      a: 1,
      ay: 1,
      at: 1,
      ant: 2,
    },
    30: { name: '改错模板', h: 0, s: 1, o: 0, c: 0, a: 1, ay: 1, at: 1 },
    31: {
      name: '同类选择题模板',
      h: 0,
      s: 1,
      o: 0,
      c: 1,
      a: 0,
      ay: 1,
      at: 2,
      nochStem: 1,
      some: 1,
    },
    32: {
      name: '听力题-解答题',
      h: 1,
      s: 1,
      o: 0,
      c: 0,
      a: 1,
      ay: 1,
      at: 1,
      ant: 1,
    },
    33: {
      name: '听力题',
      h: 1,
      s: 1,
      nns: 1,
      o: 0,
      c: 1,
      a: 0,
      ay: 0,
      at: 2,
      nochStem: 1,
    },
  },
  //  渲染 可能又分析的 题目结构 23，24，25
  analysisQuesConfig: [23, 24, 25],
}

export default {
  cfgDict,
}
