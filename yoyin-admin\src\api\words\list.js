/*
 * @Author: your name
 * @Date: 2019-10-16 11:52:25
 * @LastEditTime: 2019-10-22 15:10:15
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /d:\src\starPrinter\xstar-ui\star-printer-admin\src\api\version\list.js
 */
import request from '@/utils/request'

export function getOSSOptionFromServer() {
  return request({
    url: '/api/log/event/v1/storage/getosstoken',
    method: 'get'
  })
}

const LOAD_DATA_URL = '/platform/gam/community/v1/words/wordTypeList'
// 删除记录
const DELETE_RECORD_URL = '/platform/gam/community/v1/words/deleteCourse'
// 保存记录
const SAVE_RECORD_URL = '/platform/gam/community/v1/words/saveOrUpdateCourse'

const LOAD_DATA_WORDS_URL = '/platform/gam/community/v1/words/wordsList'
// 删除记录
const DELETE_RECORD_WORDS_URL = '/platform/gam/community/v1/words/deleteWrods'
// 保存记录
const SAVE_RECORD_WORDS_URL = '/platform/gam/community/v1/words/saveOrUpdateWords'
// 保存记录
const IMPORT_RECORD_WORDS_URL = '/platform/gam/community/v1/words/importWords'
const IMPORT_WORDS_JSON_URL = '/platform/gam/community/v1/words/importWordsJson'

const LOAD_DATA_COURSE_URL = '/platform/gam/community/v1/words/courseList'
const CREATE_COURSE_URL = '/platform/gam/community/v1/words/createCourse'
const UPDATE_COURSE_URL = '/platform/gam/community/v1/words/updateCourse'
const DELETE_WORDS_BY_COURSE_URL = '/platform/gam/community/v1/words/deleteWordsByCourseId'
const BATCH_DELETE_WORDS_URL = '/platform/gam/community/v1/words/batchDeleteWords'

/**
 * 查询用户列表
 * @param params 参数
 */
export const fetchListByPage = async params => {
  return request({
    url: LOAD_DATA_URL,
    method: 'get',
    params
  })
}

/**
 * 删除记录
 * @param {String} id 记录id
 */
export const deleteRecord = async params => {
  return request({
    url: DELETE_RECORD_URL,
    method: 'get',
    params
  })
}

/**
 * 保存记录
 * @param ids 记录id
 */
export const saveRecord = async params => {
  return request({
    url: SAVE_RECORD_URL,
    method: 'post',
    data: params
  })
}

/**
 * 查询用户列表
 * @param params 参数
 */
export const fetchWordList = async params => {
  return request({
    url: LOAD_DATA_WORDS_URL,
    method: 'get',
    params
  })
}

/**
 * 删除记录
 * @param {String} id 记录id
 */
export const deleteWrod = async params => {
  return request({
    url: DELETE_RECORD_WORDS_URL,
    method: 'get',
    params
  })
}

/**
 * 保存记录
 * @param ids 记录id
 */
export const saveWord = async params => {
  return request({
    url: SAVE_RECORD_WORDS_URL,
    method: 'post',
    data: params
  })
}

/**
 * 查询用户列表
 * @param params 参数
 */
export const fetchCourseList = async params => {
  return request({
    url: LOAD_DATA_COURSE_URL,
    method: 'get',
    params
  })
}

export const importWord = async params => {
  return request({
    url: IMPORT_RECORD_WORDS_URL,
    method: 'post',
    data: params
  })
}

/**
 * 导入单词JSON
 * @param {Object} params 参数
 * @param {String} params.courseId 课程ID
 * @param {String} params.importFile 导入文件内容
 * @param {String} params.userid 用户ID
 */
export const importWordsJson = async params => {
  return request({
    url: IMPORT_WORDS_JSON_URL,
    method: 'post',
    data: params
  })
}

/**
 * 创建课程
 * @param {Object} params 课程信息
 * @param {String} params.name 课程名称
 * @param {String} params.pId 父课程ID
 */
export const createCourse = async params => {
  return request({
    url: CREATE_COURSE_URL,
    method: 'post',
    data: params
  })
}

/**
 * 更新课程
 * @param {Object} params 课程信息
 * @param {String} params.id 课程ID
 * @param {String} params.name 课程名称
 */
export const updateCourse = async params => {
  return request({
    url: UPDATE_COURSE_URL,
    method: 'post',
    data: params
  })
}

/**
 * 清空课程下的所有单词
 * @param {Object} params 参数
 * @param {String} params.courseId 课程ID
 */
export const deleteWordsByCourseId = async params => {
  return request({
    url: DELETE_WORDS_BY_COURSE_URL,
    method: 'post',
    data: params
  })
}

/**
 * 批量删除单词
 * @param {Object} params 参数
 * @param {String} params.wordIds 单词ID数组的JSON字符串
 */
export const batchDeleteWords = async params => {
  return request({
    url: BATCH_DELETE_WORDS_URL,
    method: 'post',
    data: params
  })
}
