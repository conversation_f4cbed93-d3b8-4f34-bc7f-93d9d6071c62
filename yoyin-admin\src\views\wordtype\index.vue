<template>
  <div>
    <div class="table-query-container">
      <div class="query-code-btn">
        <el-button size="medium" style="margin-left:20px;" @click="handleAdd">新增</el-button>
      </div>
    </div>
    <div class="table-container">
      <el-table
        ref="multipleTable"
        :data="dataList"
        tooltip-effect="dark"
        style="width: 100%"
        border
      >
        <el-table-column label="ID" prop="id" />
        <el-table-column label="名称" prop="name" />
        <el-table-column label="上级" prop="pId" />
        <el-table-column label="排序" prop="sort" />
        <el-table-column label="type" prop="type" />
        <el-table-column label="操作">
          <template slot-scope="scope">
            <el-button size="mini" @click="handleEdit(scope.$index, scope.row)">编辑</el-button>
            <el-button
              size="mini"
              type="danger"
              @click="handleDelete(scope.$index, scope.row)"
            >删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <!-- <div class="pagination-container" style="margin-top:20px;">
        <el-pagination
          :current-page="params.pageno"
          :page-sizes="[10, 20, 30, 40]"
          :page-size="params.pagesize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div> -->
    </div>

    <el-dialog
      ref="dialog"
      :show-close="false"
      :title="form.id === '' ? '新增' : '编辑' "
      :visible.sync="showDialog"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      width="50%"
      center
    >
      <el-form ref="form" :model="form" label-width="130px" class="edit-form">
        <el-form-item label="名称" prop="name">
          <el-input v-model="form.name" />
        </el-form-item>
        <el-form-item label="排序" prop="sort">
          <el-input v-model="form.sort" />
        </el-form-item>
        <el-form-item label="上级" prop="pId">
          <el-input v-model="form.pId" />
        </el-form-item>
        <el-form-item label="类型" prop="type">
          <el-input v-model="form.type" />
        </el-form-item>
        <el-form-item>
          <el-button @click="handleClose">返回</el-button>
          <el-button type="primary" @click="submit">保存</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>

  </div>
</template>
<script>
import { fetchListByPage, deleteRecord, saveRecord } from '@/api/wordtype/list'
export default {
  name: 'WordTypeIndex',
  data() {
    return {
      dataList: [],
      showDialog: false,
      form: {}
    }
  },
  mounted() {
    this.getList()
  },
  methods: {
    async getList() {
      const res = await fetchListByPage({})
      if (res.head.ret === 0) {
        this.dataList = res.data
      }
    },
    handleDelete(index, row) {
      const parms = { id: row.id }
      deleteRecord(parms).then(res => {
        this.getList()
      })
    },
    handleEdit(index, row) {
      this.form = Object.assign({}, row)
      this.showDialog = true
    },
    handleAdd() {
      this.form = {}
      this.form.id = ''
      this.showDialog = true
    },
    handleClose() {
      this.form = {}
      this.showDialog = false
      this.getList()
    },
    submit() {
      saveRecord(this.form).then(res => {
        this.getList()
        this.handleClose()
      })
    }
  }
}
</script>
