<template>
  <div class="app-container">
    <div class="table-query-container">
      <span class="demonstration" style="padding-right: 10px">用户id/昵称:</span>
      <el-select
        v-model="params.createUserId"
        filterable
        clearable
        remote
        reserve-keyword
        placeholder="请输入关键词"
        :remote-method="queryUserList"
        :loading="loading"
      >
        <el-option
          v-for="item in userQueryList"
          :key="item.id"
          :label="item.value"
          :value="item.id"
        />
      </el-select>

      <!-- <el-input v-model="params.nickName" type="text" maxlength="15" style="width:120px;padding-right: 10px" /> -->
      <span class="demonstration" style="padding-right: 10px">订单编号:</span>
      <el-input v-model="params.orderNo" type="text" maxlength="15" style="width:120px;padding-right: 10px" />
      <span class="demonstration" style="padding-right: 10px">套餐类型:</span>
      <el-select v-model="params.goodsId" placeholder="选选择套餐">
        <el-option label="全部" value="" />
        <el-option
          v-for="item in goodsList"
          :key="item.id"
          :label="item.title"
          :value="item.id"
        />
      </el-select>
      <span class="demonstration" style="padding-right: 10px">订单状态:</span>
      <el-select v-model="params.orderStatus" placeholder="请选择状态">
        <el-option label="全部" value="" />
        <el-option label="已支付" value="审核通过" />
        <el-option label="未支付" value="已删除" />
      </el-select>
      <span class="demonstration" style="padding-right: 10px">支付时间:</span>
      <el-date-picker
        v-model="timeRange"
        :value-format="timeFormat"
        :format="timeFormat"
        :unlink-panels="true"
        type="datetimerange"
        :clearable="false"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        @change="changeTimeRange"
      />
      <el-button type="primary" @click="getList">查询</el-button>
      <el-button v-permission="'btn-menuQuanpin-orderrecord-edit'" @click="exportList">导出</el-button>
    </div>

    <div class="table-container">
      <el-table
        ref="multipleTable"
        :data="list"
        tooltip-effect="dark"
        style="width: 100%"
        border
      >
        <el-table-column label="订单编号">
          <template slot-scope="scope">{{ scope.row.orderNo }}</template>
        </el-table-column>
        <el-table-column label="用户账号">
          <template slot-scope="scope">{{ scope.row.userLoginDto.accountInfoDto
            && scope.row.userLoginDto.accountInfoDto[0] ? scope.row.userLoginDto.accountInfoDto[0].account : scope.row.createuserid + '已注销' }}</template>
        </el-table-column>
        <el-table-column label="用户id">
          <template slot-scope="scope">{{ scope.row.userLoginDto.userInfoDto ? scope.row.userLoginDto.userInfoDto.codeId : scope.row.createuserid + '已注销' }}</template>
        </el-table-column>
        <el-table-column label="套餐类型">
          <template slot-scope="scope">{{ scope.row.title }}</template>
        </el-table-column>
        <el-table-column label="金额(元)">
          <template slot-scope="scope">{{ scope.row.goodsPrice }}</template>
        </el-table-column>
        <el-table-column label="订单状态">
          <template slot-scope="scope">{{ scope.row.orderStatus }}</template>
        </el-table-column>
        <el-table-column label="支付时间">
          <template slot-scope="scope">{{ scope.row.createtime }}</template>
        </el-table-column>
        <!-- <el-table-column label="操作" width="230" align="center">
          <template slot-scope="scope">
            <el-button size="mini" @click="handleEdit(scope.$index, scope.row)">编辑</el-button>
            <el-button
              size="mini"
              type="danger"
              @click="handleDeleteRow(scope.$index, scope.row)"
            >删除</el-button>
          </template>
        </el-table-column> -->
      </el-table>
    </div>
    <div class="pagination-container" style="margin-top:20px;">
      <el-pagination
        :current-page="params.pageNo"
        :page-sizes="[10, 20, 30, 40]"
        :page-size="params.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>
<script>
import { fetchListByPage as fetchGoodsList } from '@/api/quanpin/membergoods'
import { fetchListByPage, exportList } from '@/api/quanpin/quanpinorder'
import { findUserInfoByKeyword } from '@/api/user/list'
export default {
  data() {
    return {
      timeRange: [],
      // 查询参数
      params: {
        pageNo: 1,
        pageSize: 10,
        type: ''
      },
      /** 列表 */
      list: [],
      /** 总条数 */
      total: 0,
      userQueryList: [],
      showDialog: false,
      loading: false,
      /** 时间格式 */
      timeFormat: 'yyyy-MM-dd',
      goodsList: []
    }
  },
  mounted() {
    this.getList()
    this.getGoodsList()
  },
  methods: {
    async getGoodsList() {
      const res = await fetchGoodsList({})
      if (res.head.ret === 0) {
        this.goodsList = res.data.result
      }
    },
    /** 获取列表 */
    async getList() {
      const res = await fetchListByPage(this.params)
      if (res.head.ret === 0) {
        this.list = res.data.result
        this.total = res.data.totalCount
      }
    },
    /** 切换每页条数 */
    handleSizeChange(val) {
      this.params.pageSize = val
      this.getList()
    },
    /** 切换当前页 */
    handleCurrentChange(val) {
      this.params.pageNo = val
      this.getList()
    },
    /** 选择时间段 */
    changeTimeRange(val) {
      if (val) {
        this.params.payBeginDate = val[0]
        this.params.payEndDate = val[1]
      }
    },
    formatUnitAndAmount(row) {
      return ''
    },
    queryUserList(e) {
      const params = {
        keyword: e
      }
      findUserInfoByKeyword(params).then(res => {
        if (res.head.ret === 0 && res.data.length > 0) {
          this.userQueryList = res.data
        }
        // if (res.head.ret === 0 && res.data.result.length > 0) {
        //   this.userQueryList = res.data.result
        // } else {
        //   delete params['codeId']
        //   params['nickName'] = e
        //   fetchUserListByPage(params).then(res2 => {
        //     if (res2.head.ret === 0) {
        //       this.userQueryList = res2.data.result
        //     }
        //   })
        // }
      })
    },
    async exportList() {
      const loading = this.$loading({
        lock: true,
        text: 'Loading',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })

      const res = await exportList(this.params)
      if (res.head.ret === 0) {
        const a = document.createElement('a')
        a.href = res.data // 这里的请求方式为get，如果需要认证，接口上需要带上token
        a.click()
      }
      loading.close()
    }

  }
}
</script>
<style lang="scss" scoped>

</style>
