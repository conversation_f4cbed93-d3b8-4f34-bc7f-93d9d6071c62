import request from '@/utils/request'
/*

 /platform/gam/community/v1/cache/queryJqApiWhiteList
 /platform/gam/community/v1/cache/addJqApiUserId
 /platform/gam/community/v1/cache/removeJqApiUserId
 /platform/gam/community/v1/cache/queryJqApiCountByUserId
 /platform/gam/community/v1/cache/settingJqApiCountByUserId

*/
export const queryJqApiWhiteList = async params => {
  return request({
    url: '/platform/gam/community/v1/cache/queryJqApiWhiteList',
    method: 'get'
  })
}

export const addJqApiUserId = async params => {
  return request({
    url: '/platform/gam/community/v1/cache/addJqApiUserId',
    method: 'post',
    data: params
  })
}

export const removeJqApiUserId = async params => {
  return request({
    url: '/platform/gam/community/v1/cache/removeJqApiUserId',
    method: 'post',
    data: params
  })
}

export const queryJqApiCountByUserId = async params => {
  return request({
    url: '/platform/gam/community/v1/cache/queryJqApiCountByUserId',
    method: 'get',
    params
  })
}

export const settingJqApiCountByUserId = async params => {
  return request({
    url: '/platform/gam/community/v1/cache/settingJqApiCountByUserId',
    method: 'post',
    data: params
  })
}
