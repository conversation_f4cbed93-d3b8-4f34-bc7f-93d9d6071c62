/*
 * @Description: In User Settings Edit
 * @Author: your name
 * @Date: 2019-10-16 10:58:32
 * @LastEditTime: 2019-10-16 18:08:52
 * @LastEditors: Please set LastEditors
 */
import { login, getInfo, getUserAuthorityInfo } from '@/api/user'
import { getToken, setToken, removeToken, getAuthList, setAuthList } from '@/utils/auth'
import { resetRouter } from '@/router'
import { Message } from 'element-ui'

const state = {
  token: getToken(),
  name: '',
  avatar: '',
  authList: getAuthList()
}

const mutations = {
  SET_TOKEN: (state, token) => {
    state.token = token
  },
  SET_NAME: (state, name) => {
    state.name = name
  },
  SET_AVATAR: (state, avatar) => {
    state.avatar = avatar
  },
  SET_AUTHLIST: (state, authList) => {
    state.authList = authList
  }
}

const actions = {
  // user login
  login({ commit }, userInfo) {
    const { username, password } = userInfo
    return new Promise((resolve, reject) => {
      login({ userName: username.trim(), password: password, account: username.trim(), type: username.trim() }).then(response => {
        const { data } = response
        commit('SET_TOKEN', data.accesstoken)
        commit('SET_NAME', username)
        commit('SET_AVATAR', 'https://wpimg.wallstcn.com/f778738c-e4f8-4870-b634-56703b4acafe.gif')
        setToken(data.accesstoken)
        resolve()
      }).catch(error => {
        Message('用户或密码有误')
        console.log('login error: ')
        reject(error)
      })
    })
  },

  // get user info
  getInfo({ commit, state }) {
    return new Promise((resolve, reject) => {
      getInfo(state.token).then(response => {
        const { data } = response

        if (!data) {
          reject('Verification failed, please Login again.')
        }

        const { name, avatar } = data

        commit('SET_NAME', name)
        commit('SET_AVATAR', avatar)
        resolve(data)
      }).catch(error => {
        reject(error)
      })
    })
  },

  // user logout
  logout({ commit, state }) {
    return new Promise((resolve, reject) => {
      // logout(state.token).then(() => {
      commit('SET_TOKEN', '')
      removeToken()
      resetRouter()
      resolve()
      // }).catch(error => {
      //   reject(error)
      // })
    })
  },

  // remove token
  resetToken({ commit }) {
    return new Promise(resolve => {
      commit('SET_TOKEN', '')
      removeToken()
      resolve()
    })
  },

  getUserAuthInfo({ commit }) {
    return new Promise((resolve, reject) => {
      getUserAuthorityInfo().then(response => {
        const { data } = response
        if (data.resource) {
          const resourceList = data.resource
          const resList = []
          resourceList.forEach(element => {
            resList.push(element.resCode)
          })
          setAuthList(resList)
          commit('SET_AUTHLIST', resList)
        }

        resolve()
      }).catch(error => {
        Message('获取用户权限失败')
        reject(error)
      })
    })
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}

