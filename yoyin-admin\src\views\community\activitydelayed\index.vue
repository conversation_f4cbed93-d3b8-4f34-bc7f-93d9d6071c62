<!--
 * @Description: In User Settings Edit
 * @Author: your name
 * @Date: 2019-10-16 11:28:20
 * @LastEditTime: 2019-10-16 11:38:48
 * @LastEditors: Please set LastEditors
 -->
<template>
  <div class="app-container">
    <div class="table-query-container">
      <div class="query-date-hot">
        <el-button type="primary" size="medium" style="margin-left: 20px;" @click="handleQuery">查询</el-button>
      </div>
    </div>

    <div class="cards-container">
      <div v-for="(item, index) in list" :key="index" class="cards-container-item">
        <!-- <Card :id="item.feedId" :item="item" :label-list="labelList" :subject-list="subjectList" @notifyUpdate="notifyUpdate" /> -->
      </div>
    </div>

    <div class="table-container">
      <el-table
        ref="multipleTable"
        :data="list"
        tooltip-effect="dark"
        style="width: 100%"
        border
      >
        <el-table-column type="selection" width="55" />
        <el-table-column label="标题">
          <template slot-scope="scope">{{ scope.row.title }}</template>
        </el-table-column>
        <el-table-column label="内容">
          <template slot-scope="scope">{{ scope.row.content }}</template>
        </el-table-column>
        <el-table-column label="标签" width="120" :formatter="labelIdsFormat" prop="labelIds" />
        <el-table-column label="发布时间" width="120">
          <template slot-scope="scope">{{ scope.row.delayedTime }}</template>
        </el-table-column>
        <el-table-column label="年级" width="60" :formatter="gradeFormat" prop="gradeLevel" />
        <el-table-column label="学科" width="60">
          <template slot-scope="scope">{{ scope.row.subject }}</template>
        </el-table-column>
        <el-table-column label="发帖人名称" width="120">
          <template slot-scope="scope">{{ scope.row.nickName }}</template>
        </el-table-column>
        <el-table-column label="图片" width="160">
          <template slot-scope="scope">
            <div style="display:flex;flex-wrap: wrap;">
              <div v-for="picitem in scope.row.pic" :key="picitem.pic" style="width: 40px;margin-right:5px">
                <!-- <img :src="picitem.pic" style="height:40px;width:40px;"> -->
                <el-image
                  style="height:40px;width:40px;"
                  :src="picitem.pic"
                  :preview-src-list="getPicList(scope.row.pic)"
                />
              </div>
            </div>
          </template>
        </el-table-column>
        <!-- <el-table-column prop="showType" label="显示类型" :formatter="jumpTypeFormat" width="120" /> -->
        <el-table-column label="操作" width="120">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="danger"
              @click="handleDeleteRow(scope.row)"
            >删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="pagination-container" style="margin-top:20px;">
      <el-pagination
        :current-page="params.pageno"
        :page-sizes="[10, 20, 30, 40]"
        :page-size="params.pagesize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>
<script>
import { fetchDelayedListByPage, findLabelPage, getSubjectList, deleteDelayedRecord } from '@/api/community/activitydelayed'
import { fetchUserListByPage } from '@/api/user/list'
export default {
  data() {
    return {
      stickNum: '',
      // 查询参数
      params: {
        startdate: '',
        enddate: '',
        pageno: 1,
        pagesize: 10,
        codeid: '',
        haspic: '',
        ishot: '',
        feedid: ''
      },
      /** 列表 */
      list: [],
      /** 总条数 */
      total: 0,
      /** 消息体 */
      banner: { },
      /** 勾选数据 */
      multipleSelection: [],
      /** 时间段 */
      timeRange: '',
      /** 时间格式 */
      timeFormat: 'yyyy-MM-dd',
      /** 显示编辑框 */
      showEdit: false,
      /** 标签列表 */
      labelList: [],
      /** 显示详情 */
      showDetail: false,
      /** 详情数据 */
      detailUrl: '',
      /** 素材标签 */
      scTabs: [],
      /** 活动标签 */
      htTabs: [],
      tabType: 'study',
      tabLabelId: '999999',
      tabSubjectId: '',
      subjectList: [],
      userQueryList: [],
      loading: false,
      gradeList: ['', '一年级', '二年级', '三年级', '四年级', '五年级', '六年级', '初一', '初二', '初三', '高一', '高二', '高三']
    }
  },
  mounted() {
    const feedId = this.$route.query.feedId
    this.params.feedid = feedId
    const codeId = this.$route.query.codeId
    this.params.codeid = codeId
    this.getMySubjectList()
    this.getLableList()
    this.getList()
  },
  methods: {
    gradeFormat(e) {
      return this.gradeList[e.gradeNum]
    },
    getPicList(array) {
      const arr = []
      array.forEach(element => {
        arr.push(element.pic)
      })
      return arr
    },
    labelIdsFormat(e) {
      const _this = this
      let labelIdsCn = ''
      if (e.labelIds) {
        e.labelIds.forEach(item => {
          _this.labelList.forEach(itema => {
            if (itema.id === item) {
              labelIdsCn += itema.titleZh + ' '
            }
          })
        })
      }

      return labelIdsCn
    },
    handleDeleteRow(e) {
      this.$confirm('此操作将删除该记录, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const parms = {
          id: e.id
        }
        deleteDelayedRecord(parms).then(res => {
          this.$message.success('操作成功')
          this.getList()
        })
      }).catch(() => {
      })
    },
    queryUserList(e) {
      const params = {
        pageNo: 1,
        pageSize: 12,
        sort: 'desc',
        sortType: 'time',
        codeId: e
      }
      fetchUserListByPage(params).then(res => {
        if (res.head.ret === 0 && res.data.result.length > 0) {
          this.userQueryList = res.data.result
        } else {
          delete params['codeId']
          params['nickName'] = e
          fetchUserListByPage(params).then(res2 => {
            if (res2.head.ret === 0) {
              this.userQueryList = res2.data.result
            }
          })
        }
      })
    },
    async getMySubjectList() {
      const res = await getSubjectList()
      if (res.head.ret === 0) {
        this.subjectList = res.data
      }
    },
    /** 获取列表 */
    async getList() {
      const res = await fetchDelayedListByPage(this.params)
      if (res.head.ret === 0) {
        this.list = []
        this.list = res.data.result
        this.total = res.data.totalCount
      }
    },
    /** 切换每页条数 */
    handleSizeChange(val) {
      this.params.pagesize = val
      this.getList()
    },
    /** 切换当前页 */
    handleCurrentChange(val) {
      this.params.pageno = val
      this.getList()
    },
    onChangeSubject(val) {
      this.params.subject = val
      this.getList()
    },
    /** 选择时间段 */
    changeTimeRange(val) {
      this.params.startdate = val[0]
      this.params.enddate = val[1]
    },
    /** 查询 */
    handleQuery() {
      this.getList()
    },
    /** 新增 */
    handleAdd() {
      this.banner = {}
      // this.getLableList()
      this.showEdit = true
    },
    onChangeLabel(e) {
      this.params.labelId = e
      this.getList()
    },
    onChangeStickNum(e) {
      this.params.stickNum = e
      this.getList()
    },
    /** 关闭弹框 */
    closeDialog(code) {
      this.showEdit = false
      this.banner = {}
      // 保存code
      if (code === '1') {
        this.getList()
      }
    },
    /** 获取标签列表 */
    async getLableList() {
      const res = await findLabelPage({
        pageno: 1,
        pagesize: 100
      })
      if (res.head.ret === 0) {
        this.labelList = res.data
      }
    },
    /** 点击详情 */
    handleClickDetail(item) {
      this.detailUrl = item.htmlUrl
      this.showDetail = true
    },
    /** 保存后关闭刷新 */
    handleClose(val) {
      this.showEdit = false
      this.getList()
    },
    /** 删除后刷新 */
    notifyUpdate() {
      this.getList()
    }

  }
}
</script>

<style lang="scss" scoped>
.app-container {
  .table-query-container {
    .query-date-hot{
      margin-bottom: 10px;
    }
    .query-code-btn{
      margin-top: 10px;
      button{
        margin-right: 20px;
      }
    }
  }
  .cards-container{
    display: flex;
    flex-flow: row wrap;
    justify-content:flex-start;
    &-item{
      width: 30%;
      margin-right: 1%;
      margin-bottom: 20px;
    }
  }

}
</style>
