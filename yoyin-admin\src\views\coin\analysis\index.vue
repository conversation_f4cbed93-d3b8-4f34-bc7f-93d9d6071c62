<!--
 * @Description: In User Settings Edit
 * @Author: your name
 * @Date: 2019-10-16 11:28:20
 * @LastEditTime: 2019-10-18 14:10:13
 * @LastEditors: Please set LastEditors
 -->
<template>
  <div class="app-container">
    <el-tabs v-model="activeName" @tab-click="handleClick">
      <el-tab-pane label="任务次数" name="first" :lazy="true">
        <count-finish />
      </el-tab-pane>
      <el-tab-pane label="新增积分排名" name="second" :lazy="true">
        <count-trank />
      </el-tab-pane>
      <el-tab-pane label="积分任务" name="three" :lazy="true">
        <coin-mission-finish-stat />
      </el-tab-pane>
      <el-tab-pane label="商品兑换" name="four" :lazy="true">
        <goods-change-list />
      </el-tab-pane>
      <el-tab-pane label="积分余额排名" name="five" :lazy="true">
        <curr-coin-rank />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
<script>
import CountFinish from './component/CountFinish'
import CountTrank from './component/CountTrank'
import CurrCoinRank from './component/CurrCoinRank'
import GoodsChangeList from './component/GoodsChangeList'
import CoinMissionFinishStat from './component/CoinMissionFinishStat'
export default {
  name: 'Analysis',
  components: { CountFinish, CountTrank, CurrCoinRank, GoodsChangeList, CoinMissionFinishStat },
  data() {
    return {
      actType: '0',
      activeName: 'first'
    }
  },
  computed: {

  },

  created() {

  },
  mounted() {

  },
  methods: {
    /** 切换tab */
    handleClick(tab, event) {
    }

  }

}
</script>

<style lang="scss" scoped>

.date-container{
  margin-bottom: 20px;
}
.condition-wrapper{
  display: flex;
  flex-direction: row;
  justify-content:space-between;
}
</style>
