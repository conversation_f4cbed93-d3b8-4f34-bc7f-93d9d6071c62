import request from '@/utils/request'

const getDraftListUrl = '/platform/gam/community/v1/drafts/finddraftshistory'

export const fetchUserDraftListByPage = async params => {
  return request({
    url: getDraftListUrl,
    method: 'get',
    params
  })
}

export const fetchXeasylabelDraftListByPage = async params => {
  return request({
    url: '/platform/gam/community/v1/drafts/findxeasylabeldrafts',
    method: 'get',
    params
  })
}
