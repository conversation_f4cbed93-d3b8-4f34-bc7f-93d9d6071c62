{"remainingRequest": "D:\\workspace\\xplife\\yoyin-ui\\yoyin-admin\\node_modules\\thread-loader\\dist\\cjs.js!D:\\workspace\\xplife\\yoyin-ui\\yoyin-admin\\node_modules\\babel-loader\\lib\\index.js!D:\\workspace\\xplife\\yoyin-ui\\yoyin-admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\workspace\\xplife\\yoyin-ui\\yoyin-admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\workspace\\xplife\\yoyin-ui\\yoyin-admin\\src\\views\\office\\material\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\workspace\\xplife\\yoyin-ui\\yoyin-admin\\src\\views\\office\\material\\index.vue", "mtime": 1751270715624}, {"path": "D:\\workspace\\xplife\\yoyin-ui\\yoyin-admin\\babel.config.js", "mtime": 1744192657864}, {"path": "D:\\workspace\\xplife\\yoyin-ui\\yoyin-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\workspace\\xplife\\yoyin-ui\\yoyin-admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\workspace\\xplife\\yoyin-ui\\yoyin-admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\workspace\\xplife\\yoyin-ui\\yoyin-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\workspace\\xplife\\yoyin-ui\\yoyin-admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["import _typeof from \"D:/workspace/xplife/yoyin-ui/yoyin-admin/node_modules/@babel/runtime/helpers/esm/typeof.js\";\nimport \"core-js/modules/es6.function.name\";\nimport \"core-js/modules/web.dom.iterable\";\nimport _regeneratorRuntime from \"D:/workspace/xplife/yoyin-ui/yoyin-admin/node_modules/@babel/runtime/helpers/esm/regeneratorRuntime.js\";\nimport \"regenerator-runtime/runtime\";\nimport _asyncToGenerator from \"D:/workspace/xplife/yoyin-ui/yoyin-admin/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport { fetchOfficeMaterialPageList, fetchOfficeMaterialDetail } from '@/api/office/material';\nimport { fetchDictList } from '@/api/system/dict';\nimport Card from \"./component/Card/index\";\nimport CommonForm from \"./component/CommonForm/index\";\nimport ToDoListForm from \"./component/ToDoListForm/index\";\nimport TextPopForm from \"./component/TextPopForm/index\";\nimport FontForm from \"./component/FontForm/index\";\nexport default {\n  components: {\n    Card: Card,\n    CommonForm: CommonForm,\n    ToDoListForm: ToDoListForm,\n    TextPopForm: TextPopForm,\n    FontForm: FontForm\n  },\n  data: function data() {\n    return {\n      list: [],\n      industryList: [],\n      page: {\n        size: 20,\n        no: 1,\n        total: 0\n      },\n      showAdd: false,\n      currentView: 'CommonForm',\n      currentForm: {},\n      currentTitile: '办公素材编辑',\n      currentId: '',\n      currentName: ''\n    };\n  },\n  mounted: function () {\n    var _mounted = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee() {\n      return _regeneratorRuntime().wrap(function _callee$(_context) {\n        while (1) switch (_context.prev = _context.next) {\n          case 0:\n            _context.next = 2;\n            return this.fetchIndustryData();\n          case 2:\n            this.fetchList();\n          case 3:\n          case \"end\":\n            return _context.stop();\n        }\n      }, _callee, this);\n    }));\n    function mounted() {\n      return _mounted.apply(this, arguments);\n    }\n    return mounted;\n  }(),\n  methods: {\n    handleAdd: function handleAdd() {\n      var _this = this;\n      this.industryList.forEach(function (m) {\n        if (m.code === _this.currentId) {\n          _this.currentName = m.name;\n          _this.currentTitile = m.name;\n        }\n      });\n      this.currentForm = {\n        mId: this.currentId\n      };\n      this.currentTitile = this.currentTitile + '新增';\n      this.currentView = 'CommonForm';\n      this.showAdd = true;\n    },\n    notifyUpdate: function notifyUpdate() {\n      this.fetchList();\n    },\n    notifyEdit: function () {\n      var _notifyEdit = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee2(item) {\n        return _regeneratorRuntime().wrap(function _callee2$(_context2) {\n          while (1) switch (_context2.prev = _context2.next) {\n            case 0:\n              _context2.next = 2;\n              return this.getDetail(item.id);\n            case 2:\n            case \"end\":\n              return _context2.stop();\n          }\n        }, _callee2, this);\n      }));\n      function notifyEdit(_x) {\n        return _notifyEdit.apply(this, arguments);\n      }\n      return notifyEdit;\n    }(),\n    getDetail: function () {\n      var _getDetail = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee3(id) {\n        var _this2 = this;\n        var res, materialCategory;\n        return _regeneratorRuntime().wrap(function _callee3$(_context3) {\n          while (1) switch (_context3.prev = _context3.next) {\n            case 0:\n              _context3.prev = 0;\n              _context3.next = 3;\n              return fetchOfficeMaterialDetail({\n                id: id\n              });\n            case 3:\n              res = _context3.sent;\n              if (res.head && res.head.ret === 0) {\n                this.currentForm = res.data;\n                this.currentForm.id = id;\n\n                // 修改这里：使用素材自身的category而不是当前选中的currentId\n                materialCategory = res.data.category || '';\n                this.industryList.forEach(function (m) {\n                  if (m.code === materialCategory) {\n                    _this2.currentName = m.name;\n                    _this2.currentTitile = m.name;\n                  }\n                });\n                this.currentTitile = this.currentTitile + '编辑';\n                this.currentView = 'CommonForm';\n                this.showAdd = true;\n              }\n              _context3.next = 10;\n              break;\n            case 7:\n              _context3.prev = 7;\n              _context3.t0 = _context3[\"catch\"](0);\n              console.error('获取办公素材详情失败:', _context3.t0);\n            case 10:\n            case \"end\":\n              return _context3.stop();\n          }\n        }, _callee3, this, [[0, 7]]);\n      }));\n      function getDetail(_x2) {\n        return _getDetail.apply(this, arguments);\n      }\n      return getDetail;\n    }(),\n    handleBack: function handleBack(val) {\n      console.log('handleBack called with val:', val, 'type:', _typeof(val));\n      this.currentForm = {};\n      this.showAdd = false;\n      if (val === '1') {\n        console.log('新增/编辑成功，开始刷新列表...');\n        // 重置到第一页，确保能看到新增的数据\n        this.page.no = 1;\n        this.fetchList();\n      }\n    },\n    handleCurrentChange: function handleCurrentChange(val) {\n      this.page.no = val;\n      this.fetchList();\n    },\n    handleSizeChange: function handleSizeChange(val) {\n      this.page.size = val;\n      this.fetchList();\n    },\n    handleMaterialChange: function handleMaterialChange() {\n      var _this3 = this;\n      this.page = {\n        size: 20,\n        no: 1,\n        total: 0\n      };\n      this.industryList.forEach(function (m) {\n        if (m.code === _this3.currentId) {\n          _this3.currentName = m.name;\n          _this3.currentTitile = m.name;\n        }\n      });\n      this.fetchList();\n    },\n    fetchList: function () {\n      var _fetchList = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee4() {\n        var params, res;\n        return _regeneratorRuntime().wrap(function _callee4$(_context4) {\n          while (1) switch (_context4.prev = _context4.next) {\n            case 0:\n              _context4.prev = 0;\n              params = {\n                pageno: this.page.no,\n                pagesize: this.page.size\n              };\n              if (this.currentId) {\n                params.category = this.currentId;\n              }\n              console.log('fetchList 请求参数:', params);\n              _context4.next = 6;\n              return fetchOfficeMaterialPageList(params);\n            case 6:\n              res = _context4.sent;\n              console.log('fetchList 响应结果:', res);\n              if (res.head && res.head.ret === 0) {\n                this.list = res.data && res.data.result || [];\n                this.page.total = res.data && res.data.totalCount || 0;\n                console.log('列表更新成功，当前数据量:', this.list.length, '总数:', this.page.total);\n              }\n              _context4.next = 14;\n              break;\n            case 11:\n              _context4.prev = 11;\n              _context4.t0 = _context4[\"catch\"](0);\n              console.error('获取办公素材列表失败:', _context4.t0);\n            case 14:\n            case \"end\":\n              return _context4.stop();\n          }\n        }, _callee4, this, [[0, 11]]);\n      }));\n      function fetchList() {\n        return _fetchList.apply(this, arguments);\n      }\n      return fetchList;\n    }(),\n    fetchIndustryData: function () {\n      var _fetchIndustryData = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee5() {\n        var res;\n        return _regeneratorRuntime().wrap(function _callee5$(_context5) {\n          while (1) switch (_context5.prev = _context5.next) {\n            case 0:\n              _context5.prev = 0;\n              _context5.next = 3;\n              return fetchDictList({\n                type: 'industry'\n              });\n            case 3:\n              res = _context5.sent;\n              if (res.head && res.head.ret === 0) {\n                this.industryList = res.data || [];\n                // 默认选中\"全部\"\n                this.currentId = '';\n              }\n              _context5.next = 10;\n              break;\n            case 7:\n              _context5.prev = 7;\n              _context5.t0 = _context5[\"catch\"](0);\n              console.error('获取行业列表失败:', _context5.t0);\n            case 10:\n            case \"end\":\n              return _context5.stop();\n          }\n        }, _callee5, this, [[0, 7]]);\n      }));\n      function fetchIndustryData() {\n        return _fetchIndustryData.apply(this, arguments);\n      }\n      return fetchIndustryData;\n    }()\n  }\n};", null]}