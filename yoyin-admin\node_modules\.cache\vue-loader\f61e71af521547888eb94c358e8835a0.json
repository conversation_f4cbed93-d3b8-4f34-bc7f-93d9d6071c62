{"remainingRequest": "D:\\workspace\\xplife\\yoyin-ui\\yoyin-admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\workspace\\xplife\\yoyin-ui\\yoyin-admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\workspace\\xplife\\yoyin-ui\\yoyin-admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\workspace\\xplife\\yoyin-ui\\yoyin-admin\\src\\views\\office\\material\\index.vue?vue&type=template&id=22b158ed&scoped=true", "dependencies": [{"path": "D:\\workspace\\xplife\\yoyin-ui\\yoyin-admin\\src\\views\\office\\material\\index.vue", "mtime": 1751274032261}, {"path": "D:\\workspace\\xplife\\yoyin-ui\\yoyin-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\workspace\\xplife\\yoyin-ui\\yoyin-admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\workspace\\xplife\\yoyin-ui\\yoyin-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\workspace\\xplife\\yoyin-ui\\yoyin-admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"app-container\"},[_c('div',{staticClass:\"list-contatiner\"},[_c('div',{staticClass:\"list-container-change\"},[_c('el-radio-group',{staticStyle:{\"margin-right\":\"20px\"},attrs:{\"size\":\"small\"},on:{\"change\":_vm.handleMaterialChange},model:{value:(_vm.currentId),callback:function ($$v) {_vm.currentId=$$v},expression:\"currentId\"}},[_c('el-radio-button',{attrs:{\"label\":\"\"}},[_vm._v(\"全部\")]),_vm._v(\" \"),_vm._l((_vm.industryList),function(item){return _c('el-radio-button',{key:item.code,attrs:{\"label\":item.code}},[_vm._v(_vm._s(item.name))])})],2),_vm._v(\" \"),_c('el-button',{attrs:{\"type\":\"primary\"},on:{\"click\":_vm.handleAdd}},[_vm._v(\"新增办公素材\")])],1),_vm._v(\" \"),_c('div',{staticClass:\"list-container-data\"},[_c('div',{staticClass:\"list-container-data-cards\"},_vm._l((_vm.list),function(item,index){return _c('div',{key:index,staticClass:\"list-container-data-cards-item\"},[_c('Card',{attrs:{\"item\":item},on:{\"notifyUpdate\":_vm.notifyUpdate,\"notifyEdit\":_vm.notifyEdit}})],1)}),0),_vm._v(\" \"),_c('div',{staticClass:\"list-container-data-page\"},[_c('el-pagination',{attrs:{\"total\":_vm.page.total,\"current-page\":_vm.page.no,\"page-size\":_vm.page.size,\"layout\":\"total, sizes, prev, pager, next, jumper\"},on:{\"current-change\":_vm.handleCurrentChange,\"size-change\":_vm.handleSizeChange}})],1)])]),_vm._v(\" \"),_c('div',{staticClass:\"dialog-container\"},[_c('el-dialog',{attrs:{\"append-to-body\":true,\"title\":_vm.currentTitile,\"visible\":_vm.showAdd,\"center\":\"\",\"show-close\":false,\"destroy-on-close\":true},on:{\"update:visible\":function($event){_vm.showAdd=$event}}},[(_vm.currentView === 'CommonForm')?_c('CommonForm',{attrs:{\"m-id\":_vm.currentId,\"m-name\":_vm.currentName,\"form-data\":_vm.currentForm},on:{\"handleBack\":_vm.handleBack}}):_vm._e(),_vm._v(\" \"),(_vm.currentView === 'ToDoListForm')?_c('ToDoListForm',{attrs:{\"m-id\":_vm.currentId,\"m-name\":_vm.currentName,\"form-data\":_vm.currentForm},on:{\"handleBack\":_vm.handleBack}}):_vm._e(),_vm._v(\" \"),(_vm.currentView === 'TextPopForm')?_c('TextPopForm',{attrs:{\"m-id\":_vm.currentId,\"m-name\":_vm.currentName,\"form-data\":_vm.currentForm},on:{\"handleBack\":_vm.handleBack}}):_vm._e(),_vm._v(\" \"),(_vm.currentView === 'FontForm')?_c('FontForm',{attrs:{\"m-id\":_vm.currentId,\"m-name\":_vm.currentName,\"form-data\":_vm.currentForm},on:{\"handleBack\":_vm.handleBack}}):_vm._e()],1)],1)])}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }"]}