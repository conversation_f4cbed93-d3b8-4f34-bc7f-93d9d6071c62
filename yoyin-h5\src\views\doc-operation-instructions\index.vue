<template>
  <div class="content">
    <div class="content-title">{{$t(`doc-import-ios.title`)}}</div>

    <div class="content-step">
      <div class="content-step-text">
        <div class="content-step-text-logo">
          <img  src="./assets/img/step01.png" />
        </div>
        {{$t(`doc-import-ios.step1`)}}
      </div>
      <div class="content-step-pic"><img src="./assets/img/step01-pic.png" /></div>
    </div>

    <div class="content-step">
      <div class="content-step-text">
        <div class="content-step-text-logo">
          <img  src="./assets/img/step02.png" />
        </div>
        {{$t(`doc-import-ios.step2`)}}
      </div>
      <div class="content-step-pic content-step-pics">
        <div>
          <img src="./assets/img/step02-pic-l.png" />
        </div>
        <div>
          <img src="./assets/img/step02-pic-r.png" />
        </div>
      </div>
    </div>

    <div class="content-step">
      <div class="content-step-text">
        <div class="content-step-text-logo">
          <img  src="./assets/img/step03.png" />
        </div>
        {{$t(`doc-import-ios.step3`)}}
      </div>
      <div class="content-step-pic"><img src="./assets/img/step03-pic.png" /></div>
    </div>    

  </div>
</template>
<script>
import Cookies from 'js-cookie'
export default {
  data () {
    return {
      moduleKey: 'doc-operation-instructions',
      language: ''
    }
  },
  async mounted () {
    // this.language = Cookies.get('language')
  }
}
</script>

<style lang="scss">

.content {
  display: flex;
  flex-direction: column;
  padding: 20px 15px;
  &-title{
    font-size: 20px;
    font-weight: bold;
    margin-bottom: 15px;
  }
  &-step{
    display: flex;
    flex-direction: column;
    &-text{
      display: flex;
      align-items: center;
      &-logo{
        height: 45px;
        width: 45px;
        margin-right: 8px;
        img{
          height: 45px;
          width: 45px;
        }
      }
      &-tlogo{
        img{
          height: 27px;
          width: 24px;
        }        
      }
    }
    &-pic{
      align-self: center;
      width: 50vw;
      img {
        width: 100%;
        height: auto;
      }      
    }
    &-pics{
      display: flex;
      width: 90vw;
      div{
        width: 50%;
      }
    }
  }
  &-divider{
    width:96%;
    height:0px;
    border:1px dashed #d8d4d4c9;
    align-self: center;
    margin: 28px auto 35px auto;
  }

}
</style>

