import request from '@/utils/request'

/**
 * 查询列表数据
 * @param params 参数
 */
export const fetchListByPage = async params => {
  return request({
    url: '/platform/authority/auth/v1/useraccount/findpage',
    method: 'get',
    params
  })
}

/**
 * 保存记录
 * @param ids 记录id
 */
export const saveRecord = async params => {
  return request({
    url: '/platform/authority/auth/v1/useraccount/saveorupdate',
    method: 'post',
    data: params
  })
}

export const deleteRecord = async params => {
  return request({
    url: '/platform/authority/auth/v1/useraccount/delete',
    method: 'post',
    data: params
  })
}

export const fetchAccountRoleList = async params => {
  return request({
    url: '/platform/authority/auth/v1/useraccount/findAccountRoleById',
    method: 'get',
    params
  })
}

export const disrole = async params => {
  return request({
    url: '/platform/authority/auth/v1/useraccount/disrole',
    method: 'post',
    data: params
  })
}

