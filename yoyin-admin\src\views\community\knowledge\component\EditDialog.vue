<template>
  <div v-if="visiable" class="edit-dialog">
    <el-dialog
      ref="dialog"
      :show-close="false"
      :title="form.id === '' ? '新增' : '编辑' "
      :visible.sync="visiable"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      width="80%"
      center
    >
      <el-form ref="form" :model="form" :rules="rules" label-width="100px" class="edit-form">
        <el-form-item label="标题" prop="title">
          <el-input v-model="form.title" />
        </el-form-item>
        <el-form-item label="封面" prop="headUrl">
          <el-upload
            class="avatar-uploader"
            action=""
            :show-file-list="false"
            :on-change="changeHead"
            :before-upload="beforeUpload"
            accept="image/png, image/jpeg"
          >
            <img v-if="form.headUrl" :src="form.headUrl" class="avatar">
            <i v-else class="el-icon-plus avatar-uploader-icon" />
          </el-upload>
          <img v-show="false" :src="testUrl" class="avatar">
        </el-form-item>
        <el-form-item label="开始展示的版本号" prop="version">
          <el-input
            v-model="form.version"
            type="text"
            maxlength="200"
            placeholder="请输入格式x.x.x"
          />
          {主版本号} + "." + {版本小号} + "." + {修复版本号} 例如:  3.3.1
        </el-form-item>
        <el-form-item label="学科" prop="subject">
          <el-radio-group v-model="form.subject">
            <el-radio-button v-for="item in subjectList" :key="item.id" :label="item.id">{{ item.name }}</el-radio-button>
            <el-radio-button label="">无</el-radio-button>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="年级">
          <!-- <el-radio-group v-model="form.gradeLevel">
            <el-radio-button label="xx">小学</el-radio-button>
            <el-radio-button label="cz">初中</el-radio-button>
            <el-radio-button label="gz">高中</el-radio-button>
            <el-radio-button label="">无</el-radio-button>
          </el-radio-group> -->
          <el-checkbox-group v-model="checkboxGroup1">
            <el-checkbox-button v-for="item in gradeLevelList" :key="item.key" :label="item.key">{{ item.label }}</el-checkbox-button>
          </el-checkbox-group>
        </el-form-item>
        <el-row>
          <el-col :span="6">
            <el-form-item label="排序号" prop="sortNum">
              <el-input v-model="form.sortNum" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="点击次数" prop="clickNum">
              <el-input v-model="form.clickNum" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="分享次数" prop="shareNum">
              <el-input v-model="form.shareNum" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="打印次数" prop="printNum">
              <el-input v-model="form.printNum" />
            </el-form-item>
          </el-col>
        </el-row>
        <!-- <el-form-item label="内容编辑">
          <tiny-mce v-model="form.contents" style="width:80%" />
        </el-form-item> -->
        <el-form-item label="打印图片上传">
          <el-upload
            action=""
            :multiple="true"
            :http-request="myUploadImageMethod"
            list-type="picture-card"
            :on-remove="handleRemove"
            :file-list="fileList"
            :auto-upload="true"
            accept="image/png, image/jpeg"
          >
            <i class="el-icon-plus" />
          </el-upload>
        </el-form-item>
        <el-form-item>
          <el-button @click="handleClose">返回</el-button>
          <el-button v-permission="'btn-menu2cunkuan-knowledge-edit'" type="primary" @click="submit">保存</el-button>
          <el-button type="primary" @click="sortByFileName">自动排序</el-button>
          <el-button type="primary" @click="showDialog=true">排序与设置</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>

    <el-dialog
      ref="dialogxxx"
      :show-close="true"
      :title="'文件列表编辑'"
      :visible.sync="showDialog"
      width="50%"
      center
    >
      <el-table
        ref="multipleTable"
        :data="fileList"
        tooltip-effect="dark"
        style="width: 100%"
        border
      >
        <el-table-column label="缩略图">
          <template slot-scope="scope">
            <a :href="scope.row.pic" target="_blank">
              <el-avatar shape="square" :size="80" :src="scope.row.pic" />
            </a>
          </template>
        </el-table-column>
        <el-table-column label="图片名称">
          <template slot-scope="scope">
            {{ scope.row.name }}
          </template>
        </el-table-column>
        <el-table-column label="是否打印">
          <template slot-scope="scope">
            <el-radio-group v-model="scope.row.printFlag">
              <el-radio-button label="0">不打印</el-radio-button>
              <el-radio-button label="1">打印</el-radio-button>
            </el-radio-group>
          </template>
        </el-table-column>
        <el-table-column label="不分享能否阅读">
          <template slot-scope="scope">
            <el-radio-group v-model="scope.row.canRead">
              <el-radio-button label="0">不能</el-radio-button>
              <el-radio-button label="1">能</el-radio-button>
            </el-radio-group>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="160">
          <template slot-scope="scope">
            <el-button type="primary" icon="el-icon-top" circle @click="move(scope.$index, -1)" />
            <el-button type="primary" icon="el-icon-bottom" circle @click="move(scope.$index, 1)" />
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
  </div>
</template>

<script>
import { saveRecord, getSubject } from '@/api/community/knowledge'
import AliyunOSS from '@/utils/aliyunOSS2'
// import TinyMce from '@/components/Tinymce/index'
const oss = new AliyunOSS()

export default {
  name: 'EditDialog',
  // components: { TinyMce },
  props: {
    visiable: {
      type: Boolean,
      required: true,
      default: false
    },
    formData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      /** 表单 */
      form: {
        pics: []
      },
      /** 校验规则 */
      rules: {
        title: [
          { required: true, message: '请输入标题', trigger: 'blur' }
        ],
        headUrl: [
          { required: true, message: '请输入选择图片', trigger: 'blur' }
        ]
      },
      /** 待上传的文件 */
      upFile: undefined,
      testUrl: '',
      /** 时间格式 */
      timeFormat: 'yyyy-MM-dd HH:mm:ss',
      fileList: [],
      preUploadList: [],
      showDialog: false,
      subjectList: [],
      checkboxGroup1: [],
      gradeLevelList: [
        {
          label: '小学',
          key: 'xx'
        },
        {
          label: '初中',
          key: 'cz'
        },
        {
          label: '高中',
          key: 'gz'
        }
      ]
    }
  },
  computed: {
  },
  watch: {
    formData: function(val) {
      this.upFile = undefined
      this.form = { ...val }
      this.fileList = []
      if (this.form.pics) {
        this.form.pics.forEach(element => {
          const fileObj = {
            name: element.name,
            url: element.pic,
            size: element.size,
            pic: element.pic,
            printFlag: element.printFlag,
            canRead: element.canRead
          }
          this.fileList.push(fileObj)
        })
      } else {
        this.form.pics = []
        this.fileList = []
      }
      if (this.form.gradeLevel) {
        this.checkboxGroup1 = this.form.gradeLevel.split(',')
      } else {
        this.checkboxGroup1 = []
      }
    },
    visiable: function(val) {
      if (val) {
        oss.initClient2()
      }
    }
  },
  mounted() {
    this.show = this.visiable
    this.getSubjectList()
  },
  methods: {
    init() {
      oss.initClient2()
    },
    move(index, action) {
      if (action === 1) {
        if (index !== this.fileList.length - 1) {
          this.fileList[index] = this.fileList.splice(index + 1, 1, this.fileList[index])[0]
        }
      } else {
        if (index !== 0) {
          this.fileList[index] = this.fileList.splice(index - 1, 1, this.fileList[index])[0]
        }
      }
    },
    async getSubjectList() {
      const res = await getSubject()
      if (res.head.ret === 0) {
        this.subjectList = res.data
      }
    },
    /** 返回 */
    handleClose(code) {
      this.$emit('closeDialog', code)
    },
    /** 选择图片*/
    changeHead(file) {
      this.upFile = file.raw
      this.form.headUrl = URL.createObjectURL(file.raw)
      this.testUrl = URL.createObjectURL(file.raw)
      return false
    },
    /** 上传前 */
    beforeUpload(file) {
      return false
    },
    /** 上传pic时 */
    async changeMyPics(file) {
      console.log('我要上传文件：', file)
      if (file.pic) {
        return
      }
      oss.uploadFile(file.raw, (result, error) => {
        if (error) {
          this.$message.error('上传文件失败')
          return -1
        }
        if (result) {
          const fileObj = {
            name: file.name,
            url: result.name,
            size: file.size,
            pic: result.name,
            printFlag: 1,
            canRead: 0
          }
          this.form.pics.push(fileObj)
          this.fileList.push(fileObj)
        }
      })
      // console.log(this.form.pics)
      return false
    },
    async myUploadImageMethod(parms) {
      console.log(parms)
      oss.uploadFile(parms.file, (result, error) => {
        if (error) {
          this.$message.error('上传文件失败')
          return -1
        }
        if (result) {
          const fileObj = {
            name: parms.file.name,
            url: result.name,
            size: parms.file.size,
            pic: result.name,
            printFlag: 1,
            canRead: 0
          }
          console.log('我调用了upload', this.form.pics.length)
          this.form.pics.push(fileObj)
          this.fileList.push(fileObj)
        }
      })
    },
    sortByFileName() {
      this.fileList = this.fileList.sort(function(val1, val2) {
        console.log(val1.name)
        return val1.name > val2.name ? 1 : -1
      })
      this.form.pics = this.form.pics.sort(function(val1, val2) {
        return val1.name > val2.name ? 1 : -1
      })
      this.$message.success('操作成功')
    },
    handleRemove(e) {
      this.fileList = this.fileList.filter(item => {
        return item.pic !== e.pic
      })
      this.form.pics = this.form.pics.filter(item => {
        return item.pic !== e.pic
      })
    },
    /** 提交 */
    submit() {
      if (this.checkboxGroup1) {
        this.form.gradeLevel = this.checkboxGroup1.join(',')
      } else {
        this.form.gradeLevel = ''
      }
      this.$refs['form'].validate(valid => {
        if (valid) {
          if (this.upFile) {
            oss.uploadFile(this.upFile, (result, error) => {
              if (error) {
                this.$message.error('上传文件失败')
                return -1
              }
              if (result) {
                this.form.headUrl = result.url
                this.addUpdateRecord()
              }
            })
          } else {
            // 直接保存
            this.addUpdateRecord()
          }
        } else {
          console.log('submit error')
          return -1
        }
      })
    },
    async addUpdateRecord() {
      // 新增
      if (!this.form.id) {
        this.form.id = ''
      }
      // 更新
      // console.log('fileList=', this.fileList)
      // console.log('form.pics=', this.form.pics)
      this.form.pics = this.fileList
      const res = await saveRecord(this.form)
      if (res.head.ret === 0) {
        this.$message.success('保存成功')
        this.handleClose('1')
      }
    }
  }

}
</script>

<style lang='scss' scoped>
  .avatar-uploader .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }
  .avatar-uploader .el-upload:hover {
    border-color: #409EFF;
  }
  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 178px;
    height: 178px;
    line-height: 178px;
    text-align: center;
  }
  .avatar {
    width: 178px;
    height: 178px;
    display: block;
  }
</style>
