<template>
  <div class="todolist">
    <el-form class="demo-form-inline">
      <el-form-item label="名称：">
        <el-input v-model="formData.name" type="text" style="width: 60%" />
      </el-form-item>
      <el-form-item label="行业分类：">
        <el-select
          v-model="formData.category"
          placeholder="请选择行业分类"
          style="width: 60%"
        >
          <el-option
            v-for="item in industryList"
            :key="item.code"
            :label="item.name"
            :value="item.code"
          />
        </el-select>
      </el-form-item>

      <!-- 默认图片上传区域 -->
      <el-row>
        <el-col :span="12">
          <el-form-item label="预览图：">
            <SingleUpload
              key="listUrl"
              key-word="listUrl"
              :init-url="isEdit ? files.listUrl.pic : ''"
              @updateFile="updateFile"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="资源图：">
            <SingleUpload
              key="resUrl"
              key-word="resUrl"
              :init-url="isEdit ? files.resUrl.pic : ''"
              @updateFile="updateFile"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="是否展示new:" prop="shareFlag">
        <el-radio-group v-model="formData.isNew">
          <el-radio-button label="0">不展示</el-radio-button>
          <el-radio-button label="1">展示</el-radio-button>
        </el-radio-group>
        <span style="padding-left: 20px; padding-right: 15px">期限:</span>
        <el-date-picker
          v-model="formData.newFlagBeforeDate"
          value-format="yyyy-MM-dd"
          type="date"
          placeholder="选择展示期限"
        />
      </el-form-item>

      <!-- A4/A5切换区域 -->
      <el-tabs v-model="tabsActiveName" type="card">
        <el-tab-pane label="A4" name="first">
          <el-row>
            <el-col :span="12">
              <el-form-item label="预览图：">
                <SingleUpload
                  key="listUrl"
                  key-word="listUrl"
                  :init-url="
                    isEdit && filesA4.listUrl && filesA4.listUrl.pic
                      ? filesA4.listUrl.pic
                      : ''
                  "
                  @updateFile="updateFileA4"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="资源图：">
                <SingleUpload
                  key="resUrl"
                  key-word="resUrl"
                  :init-url="
                    isEdit && filesA4.resUrl && filesA4.resUrl.pic
                      ? filesA4.resUrl.pic
                      : ''
                  "
                  @updateFile="updateFileA4"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-tab-pane>
        <el-tab-pane label="A5" name="second">
          <el-row>
            <el-col :span="12">
              <el-form-item label="预览图：">
                <SingleUpload
                  key="listUrl"
                  key-word="listUrl"
                  :init-url="
                    isEdit && filesA5.listUrl && filesA5.listUrl.pic
                      ? filesA5.listUrl.pic
                      : ''
                  "
                  @updateFile="updateFileA5"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="资源图：">
                <SingleUpload
                  key="resUrl"
                  key-word="resUrl"
                  :init-url="
                    isEdit && filesA5.resUrl && filesA5.resUrl.pic
                      ? filesA5.resUrl.pic
                      : ''
                  "
                  @updateFile="updateFileA5"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-tab-pane>
      </el-tabs>

      <el-form-item>
        <el-button @click="handleBack">返回</el-button>
        <el-button
          v-permission="'btn-menuA4-material-edit'"
          type="primary"
          @click="handleSave"
          >保存</el-button
        >
      </el-form-item>
    </el-form>
  </div>
</template>
<script>
import { saveOfficeMaterial } from '@/api/office/material'
import { fetchDictList } from '@/api/system/dict'
import SingleUpload from '../SingleUpload/index'
import AliyunOSS from '@/utils/aliyunOSS'

const oss = new AliyunOSS()
const TIP_MESSAGES = {
  listUrl: '请选择预览图',
  resUrl: '请选择资源图'
}
const TIP_MESSAGES_A4 = {
  listUrl: '请选择A4预览图',
  resUrl: '请选择A4资源图'
}
const TIP_MESSAGES_A5 = {
  listUrl: '请选择A5预览图',
  resUrl: '请选择A5资源图'
}

export default {
  components: {
    SingleUpload
  },
  props: {
    formData: {
      type: Object,
      default: () => { return {} }
    }
  },
  data() {
    return {
      uploadFiles: { listUrl: {}, resUrl: {}, topUrl: {}, downUrl: {}, leftUrl: {}, rightUrl: {} },
      uploadFilesA4: { listUrl: {}, resUrl: {}, topUrl: {}, downUrl: {}, leftUrl: {}, rightUrl: {} },
      uploadFilesA5: { listUrl: {}, resUrl: {}, topUrl: {}, downUrl: {}, leftUrl: {}, rightUrl: {} },
      industryList: [],
      tabsActiveName: 'first'
    }
  },
  computed: {
    isEdit() {
      if (this.formData && this.formData.id) {
        return true
      } else {
        return false
      }
    },
    /** 编辑的数据 */
    files() {
      if (this.isEdit) {
        return this.formData.resMap || {}
      } else {
        return {}
      }
    },
    /** A4编辑的数据 */
    filesA4() {
      if (this.isEdit) {
        return this.formData.resMapA4 ? this.formData.resMapA4 : {}
      } else {
        return {}
      }
    },
    /** A5编辑的数据 */
    filesA5() {
      if (this.isEdit) {
        return this.formData.resMapA5 ? this.formData.resMapA5 : {}
      } else {
        return {}
      }
    },
    /** 编辑ID */
    addId() {
      if (this.isEdit) {
        return this.formData.id
      } else {
        return ''
      }
    }
  },
  mounted() {
    this.fetchIndustryData()
  },
  methods: {
    handleBack(val) {
      this.$emit('handleBack', val)
    },
    /** 子组件上传文件 */
    updateFile(key, file) {
      this.uploadFiles[key] = {
        file,
        key
      }
    },
    updateFileA4(key, file) {
      this.uploadFilesA4[key] = {
        file,
        key
      }
    },
    updateFileA5(key, file) {
      this.uploadFilesA5[key] = {
        file,
        key
      }
    },
    /** 校验文件是否上传 */
    validateFiles() {
      let flag = true

      // 合并默认文件
      if (this.isEdit) {
        Object.keys(this.files).forEach(key => {
          const item = this.uploadFiles[key]
          if (!item || !item.hasOwnProperty('file')) {
            this.uploadFiles[key] = this.files[key]
          }
        })

        Object.keys(this.filesA4).forEach(key => {
          const item = this.uploadFilesA4[key]
          if (!item || !item.hasOwnProperty('file')) {
            this.uploadFilesA4[key] = this.filesA4[key]
          }
        })

        Object.keys(this.filesA5).forEach(key => {
          const item = this.uploadFilesA5[key]
          if (!item || !item.hasOwnProperty('file')) {
            this.uploadFilesA5[key] = this.filesA5[key]
          }
        })
      }

      // 校验默认图片
      Object.keys(this.uploadFiles).forEach(key => {
        if (!flag) {
          return
        }
        const file = this.uploadFiles[key]
        if (TIP_MESSAGES[key] && file && !file.file && (!file.pic || file.pic.indexOf('http') < 0)) {
          this.$message.error(TIP_MESSAGES[key])
          flag = false
        }
      })

      // 校验A4
      Object.keys(this.uploadFilesA4).forEach(key => {
        if (!flag) {
          return
        }
        const file = this.uploadFilesA4[key]
        if (TIP_MESSAGES_A4[key] && file && !file.file && (!file.pic || file.pic.indexOf('http') < 0)) {
          this.$message.error(TIP_MESSAGES_A4[key])
          flag = false
        }
      })

      // 校验A5
      Object.keys(this.uploadFilesA5).forEach(key => {
        if (!flag) {
          return
        }
        const file = this.uploadFilesA5[key]
        if (TIP_MESSAGES_A5[key] && file && !file.file && (!file.pic || file.pic.indexOf('http') < 0)) {
          this.$message.error(TIP_MESSAGES_A5[key])
          flag = false
        }
      })

      return flag
    },
    /** 保存 */
    async handleSave() {
      if (this.validateFiles()) {
        // 处理默认图片
        const upFiles = []
        const picDto = {}
        Object.keys(this.uploadFiles).forEach(key => {
          const item = this.uploadFiles[key]
          if (item.file) {
            item.file.key = key
            upFiles.push(item.file)
          } else {
            picDto[key] = { pic: item.pic }
          }
        })

        // 处理A4图片
        const upFilesA4 = []
        const picDtoA4 = {}
        Object.keys(this.uploadFilesA4).forEach(key => {
          const item = this.uploadFilesA4[key]
          if (item.file) {
            item.file.key = key
            upFilesA4.push(item.file)
          } else {
            picDtoA4[key] = { pic: item.pic }
          }
        })

        // 处理A5图片
        const upFilesA5 = []
        const picDtoA5 = {}
        Object.keys(this.uploadFilesA5).forEach(key => {
          const item = this.uploadFilesA5[key]
          if (item.file) {
            item.file.key = key
            upFilesA5.push(item.file)
          } else {
            picDtoA5[key] = { pic: item.pic }
          }
        })

        // 上传所有文件
        this.uploadAllFiles(upFiles, upFilesA4, upFilesA5, picDto, picDtoA4, picDtoA5)
      }
    },

    uploadAllFiles(upFiles, upFilesA4, upFilesA5, picDto, picDtoA4, picDtoA5) {
      // 先上传默认文件
      oss.uploadFiles(upFiles, (results, error) => {
        if (error) {
          this.$message.error('文件上传失败，请检查')
          return
        }
        if (results) {
          results.forEach(res => {
            picDto[res.key] = { pic: res.name }
          })
        }

        // 上传A4文件
        oss.uploadFiles(upFilesA4, (resultsA4, errorA4) => {
          if (errorA4) {
            this.$message.error('A4文件上传失败，请检查')
            return
          }
          if (resultsA4) {
            resultsA4.forEach(res => {
              picDtoA4[res.key] = { pic: res.name }
            })
          }

          // 上传A5文件
          oss.uploadFiles(upFilesA5, (resultsA5, errorA5) => {
            if (errorA5) {
              this.$message.error('A5文件上传失败，请检查')
              return
            }
            if (resultsA5) {
              resultsA5.forEach(res => {
                picDtoA5[res.key] = { pic: res.name }
              })
            }
            this.save(picDto, picDtoA4, picDtoA5)
          })
        })
      })
    },

    save(picDto, picDtoA4, picDtoA5) {
      // 保存办公素材（包含默认图片和A4/A5数据）
      const params = {
        id: this.addId,
        name: this.formData.name,
        category: this.formData.category,
        isNew: this.formData.isNew,
        newFlagBeforeDate: this.formData.newFlagBeforeDate,
        picDto: picDto,
        picDtoA4: picDtoA4,
        picDtoA5: picDtoA5
      }

      console.log('saveOfficeMaterial params', params)
      saveOfficeMaterial(params).then(res => {
        if (res.head.ret === 0) {
          this.$message.success('保存成功')
          this.handleBack('1')
        } else {
          this.$message.error(res.head.msg)
        }
      })
    },
    async fetchIndustryData() {
      try {
        const res = await fetchDictList({ type: 'industry' })
        if (res.head && res.head.ret === 0) {
          this.industryList = res.data || []
        }
      } catch (error) {
        console.error('获取行业列表失败:', error)
      }
    }
  }
}
</script>
