export default {
  starpany: 'STARPANY',
  loading: 'Loading...',
  day: 'Day',
  title: {
    'communty-agreements': 'STARPANY Community Specification',
    'doc-operation-instructions': 'FAQ',
    'feed-detail': 'Detail',
    'note-detail': 'STARPANY Note',
    'oauth-transfer': 'Authorization callback',
    'reply-list': 'Comment reply',
    'share-about-us': 'starpany',
    'share-device': 'Share STARPANY',
    'user-protocol': 'User Agreement',
    'study-note-detail': 'Detail',
    download: 'STARPANY Download'
  },

  // 社区规范
  'communty-agreements': {
    'dear-starpany-user': 'Dear STARPANY User'
  },
  // 安卓文档导入说明
  'doc-import-andriod': {
    'title': 'How to import a document into APP?',
    'step1': 'Send the document to a mobile phone by email, etc.',
    'step2': 'Open Starpany App, and choose document type, then you can see the document',
    'step3': 'When there are too many documents, you can search for the document by search function',
    'tips': 'If you fail to import the document through the above method, please find the document needing to be printed in [File Management] on the mobile phone, choose the document, and choose Starpany App in Opening Mode.'
  },
  // IOS文档导入说明
  'doc-import-ios': {
    'title': 'How to import a document into APP in IOS?',
    'step1': 'Send the document to a mobile phone by email, etc.',
    'step2': 'Open the file, click “...” at the top right corner, and choose “Open by Other app”',
    'step3': 'Choose “Starpany” to open'
  },

  // 动态详情
  'feed-detail': {
    'add-to-friends': 'Add to friends',
    'follow': 'follow',
    'followed': 'followed',
    'mutual-followed': 'mutual-followed',
    'i-want-to-say': 'I want to say...',
    release: 'Post',
    'delete-tip': 'Are you sure you want to delete this comment?',
    'delete-success': 'Deleted success',
    'data-empty': 'The dynamic does not exist or has been deleted',
    'share-feed': 'Shared a new dynamic',
    'no-comments': 'No one has commented yet',
    comment: 'Comment',
    reply: 'Reply',
    'view-all': 'View all ',
    comments: 'comments',
    'dear-starpany-user': 'feed has been deleted, backing'
  },

  // 动态详情
  'study-note-detail': {
    'title': 'Study Note',
    'add-to-friends': 'Add to friends',
    'follow': 'follow',
    'followed': 'followed',
    'mutual-followed': 'mutual-followed',
    'i-want-to-say': 'Add a comment',
    release: 'Release',
    'delete-tip': 'Are you sure you want to delete this comment?',
    'delete-success': 'Deleted success',
    'data-empty': 'The dynamic does not exist or has been deleted',
    'share-feed': 'Shared a new dynamic',
    'no-comments': 'No one has commented yet',
    comment: 'Comment',
    reply: 'Reply',
    'view-all': 'View all ',
    comments: 'comments',
    'dear-starpany-user': 'feed has been deleted, backing'
  },

  // 纸条详情
  'reply-list': {
    'view-original-dynamic': 'View original dynamic',
    reply: 'Reply',
    'comment-replies': 'Comment replies',
    'data-empty': 'The comment does not exist or has been deleted',
    'delete-tip': 'Are you sure you want to delete this reply?',
    'delete-success': 'Deleted success',
    replies: 'Replies'
  },

  'share-about-us': {
    'download-now': 'Download Now',
    share: 'Share',
    'product-manual': 'STARPANY Product Manual'
  },

  'share-device': {
    anonymous: 'Anonymous',
    'share-theme': 'Share theme',
    countdown: 'Countdown',
    organizer: 'Organizer',
    'sharing-has-expired': 'Sharing has expired',
    'enter-your-content': 'Enter your content',
    'add-image': 'Add image',
    'send-to-friends': 'Send to friends',
    'enter-the-content': 'Enter the content',
    sending: 'Sending',
    'image-upload-failed': 'Image upload failed',
    'send-success': 'Send success'
  },

  'user-protocol': {
    'user-agreement': 'User Agreement'
  }
}
