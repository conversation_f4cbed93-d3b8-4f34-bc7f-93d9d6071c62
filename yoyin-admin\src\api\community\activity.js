import request from '@/utils/request'

// 翻页查询
const LOAD_DATA_URL = '/platform/gam/community/v1/feed/findfeednotepage'
// 删除记录
const DELETE_RECORD_URL = '/platform/gam/community/v1/feed/del'
// 保存记录
const SAVE_RECORD_URL = '/platform/gam/community/v1/feed/addfeednote'
// 保存记录-延期发布
const SAVE_RECORD_DELAYED_URL = '/platform/gam/community/v1/feed/addfeednotedelayed'

// 标签列表
const TAGSLIST_URL = '/platform/gam/community/v1/label/findlabellist'
// 保存记录
const SET_STICK_RECORD_URL = '/platform/gam/community/v1/feed/updateStickFeed'
const CANCEL_STICK_RECORD_URL = '/platform/gam/community/v1/feed/cancelStickFeed'
const GET_BY_ID_URL = '/platform/gam/community/v1/feed/getfeednoteobject'
const GET_SUBJECT_LIST_URL = '/platform/gam/community/v1/feed/getsubjectlist'
const UPDATE_TAG_URL = '/platform/gam/community/v1/feed/updateTag'

const GIVE_LIKE_URL = '/platform/gam/community/v1/feed/givelike'
const DEL_CACHE_URL = '/platform/gam/community/v1/feed/deleteCacheById'

// 翻页查询(定期发布)
const LOAD_DELAYED_DATA_URL = '/platform/gam/community/v1/feed/findfeednotedelayedpage'
// 删除记录
const DELETE_DELAYED_RECORD_URL = '/platform/gam/community/v1/feed/delfeednotedelayed'

/**
 * 查询列表数据
 * @param params 参数
 */
export const fetchListByPage = async params => {
  return request({
    url: LOAD_DATA_URL,
    method: 'get',
    params
  })
}

/**
 * 删除记录
 * @param ids 记录id
 */
export const deleteRecord = async(params) => {
  return request({
    url: DELETE_RECORD_URL,
    method: 'get',
    params
  })
}

/**
 * 标签翻页查询
 * @param {Number} page 页码 从1开始
 */
export const findLabelPage = async(params) => {
  return request({
    url: TAGSLIST_URL,
    method: 'get',
    params
  })
}

/**
   * 保存记录
   * @param ids 记录id
   */
export const saveRecord = async params => {
  return request({
    url: SAVE_RECORD_URL,
    method: 'post',
    data: params
  })
}

export const saveRecordDelayed = async params => {
  return request({
    url: SAVE_RECORD_DELAYED_URL,
    method: 'post',
    data: params
  })
}

/**
   * 保存记录
   * @param ids 记录id
   */
export const updateStickFeed = async params => {
  return request({
    url: SET_STICK_RECORD_URL,
    method: 'post',
    data: params
  })
}

/**
   * 保存记录
   * @param ids 记录id
   */
export const cancelStickFeed = async params => {
  return request({
    url: CANCEL_STICK_RECORD_URL,
    method: 'post',
    data: params
  })
}

/**
 * 查询具体实体
 * @param {feedid} feedid feedNote的id
 */
export const getById = async(params) => {
  return request({
    url: GET_BY_ID_URL,
    method: 'get',
    params
  })
}

/**
 * 查询具体实体
 * @param {feedid} feedid feedNote的id
 */
export const getSubjectList = async() => {
  return request({
    url: GET_SUBJECT_LIST_URL,
    method: 'get'
  })
}

/**
   * 保存记录
   * @param ids 记录id
   */
export const updateFeedNoteTag = async params => {
  return request({
    url: UPDATE_TAG_URL,
    method: 'post',
    data: params
  })
}

/**
   * 保存记录
   * @param ids 记录id
   */
export const giveLike = async params => {
  return request({
    url: GIVE_LIKE_URL,
    method: 'post',
    data: params
  })
}

export const delCacheLabelById = async params => {
  return request({
    url: DEL_CACHE_URL,
    method: 'post',
    data: params
  })
}

/**
 * 查询列表数据
 * @param params 参数
 */
export const fetchDelayedListByPage = async params => {
  return request({
    url: LOAD_DELAYED_DATA_URL,
    method: 'get',
    params
  })
}

/**
 * 删除记录
 * @param ids 记录id
 */
export const deleteDelayedRecord = async(params) => {
  return request({
    url: DELETE_DELAYED_RECORD_URL,
    method: 'get',
    params
  })
}
