/*
 * @Description: In User Settings Edit
 * @Author: your name
 * @Date: 2019-10-16 11:54:09
 * @LastEditTime: 2019-10-23 09:37:00
 * @LastEditors: Please set LastEditors
 */
import request from '@/utils/request'

export function getOSSOptionFromServer() {
  return request({
    url: '/api/log/event/v1/storage/getosstoken',
    method: 'get'
  })
}

export const getSystemConfig = async() => {
  return request({
    url: '/platform/gam/community/v1/config/findsysconfig',
    method: 'get'
  })
}

export const updateConfig = async(params) => {
  return request({
    url: '/platform/gam/community/v1/config/update',
    method: 'post',
    data: params
  })
}
