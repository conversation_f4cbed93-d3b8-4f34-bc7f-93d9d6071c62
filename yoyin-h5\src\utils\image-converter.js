// canvas转dataURL：canvas对象、转换格式、图像品质
function canvasToDataURL(canvas, format, quality) {
  return canvas.toDataURL(format || 'image/jpeg', quality || 1.0)
}

// DataURL转Blob对象
function dataURLToBlob(dataurl) {
  var arr = dataurl.split(',')
  var mime = arr[0].match(/:(.*?);/)[1]
  var bstr = atob(arr[1])
  var n = bstr.length
  var u8arr = new Uint8Array(n)
  while (n--) {
    u8arr[n] = bstr.charCodeAt(n)
  }
  return new Blob([u8arr], { type: mime })
}

// canvas转Blob
export const canvasToBlob = (canvas, cb) => {
  cb(dataURLToBlob(canvasToDataURL(canvas)))
}
