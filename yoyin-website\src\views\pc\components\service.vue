<template>
  <div>
      <div class="menu">
        <div :class="addMenuClass('yoyinprinter')" @click="menu='yoyinprinter'">XPLife</div>
        <!-- <div :class="addMenuClass('a4printer')" @click="menu='a4printer'">A4打印机</div> -->
        <div :class="addMenuClass('easyprinter')" @click="menu='easyprinter'">芯意贴</div>
      </div>
      <div v-show="menu==='yoyinprinter'">
        <div class="yoyinprinter-head"></div>
        <!-- <div id="yoyinprinterCanvas" ref="yoyinprinterCanvas" style="height: 784px; min-width:1200px"></div> -->
        <div class="yoyinprinter-1"></div>
        <div class="yoyinprinter-2"></div>
        <div class="yoyinprinter-3"></div>
        <div class="yoyinprinter-4"></div>
        <div class="yoyinprinter-5"></div>
      </div>
      <div v-show="menu==='a4printer'">
        <div id="a4printerCanvas" ref="a4printerCanvas" style="height: 784px; min-width:1200px"></div>
        <div class="a4printer-1"></div>
        <div class="a4printer-2"></div>
        <div class="a4printer-3"></div>
        <div class="a4printer-4"></div>
        <div class="a4printer-5"></div>
      </div>
      <div v-show="menu==='easyprinter'">
        <div id="easyprinterCanvas" ref="easyprinterCanvas" style="height: 784px; min-width:1200px"></div>
        <div class="easyprinter-1"></div>
        <div class="easyprinter-2"></div>
        <div class="easyprinter-3"></div>
        <div class="easyprinter-4"></div>
        <div class="easyprinter-5"></div>
      </div>
  </div>
</template>

<script>
import SVGA from 'svgaplayerweb'
export default {
  name: 'Service',
  data () {
    return {
      a4printerCanvasUrl: 'https://m.yoyin.net/h5/img/web/service/svga/A4header.svga',
      easyprinterCanvasUrl: 'https://m.yoyin.net/h5/img/web/service/svga/easyprinterheader.svga',
      yoyinprinterCanvasUrl: 'https://m.yoyin.net/h5/img/web/service/svga/yoyinprinterheader.svga',
      menu: 'yoyinprinter'
    }
  },
  mounted () {
    // this.initMachineSVGA('yoyinprinterCanvas', this.yoyinprinterCanvasUrl)
    this.initMachineSVGA('a4printerCanvas', this.a4printerCanvasUrl)
    this.initMachineSVGA('easyprinterCanvas', this.easyprinterCanvasUrl)
  },
  methods: {
    initMachineSVGA (idValue, url) {
      let player = new SVGA.Player('#' + idValue)
      let parser = new SVGA.Parser('#' + idValue)
      // this.imageUrl 定义一个参数接收url
      parser.load(url, function (videoItem) {
        player.setVideoItem(videoItem)
        player.startAnimation()
      })
    },
    addMenuClass (item) {
      if (item === this.menu) {
        return 'menu-item selected'
      } else {
        return 'menu-item'
      }
    }
  }
}
</script>

<style scoped>
.menu {
  padding-top: 50px;
  display:flex;
  justify-content: center;
  align-items: center;
}

.menu-item {
  cursor: pointer;
  width: 97px;
  height: 42px;
  border-radius: 179px 179px 179px 179px;
  opacity: 1;
  line-height: 42px;
  margin-right: 47px;
}

.selected {
  background: #63C184;
  border: 2px solid #63C184;
  color: #ffffff;
}

.a4printer-1 {
  background-image: url(https://m.yoyin.net/h5/img/web/service/a4printer-1.png);
  background-repeat: no-repeat;
  background-size: 1921px 882px;
  height: 882px;
  background-position: 50% 50%;
}
.a4printer-2 {
  background-image: url(https://m.yoyin.net/h5/img/web/service/a4printer-2.png);
  background-repeat: no-repeat;
  background-size: 1921px 966px;
  height: 882px;
  background-position: 50% 50%;
}
.a4printer-3 {
  background-image: url(https://m.yoyin.net/h5/img/web/service/a4printer-3.png);
  background-repeat: no-repeat;
  background-size: 1921px 933px;
  height: 882px;
  background-position: 50% 50%;
}
.a4printer-4 {
  background-image: url(https://m.yoyin.net/h5/img/web/service/a4printer-4.png);
  background-repeat: no-repeat;
  background-size: 1921px 1013px;
  height: 882px;
  background-position: 50% 50%;
}
.a4printer-5 {
  background-image: url(https://m.yoyin.net/h5/img/web/service/a4printer-5.png);
  background-repeat: no-repeat;
  background-size: 1921px 941px;
  height: 882px;
  background-position: 50% 50%;
}

.easyprinter-1 {
  background-image: url(https://m.yoyin.net/h5/img/web/service/easyprinter-1.png);
  background-repeat: no-repeat;
  background-size: 1921px 994px;
  height: 882px;
  background-position: 50% 50%;
}

.easyprinter-2 {
  background-image: url(https://m.yoyin.net/h5/img/web/service/easyprinter-2.png);
  background-repeat: no-repeat;
  background-size: 1921px 982px;
  height: 882px;
  background-position: 50% 50%;
}
.easyprinter-3 {
  background-image: url(https://m.yoyin.net/h5/img/web/service/easyprinter-3.png);
  background-repeat: no-repeat;
  background-size: 1921px 947px;
  height: 882px;
  background-position: 50% 50%;
}
.easyprinter-4 {
  background-image: url(https://m.yoyin.net/h5/img/web/service/easyprinter-4.png);
  background-repeat: no-repeat;
  background-size: 1921px 947px;
  height: 882px;
  background-position: 50% 50%;
}
.easyprinter-5 {
  background-image: url(https://m.yoyin.net/h5/img/web/service/easyprinter-5.png);
  background-repeat: no-repeat;
  background-size: 1921px 949px;
  height: 882px;
  background-position: 50% 50%;
}
.yoyinprinter-head {
  background-image: url(https://m.yoyin.net/h5/img/web/service/svga/yoyinprinterheader.png);
  background-repeat: no-repeat;
  /* background-size: 1921px 994px; */
  height: 784px;
  background-position: 50% 50%;
}
.yoyinprinter-1 {
  background-image: url(https://m.yoyin.net/h5/img/web/service/yoyinprinter-1.png);
  background-repeat: no-repeat;
  background-size: 1921px 994px;
  height: 882px;
  background-position: 50% 50%;
}
.yoyinprinter-2 {
  background-image: url(https://m.yoyin.net/h5/img/web/service/yoyinprinter-2.png);
  background-repeat: no-repeat;
  background-size: 1921px 1013px;
  height: 882px;
  background-position: 50% 50%;
}
.yoyinprinter-3 {
  background-image: url(https://m.yoyin.net/h5/img/web/service/yoyinprinter-3.png);
  background-repeat: no-repeat;
  background-size: 1921px 1013px;
  height: 882px;
  background-position: 50% 50%;
}
.yoyinprinter-4 {
  background-image: url(https://m.yoyin.net/h5/img/web/service/yoyinprinter-4.png);
  background-repeat: no-repeat;
  background-size: 1921px 1013px;
  height: 882px;
  background-position: 50% 50%;
}
.yoyinprinter-5 {
  background-image: url(https://m.yoyin.net/h5/img/web/service/yoyinprinter-5.png);
  background-repeat: no-repeat;
  background-size: 1921px 1013px;
  height: 882px;
  background-position: 50% 50%;
}
</style>
