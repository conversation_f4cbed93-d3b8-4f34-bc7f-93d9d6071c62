<template>
  <div class="faq">
    <div class="faq-title"><span>蓝牙无法连接？</span></div>
    <div class="faq-content">
      <p class="first-line">请确认您的手机蓝牙已经开启；</p>
      <p class="first-line">请在安装APP时允许应用申请的全部权限请求；</p>
      <p class="first-line">如果可以搜索的到打印机但连接不上时，可能是打印机被手机系统设置连接了。只需断开并删除手机系统设置内的连接，并返回APP内重新连接；</p>
      <p class="first-line">如果可以搜索的到打印机但连接不上时，还可能是打印机被其他手机连接了，请在其他手机上断开连接，或者双击打印机的星星按键断开连接再进行重连。</p>
    </div>

  </div>
</template>

<script>
export default {
  data () {
    return {
      moduleKey: 'faq'
    }
  },
  async mounted () { },
  methods: {},
  components: {}
}
</script>

<style lang="scss">
@import '../../assets/scss/faq.scss'; // 全局faq自定义的css样式
</style>
