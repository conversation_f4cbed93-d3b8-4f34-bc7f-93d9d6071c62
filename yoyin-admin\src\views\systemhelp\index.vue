<!--
 * @Description: In User Settings Edit
 * @Author: your name
 * @Date: 2019-10-16 11:28:20
 * @LastEditTime: 2019-10-16 11:38:48
 * @LastEditors: Please set LastEditors
 -->
<template>
  <div class="app-container">
    <div class="table-query-container">
      <el-button v-permission="'btn-menuSystemsetting-help-edit'" @click="handleAdd">新增</el-button>
    </div>
    <div class="table-query-container">
      类型：<el-radio-group v-model="params.type" @change="params.pageNo=1;getList()">
        <el-radio-button label="">全部</el-radio-button>
        <el-radio-button label="video">教程</el-radio-button>
        <el-radio-button label="text">常见问题</el-radio-button>
        <el-radio-button label="pdf">说明书</el-radio-button>
      </el-radio-group>
    </div>
    <div class="table-query-container">
      打印机型号：
      <el-select v-model="params.printerType" placeholder="选择打印机类型" @change="params.pageNo=1;getList()">
        <el-option label="全部" value="" />
        <el-option
          v-for="item in printerTypeConstantList"
          :key="item.value"
          :label="item.value"
          :value="item.value"
        />
      </el-select>
    </div>
    <div class="dialog-container">
      <EditDialog :visiable="showDialog" :form-data="helpItem" @closeDialog="closeDialog" />
    </div>

    <div class="table-container">
      <el-table
        ref="multipleTable"
        :data="list"
        tooltip-effect="dark"
        style="width: 100%"
        border
      >
        <el-table-column label="排序" width="55">
          <template slot-scope="scope">{{ scope.row.sortNum }}</template>
        </el-table-column>
        <el-table-column label="标题">
          <template slot-scope="scope">{{ scope.row.title }}</template>
        </el-table-column>
        <el-table-column label="封面">
          <template slot-scope="scope">
            <div v-if="scope.row.coverUrl">
              <img :src="scope.row.coverUrl" style="width: 80px; height:80px;">
            </div>
          </template>
        </el-table-column>
        <!-- <el-table-column label="多语言代码">
          <template slot-scope="scope">{{ scope.row.localLanguageCode }}</template>
        </el-table-column> -->
        <el-table-column label="打印机类型" :formatter="printerTypeFormatter" prop="printerType" />
        <el-table-column label="类型" :formatter="typeFormatter" prop="type" />
        <el-table-column label="url地址">
          <template slot-scope="scope">{{ scope.row.url }}</template>
        </el-table-column>
        <el-table-column label="操作">
          <template slot-scope="scope">
            <el-button size="mini" @click="handleEdit(scope.$index, scope.row)">编辑</el-button>
            <el-button
              v-permission="'btn-menuSystemsetting-help-edit'"
              size="mini"
              type="danger"
              @click="handleDeleteRow(scope.$index, scope.row)"
            >删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="pagination-container" style="margin-top:20px;">
      <el-pagination
        :current-page="params.pageNo"
        :page-sizes="[10, 20, 30, 40]"
        :page-size="params.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>
<script>
import { fetchListByPage, deleteItem } from '@/api/systemhelp/systemhelp'
import EditDialog from './component/EditDialog'
import { printerTypeConstantList2 } from '@/consts'
export default {
  components: {
    EditDialog
  },
  data() {
    return {
      // 查询参数
      params: {
        pageNo: 1,
        pageSize: 10,
        type: ''
      },
      /** 列表 */
      list: [],
      /** 总条数 */
      total: 0,
      /** 消息体 */
      helpItem: { },
      /** 勾选数据 */
      multipleSelection: [],
      /** 时间段 */
      timeRange: '',
      /** 时间格式 */
      timeFormat: 'yyyy-MM-dd',
      /** 显示编辑框 */
      showDialog: false,
      /** 科目列表 */
      subjectList: [],
      printerTypeConstantList: printerTypeConstantList2
    }
  },
  mounted() {
    this.getList()
  },
  methods: {
    /** 获取列表 */
    async getList() {
      const res = await fetchListByPage(this.params)
      if (res.head.ret === 0) {
        this.list = res.data.result
        this.total = res.data.totalCount
      }
    },
    /** 切换每页条数 */
    handleSizeChange(val) {
      this.params.pageSize = val
      this.getList()
    },
    /** 切换当前页 */
    handleCurrentChange(val) {
      this.params.pageNo = val
      this.getList()
    },
    /** 编辑 */
    handleEdit(index, row) {
      this.helpItem = row
      this.showDialog = true
    },
    printerTypeFormatter(row, column) {
      if (row.printerType === undefined || row.printerType === null) {
        return ''
      }
      const type = row.printerType.trim() + ','
      let result = ''
      this.printerTypeConstantList.forEach(item => {
        if (type && type.indexOf(item.key + ',') > -1) {
          result += item.value + ';'
        }
      })
      return result
    },
    typeFormatter(row, column) {
      if (row.type === undefined || row.type === null) {
        return ''
      }
      const type = row.type.trim()
      if (type === 'video') {
        return '教程'
      } else if (type === 'text') {
        return '常见问题'
      } else if (type === 'pdf') {
        return '说明书'
      } else {
        return '其他'
      }
    },
    /** 删除单行 */
    async handleDeleteRow(index, row) {
      const params = {
        id: row.id
      }
      this.$confirm('此操作将永久删除该记录, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteItem(params).then(res => {
          if (res.head.ret === 0) {
            this.$message({
              type: 'success',
              message: '删除成功!'
            })
            this.getList()
          }
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
    },
    /** 查询 */
    handleQuery() {
      this.getList()
    },
    /** 新增 */
    handleAdd() {
      this.helpItem = {}
      this.showDialog = true
    },
    /** 关闭弹框 */
    closeDialog(code) {
      this.showDialog = false
      this.helpItem = {}
      // 保存code
      if (code === '1') {
        this.getList()
      }
    }
  }
}
</script>

<style lang="scss" scoped>

</style>
