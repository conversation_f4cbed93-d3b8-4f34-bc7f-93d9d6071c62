<template>
  <div class="font">
    <el-form label-width="100px">
      <el-form-item label="语种" prop="localeCode">
        <el-radio-group v-model="localeCode">
          <el-radio-button v-for="item in localeData" :key="item.id" :label="item.id">{{ item.name }}</el-radio-button>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="字体文件：">
        <el-upload
          action=""
          accept=".ttf,.eot,.otf,.woff,.svg"
          :auto-upload="false"
          :show-file-list="false"
          :on-change="onChangeFont"
        >
          <div slot="tip"><a style="color:#409EFF" :href="fontFile.url" target="_blank">{{ fontFile.url }}</a> </div>
          <el-button type="primary" size="small">上传字体文件</el-button>
        </el-upload>
      </el-form-item>
      <el-form-item label="预览图：">
        <el-upload
          action="OSS上传路径，必填"
          list-type="picture-card"
          accept="image/png, image/jpeg"
          :before-upload="handelBeforeUpload"
          :http-request="upLoadNothing"
          :on-preview="handlePictureCardPreview"
          :before-remove="handleBeforeRemove"
          :file-list="previewFile"
        >
          <i class="el-icon-plus" />
        </el-upload>
        <el-dialog :visible.sync="dialogVisible">
          <img width="100%" :src="dialogImageUrl" alt="">
        </el-dialog>

      </el-form-item>
      <el-form-item label="是否展示new" prop="shareFlag">
        <el-radio-group v-model="isNew">
          <el-radio-button label="0">不展示</el-radio-button>
          <el-radio-button label="1">展示</el-radio-button>
        </el-radio-group>
      </el-form-item>
      <el-form-item>
        <el-button @click="handleBack">返回</el-button>
        <el-button v-permission="'btn-xeasylabel-material-edit'" type="primary" @click="handleSave">保存</el-button>
      </el-form-item>
    </el-form>

  </div>
</template>
<script>
import { getBase64, uuid } from '@/utils/index'
import { saveRecord } from '@/api/xeasylabel/material'
import AliyunOSS from '@/utils/aliyunOSS'
import { FILE_URL } from '@/utils/consts'
const oss = new AliyunOSS()
export default {
  props: {
    id: {
      type: String,
      default: ''
    },
    formData: {
      type: Object,
      default: () => { return { } }
    }
  },
  data() {
    return {
      localeCode: 'zh_CN',
      localeData: [{
        'id': 'zh_CN',
        'name': '简体'
      }, {
        'id': 'zh_TW',
        'name': '繁体'
      }, {
        'id': 'en_US',
        'name': '英语'
      }, {
        'id': 'ko_KR',
        'name': '韩语'
      }, {
        'id': 'ja_JP',
        'name': '日语'
      }, {
        'id': 'ru_RU',
        'name': '俄语'
      }, {
        'id': 'fr_FR',
        'name': '法语'
      }, {
        'id': 'es_ES',
        'name': '西班牙语(欧)'
      }, {
        'id': 'es_LA',
        'name': '西班牙语(拉美)'
      }, {
        'id': 'ar_AE',
        'name': '阿拉伯'
      }, {
        'id': 'de_DE',
        'name': '德语'
      }, {
        'id': 'it_IT',
        'name': '意大利'
      }, {
        'id': 'pt_PT',
        'name': '葡萄牙'
      }, {
        'id': 'th_TH',
        'name': '泰语'
      }, {
        'id': 'vi_VI',
        'name': '越南语'
      }],
      /** 字体文件 */
      fontFile: {
        name: '',
        url: '',
        file: undefined
      },
      /** 预览图 */
      previewFile: [
      ],
      /** 已选择图片 */
      hasUploadPre: false,
      /** 预览图片框 */
      dialogVisible: false,
      dialogImageUrl: '',
      info: undefined,
      isNew: 0,
      /** 提交的ID */
      addId: ''
    }
  },
  watch: {
    formData: {
      immediate: true,
      handler: function(val) {
        if (val.id) {
          this.addId = val.id
          this.fontFile.url = val.resMap.downloadUrl.pic
          const item = {
            name: '',
            url: val.resMap.resUrl.pic
          }
          this.previewFile.pop()
          this.previewFile.push(item)
          this.isNew = val.isNew
        }
      }
    }
    // formData: function(val) {
    //   if (val.id) {
    //     console.log('我更新了吗?')
    //     this.addId = val.id
    //     this.fontFile.url = val.resMap.downloadUrl.pic
    //     const item = {
    //       name: '',
    //       url: val.resMap.resUrl.pic
    //     }
    //     this.previewFile.pop()
    //     this.previewFile.push(item)
    //     this.isNew = val.isNew
    //   }
    // }
  },
  mounted() {

  },
  methods: {
    /** 返回 */
    handleBack(val) {
      this.$emit('handleBack', val)
    },
    /** 保存 */
    async handleSave() {
      const uploadFileList = []
      const picDto = {}
      const _this = this
      if (this.fontFile.file) {
        uploadFileList.push(this.fontFile.file)
      } else {
        picDto.downloadUrl = { pic: this.fontFile.url }
      }
      /** 图 */
      const item = this.previewFile[0]
      if (item && item.file) {
        uploadFileList.push(item.file)
      } else {
        picDto.resUrl = { pic: item.url }
      }
      /** 文件上传 */
      if (uploadFileList.length > 0) {
        oss.uploadFiles(uploadFileList, (results, error) => {
          if (results) {
            results.forEach(res => {
              picDto[res.key] = { pic: res.name.replace(FILE_URL, '') }
            })
            _this.addOrSave(picDto)
          }
        })
      } else {
        _this.addOrSave(picDto)
      }
    },
    async addOrSave(picDto) {
      // 提交保存
      const params = {
        id: this.addId,
        mId: this.id,
        isNew: this.isNew,
        localeCode: this.localeCode,
        picDto
      }
      const response = await saveRecord(params)
      if (response.head.ret === 0) {
        this.$message.success('保存成功')
        this.handleBack('1')
      } else {
        this.$message.error(response.head.msg)
      }
    },
    /** 上传字体文件前 */
    onChangeFont(file) {
      this.fontFile.name = file.name
      this.fontFile.file = file.raw
      this.fontFile.url = URL.createObjectURL(file.raw)
      this.fontFile.file.key = 'downloadUrl'
    },
    /** 预览图片 */
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url
      this.dialogVisible = true
    },
    /** 上传前钩子 */
    handelBeforeUpload(file) {
      let pic = {}
      if (file && /image\//i.test(file.type)) {
        getBase64(file, img => {
          pic = { url: img, file, uid: uuid() }
          pic.file.key = 'resUrl'
          this.previewFile.pop()
          this.previewFile.push(pic)
        })
      }
      return false
    },
    /** 移除前钩子 */
    handleBeforeRemove(file, fileList) {
      if (fileList.length === 0) {
        this.$message.warning('请至少保留一张图片')
        return false
      }
    },
    /** 自定义上传 */
    upLoadNothing() {
    }
  }
}
</script>
