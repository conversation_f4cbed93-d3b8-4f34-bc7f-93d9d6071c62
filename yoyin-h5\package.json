{"name": "start-printer", "version": "1.0.0", "description": "vue qingmall 版本", "author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "private": true, "scripts": {"dev": "node build/dev-server.js", "start": "node build/dev-server.js", "build": "cross-env NODE_ENV=production node build/build.js", "unit": "cross-env BABEL_ENV=test karma start test/unit/karma.conf.js --single-run", "e2e": "node test/e2e/runner.js", "test": "npm run unit && npm run e2e", "lint": "eslint --ext .js,.vue src test/unit/specs test/e2e/specs"}, "dependencies": {"ali-oss": "^6.0.2", "axios": "^0.16.1", "cnpm": "^5.0.0", "cp_fan_components": "^1.0.16", "dayjs": "^1.7.7", "echarts": "^3.6.2", "ee-first": "^1.1.1", "encodeurl": "^1.0.2", "escape-html": "^1.0.3", "escape-string-regexp": "^1.0.5", "has-ansi": "^3.0.0", "html2canvas": "^1.0.0-alpha.12", "js-cookie": "^2.2.0", "lodash": "^4.17.11", "methods": "^1.1.2", "moment": "^2.18.1", "node-sass": "^4.5.3", "on-finished": "^2.3.0", "parseurl": "^1.3.2", "postcss-loader": "^2.0.5", "statuses": "^1.5.0", "unpipe": "^1.0.0", "vue": "^2.3.3", "vue-i18n": "^6.1.3", "vue-infinite-scroll": "2.0.2", "vue-loading-overlay": "^3.1.0", "vue-router": "^2.3.1", "vue2-toast": "^2.0.2", "vuex": "^2.3.1", "element-ui": "^2.12.0"}, "devDependencies": {"autoprefixer": "^6.7.2", "babel-core": "^6.26.0", "babel-eslint": "^7.1.1", "babel-loader": "^6.4.1", "babel-plugin-istanbul": "^4.1.1", "babel-plugin-lodash": "^3.2.11", "babel-plugin-transform-runtime": "^6.22.0", "babel-preset-env": "^1.6.0", "babel-preset-stage-2": "^6.22.0", "babel-register": "^6.22.0", "chai": "^3.5.0", "chalk": "^1.1.3", "chromedriver": "^2.36.0", "compression-webpack-plugin": "^1.0.0", "connect-history-api-fallback": "^1.3.0", "copy-webpack-plugin": "^4.0.1", "cross-env": "^4.0.0", "cross-spawn": "^5.0.1", "css-loader": "^0.28.0", "del": "^3.0.0", "eslint": "^3.19.0", "eslint-config-standard": "^6.2.1", "eslint-friendly-formatter": "^2.0.7", "eslint-loader": "^1.7.1", "eslint-plugin-html": "^2.0.0", "eslint-plugin-promise": "^3.4.0", "eslint-plugin-standard": "^2.0.1", "eventsource-polyfill": "^0.9.6", "express": "^4.14.1", "extract-text-webpack-plugin": "^2.0.0", "file-loader": "^0.11.1", "friendly-errors-webpack-plugin": "^1.1.3", "fundebug-javascript": "^0.3.3", "glob": "^7.1.1", "gulp": "^3.9.1", "gulp-sequence": "^0.4.6", "gulp-sftp": "^0.1.5", "gulp-zip": "^4.0.0", "html-webpack-plugin": "^2.28.0", "http-proxy-middleware": "^0.17.3", "inject-loader": "^3.0.0", "karma": "^1.4.1", "karma-coverage": "^1.1.1", "karma-mocha": "^1.3.0", "karma-phantomjs-launcher": "^1.0.2", "karma-phantomjs-shim": "^1.4.0", "karma-sinon-chai": "^1.3.1", "karma-sourcemap-loader": "^0.3.7", "karma-spec-reporter": "0.0.30", "karma-webpack": "^2.0.2", "lodash-webpack-plugin": "^0.11.4", "lolex": "^1.5.2", "mocha": "^3.2.0", "nightwatch": "^0.9.12", "node-sass": "^4.5.2", "opn": "^4.0.2", "optimize-css-assets-webpack-plugin": "^1.3.0", "ora": "^1.2.0", "phantomjs-prebuilt": "^2.1.14", "postcss": "^6.0.1", "rimraf": "^2.6.0", "sass-loader": "^6.0.5", "selenium-server": "^3.0.1", "semver": "^5.3.0", "shelljs": "^0.7.6", "sinon": "^2.1.0", "sinon-chai": "^2.8.0", "url-loader": "^0.5.8", "vue-awesome-swiper": "^2.4.0", "vue-lazyload": "^1.1.3", "vue-loader": "^11.3.4", "vue-style-loader": "^2.0.5", "vue-template-compiler": "^2.3.3", "webpack": "^2.3.3", "webpack-bundle-analyzer": "^2.2.1", "webpack-dev-middleware": "^1.10.0", "webpack-hot-middleware": "^2.18.0", "webpack-merge": "^4.1.0"}, "engines": {"node": ">= 4.0.0", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 2 versions", "Android >= 4.0", "iOS >= 6", "not ie <= 8"], "main": ".eslintrc.js", "directories": {"test": "test"}, "license": "ISC"}