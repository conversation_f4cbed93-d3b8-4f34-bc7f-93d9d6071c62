/*
 * @Description: In User Settings Edit
 * @Author: your name
 * @Date: 2019-10-16 11:30:36
 * @LastEditTime: 2019-10-18 13:40:15
 * @LastEditors: Please set LastEditors
 */
import request from '@/utils/request'

const urls = {
  0: '/platform/gam/community/v1/analysis/feedcount',
  1: '/platform/gam/community/v1/analysis/downloadcount',
  2: '/platform/gam/community/v1/analysis/printcount',
  3: '/platform/gam/community/v1/analysis/likecount'
}

export function getUserList(params) {
  return request({
    url: '/platform/gam/community/v1/analysis/getaccountcount',
    method: 'get',
    params
  })
}

/** 获取动态数据   */
export function getActivityData(params) {
  return request({
    url: '/platform/gam/community/v1/analysis/getApiCallCount',
    method: 'get',
    params
  })
}

export function getApiCallCountTop(params) {
  return request({
    url: '/platform/gam/community/v1/analysis/getApiCallCountTop',
    method: 'get',
    params
  })
}

export function getApiCallPage(params) {
  return request({
    url: '/platform/gam/community/v1/analysis/getApiCallCountByUserId',
    method: 'get',
    params
  })
}

