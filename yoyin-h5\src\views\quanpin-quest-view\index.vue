<template>
  <div class="paper-body-item" v-if="paperData">
    <div class="paper-header">
      <h2
        class="paper-title color-3 font-20"
        v-html="paperData.latexPaperName || paperData.paperName"
      ></h2>
      <div
        class="paper-desc color-9 font-12"
        v-html="paperData.latexPaperDesc || paperData.paperDesc"
      ></div>
      <div class="paper-info color-9 font-12">
        <span class="item">
          <em class="text">{{
            paperData.provinceName || provinceName || '全国'
          }}</em>
        </span>
        <span class="item" v-if="paperData.gradeName">
          {{ paperData.gradeName || '--' }}
        </span>
        <span class="item" v-if="paperData.paperType">
          <em v-if="paperData.paperType">{{ paperData.paperType }}</em>
          <em v-else>--</em>
        </span>
        <span class="item"
          >浏览：
          <em class="text" v-if="paperData.viewCount"
            >{{ paperData.viewCount }}次</em
          >
          <em class="text" v-else>-</em>
        </span>

        <span class="item"
          >题量：
          <em class="text" v-if="paperData.paperTotalCount">{{
            paperData.paperTotalCount
          }}</em>
          <em class="text" v-else>-</em>
        </span>
      </div>
    </div>
    <CpPaperBody :paperStructure="paperData.paperStructure">
      <template #question="{ ques }">
        <div class="question-item">
          <div class="question-type-difficulty clearfix">
            <span class="item"
              >题型：<em class="">{{
                ques.questionInfo.quesType | isEmptyObject('name')
              }}</em></span
            >
            <span class="item"
              >预估难度：<em class="">{{
                ques.questionInfo.difficulty | isEmptyObject('name')
              }}</em></span
            >
          </div>
          <div class="question-item-body">
            <QuestionItem
              :ques="ques.questionInfo"
              :showQuestionNum="true"
              :questionIndex="ques.questionNum"
              :showKnowledge="false"
            ></QuestionItem>
          </div>
        </div>
      </template>
      <template #segment="{ segment }">
        <PaperSegmentName :ques="segment"></PaperSegmentName>
      </template>
    </CpPaperBody>
  </div>
</template>

<script>
  import QuestionItem from './CpFan/QuestionItem/QuestionItem.vue'
  import CpPaperBody from './CpFan/CpPaperBody'
  import PaperSegmentName from './CpFan/example/PaperSegmentName.vue'
  export default {
    components: { PaperSegmentName, CpPaperBody },
    data() {
      return {
        paperRes: require('./assets/data/paper.json'),
        paperData: null,
        provinceName: ''
      }
    },
    mounted() {
      if (this.paperRes.code === 200) {
        this.executePaperList(this.paperRes.data)
      }
    },
    methods: {
      executePaperList(arrList) {
        let stack = [...arrList.paperStructure]
        let questionNum = 0
        while (stack.length) {
          let item = stack.shift()
          if (!item.questionId) {
            let hasQues = false
            if (item.children && item.children.length) {
              item.children.forEach((subItem) => {
                if (subItem.questionId) {
                  hasQues = true
                }
              })
              item.hasQues = hasQues
              stack.unshift(...item.children)
            } else {
              item.hasQues = hasQues
            }
          } else {
            questionNum = questionNum + 1
            item.questionNum = questionNum
            item.showQuestionNum = true
            item.source = item.questionInfo.source
            item.isAddedToBox = item.questionInfo.isAddedToBox
            item.isCollected = item.questionInfo.isCollected
            item.canCollect = item.questionInfo.canCollect
          }
        }
        arrList.paperIdEnc = this.paperIdEnc
        this.paperData = arrList
        //  过滤html标签
        document.title = this.HTMLDecode(arrList.paperName) || '全品题舟'
      },
      HTMLDecode(text) {
        var temp = document.createElement('div')
        temp.innerHTML = text
        var output = temp.innerText || temp.textContent
        temp = null
        return output
      },
      isEmptyObject(obj, key) {
        if (obj === null || obj === undefined) {
          return '-'
        }
        const arr = Object.keys(obj)
        if (arr.length > 0) {
          return obj[key]
        } else {
          return '-'
        }
      }
    }
  }
</script>

<style scoped lang="scss">
  .paper-body-item {
    margin: 20px auto;
    width: 914px;
    padding: 0px 20px 20px 20px;

    .question-item {
      margin-bottom: 20px;
      position: relative;
      border: 1px solid #e2e2e2;
      border-radius: 6px;
      cursor: pointer;

      &.drop-enter-active {
        transition: all 0.4s cubic-bezier(0.49, -0.29, 0.75, 0.41);
      }
      &:hover {
        box-shadow: 0px 0px 10px 0px rgba(42, 77, 138, 0.2);
      }

      .question-item-body {
        padding: 20px 20px 5px 20px;
        letter-spacing: 1px;
      }
    }
  }

  .question-type-difficulty {
    padding: 0 20px;
    line-height: 42px;
    border-bottom: 1px solid #eeeeee;
    & > .item {
      color: #999999;
      padding-right: 18px;
      & > em {
        color: #666666;
      }
    }
    & > .share-item {
      float: right;
      color: #666666;
    }
    .item-time {
      position: absolute;
      right: 50px;
      top: 0px;
      .item {
        padding-right: 20px;
      }
    }
  }

  .paper-header {
    padding: 38px 0 18px 0;
    text-align: center;
    .paper-title {
      margin-bottom: 15px;
    }
    .paper-desc {
      margin-bottom: 20px;
      line-height: 20px;
    }
    .paper-info {
      .item {
        margin-right: 36px;
      }
    }
  }
  .color-3 {
    color: #333333;
  }
  .font-12 {
    font-size: 12px;
  }

  .font-20 {
    font-size: 20px;
  }
  .color-9 {
    color: #999999;
  }
</style>
