<template>
  <div>

  </div>
</template>

<script>
export default {
  name: 'RouteIndex',
  data () {
    return {
      msg: ''
    }
  },
  mounted () {
    this.routeSelect()
  },
  methods: {
    routeSelect () {
      if (typeof window !== 'undefined' && typeof window.navigator !== 'undefined') {
        if (/Android|webOS|iPhone|iPod|BlackBerry/i.test(navigator.userAgent)) {
          // 处理移动端的业务逻辑
          console.log('我是移动端')
          this.$router.push('/mobile')
        } else {
          // 处理电脑端的业务逻辑
          console.log('我是pc端')
          this.$router.push('/pc')
        }
      }
    }
  }
}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>

</style>
