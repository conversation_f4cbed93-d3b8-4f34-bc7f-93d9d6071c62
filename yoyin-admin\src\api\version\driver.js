/*
 * @Author: your name
 * @Date: 2019-10-16 11:52:25
 * @LastEditTime: 2019-10-22 15:10:15
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /d:\src\starPrinter\xstar-ui\star-printer-admin\src\api\version\list.js
 */
import request from '@/utils/request'

export function getOSSOptionFromServer() {
  return request({
    url: '/api/log/event/v1/storage/getosstoken',
    method: 'get'
  })
}

const LOAD_DATA_URL = '/platform/gam/community/v1/printdriverupinfo/findpage'
const LOAD_PRINTER_TYPE_URL = '/platform/gam/community/v1/printdriverupinfo/findtypelist'
// 删除记录
const DELETE_RECORD_URL = '/platform/gam/community/v1/printdriverupinfo/del'
// 保存记录
const SAVE_RECORD_URL = '/platform/gam/community/v1/printdriverupinfo/saveorupdate'

/**
 * 查询用户列表
 * @param params 参数
 */
export const fetchListByPage = async params => {
  return request({
    url: LOAD_DATA_URL,
    method: 'get',
    params
  })
}

/**
 * 删除记录
 * @param {String} id 记录id
 */
export const deleteRecord = async params => {
  return request({
    url: DELETE_RECORD_URL,
    method: 'get',
    params
  })
}

/**
 * 保存记录
 * @param ids 记录id
 */
export const saveRecord = async params => {
  return request({
    url: SAVE_RECORD_URL,
    method: 'post',
    data: params
  })
}

export const findPrintTypeList = () => {
  return request({
    url: LOAD_PRINTER_TYPE_URL,
    method: 'get'
  })
}

export const getAllByDriver = async params => {
  return request({
    url: '/platform/gam/community/v1/printdriverupinfo/findByPrinterType',
    method: 'get',
    params
  })
}

export const updatePrintDriverSubmit = async params => {
  return request({
    url: '/platform/gam/community/v1/printdriverupinfo/update-submit',
    method: 'post',
    data: params
  })
}
export const updatePrintDriverReject = async params => {
  return request({
    url: '/platform/gam/community/v1/printdriverupinfo/update-reject',
    method: 'post',
    data: params
  })
}

export const updatePrintDriverPass = async params => {
  return request({
    url: '/platform/gam/community/v1/printdriverupinfo/update-pass',
    method: 'post',
    data: params
  })
}

export const getWhiteUserList = async params => {
  return request({
    url: '/platform/gam/community/v1/printdriverupinfo/findWhileList',
    method: 'get',
    params
  })
}

export const addWhiteUser = async params => {
  return request({
    url: '/platform/gam/community/v1/printdriverupinfo/addWhileList',
    method: 'get',
    params
  })
}

export const removeWhiteUser = async params => {
  return request({
    url: '/platform/gam/community/v1/printdriverupinfo/removeWhileList',
    method: 'get',
    params
  })
}