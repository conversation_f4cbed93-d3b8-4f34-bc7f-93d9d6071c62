<template>
  <div>
    <div class="content" v-show="comment" v-infinite-scroll="loadMoreData" infinite-scroll-disabled="isLoading" infinite-scroll-distance="10">
      <div class="comment-item top" v-if="comment" @click.stop="addCommentReply">
        <!-- <img class="comment-userpic-official" v-if="comment.isOfficial==1" src="./assets/img/community_ic_official.png" /> -->
        <img class="comment-userpic" :src="comment.commentUserPic" @click.stop="launchPersonalHome(comment.commentUserId)" />
        <img class="comment-userpic-official" v-if="comment.userTitleObj && comment.userTitleObj.borderUrl" :src="comment.userTitleObj.borderUrl" @click.stop="launchPersonalHome(comment.commentUserId)" />
        <div style="width: 100%;padding-left:4.25rem;padding-top:0.875rem;">
          <div style="display:flex;">
            <div class="comment-name">{{comment.commentUserName}}</div>
            <div style="flex:1"></div>
            <div class="comment-menu"><img src="./assets/img/community_ic_comment.png" /></div>
          </div>
          <div class="comment-time">{{comment.commentDate}}</div>
          <div class="comment-content top">{{comment.content}}</div>
          <div class="view-feed" v-if="isShowFeedDetail" @click.stop="jumpToFeedDetail(comment.feedId)"><span>{{$t('reply-list.view-original-dynamic')}}</span><img src="./assets/img/common_ic_narrows.png" />
          </div>
        </div>
      </div>

      <div class="comment-item reply" v-if="replyList" v-for="(reply, index) in replyList" @click.stop="addReplyReply(reply)" :key="index">
        <!-- <img class="comment-userpic-official" v-if="reply.isOfficial==1" src="./assets/img/community_ic_official.png" /> -->
        <img class="comment-userpic" :src="reply.replyUserPic" @click.stop="launchPersonalHome(reply.replyUserId)" />
        <img class="comment-userpic-official" v-if="reply.userTitleObj && reply.userTitleObj.borderUrl" :src="reply.userTitleObj.borderUrl"  @click.stop="launchPersonalHome(reply.replyUserId)"/>
        <!-- <div class="comment-userpic" @click.stop="launchPersonalHome(reply.replyUserId)">
          <div :style="{'background-image': 'url(' + reply.replyUserPic + ')'}" />
        </div> -->
        <div style="width: 100%;padding-left:4.25rem;padding-top:0.875rem;">
          <div style="display:flex;">
            <div class="comment-name">{{reply.replyUserName}}</div>
            <div style="flex:1"></div>
            <div class="comment-menu"><img src="./assets/img/community_ic_comment.png" /></div>
            <div class="comment-menu" @click.stop="deleteDialog(index)" v-if="isMySelf(reply.replyUserId)">
              <img src="./assets/img/community_ic_delete.png" />
            </div>
          </div>
          <div class="comment-time">{{reply.replyDate}}</div>
          <div class="comment-content">{{reply.replyObjName?`${$t('reply-list.reply')} `:''}}<span class="reply-name" v-if="reply.replyObjName" @click.stop="launchPersonalHome(reply.replyObjId)">{{reply.replyObjName}}：</span>{{reply.content}}</div>
        </div>
      </div>
    </div>
    <div class="bottom-box" @click="addCommentReply">
      <div class="view-comment">{{$t('feed-detail.i-want-to-say')}}</div>
      <div class="comment-send">{{$t('feed-detail.release')}}</div>
    </div>
  </div>
</template>
<script>
import { getUrlParameter } from '@/utils/common'
import throttle from 'lodash/throttle'
import { getCommentDetail, getReplyList, deleteReply } from './assets/js/api.js'
import { TYPE_JUMP_APP, THROTTLE_TIME } from '@/consts/index'
import Cookies from 'js-cookie'
let vm
const throttleTime = THROTTLE_TIME()
export default {
  data () {
    return {
      moduleKey: 'reply-list',
      commentid: '',
      comment: undefined,
      replyList: undefined,
      clickIndex: -1,
      pageNo: 2,
      pageSize: 20,
      lastReplyId: '',
      isLoading: false,
      hasNoData: false,
      isShowFeedDetail: true
    }
  },
  watch: {
    comment (newVal, oldVal) {
      this.updateTitle(newVal.replyNum)
    }
  },
  computed: {
    isiosSystem() {
      if (window.webkit !== undefined && window.webkit.messageHandlers !== undefined) {
        return true
      } else {
        return false
      }
    }
  },
  async mounted () {
    this.$nextTick(() => {
      vm = this
      window.addCommentReplyCallBack = item => {
        this.addCommentReplyCallBack(item)
      }

      window.addReplyReplyCallBack = item => {
        this.addReplyReplyCallBack(item)
      }

      window.deleteReply = () => {
        window.setTimeout(() => {
          this.deleteReply()
        }, 1)
      }
    })
    const query = getUrlParameter()
    this.commentid = query.commentid || query.commid
    if (query.from) {
      this.isShowFeedDetail = false
    }
    this.loadData()
  },
  methods: {
    async loadData () {
      this.isLoading = true
      const { data, head } = await getCommentDetail(this.commentid)
      if (head.ret === 0) {
        this.comment = data
        this.replyList = data.commentReplyDto

        const size = this.replyList ? this.replyList.length : 0
        if (size > 0) {
          this.lastReplyId = this.replyList[size - 1].commentId
        }
        this.isLoading = false
      } else if (head.ret === 17) {
        this.$toast(this.$t('reply-list.data-empty'))
        this.isLoading = true
      }
    },
    async loadMoreData () {
      if (this.hasNoData || this.isLoading) return
      this.isLoading = true
      const { data, head } = await getReplyList(
        this.commentid,
        this.pageNo,
        this.pageSize,
        this.lastReplyId
      )
      this.isLoading = false
      if (head.ret === 0) {
        const size = data ? data.length : 0
        if (size > 0) {
          this.replyList.push(...data)
          this.pageNo++
          this.lastReplyId = this.replyList[size - 1]
        } else {
          this.hasNoData = true
        }
      }
    },

    isMySelf (userId) {
      if (window.StarPrinterJS) {
        const currentUserId = window.StarPrinterJS.getCurrentUserId()
        return currentUserId && currentUserId === userId
      } if (this.isiosSystem) {
        // 直接从cookies获取当前userId
        const currentUserId = Cookies.get('userId')
        return currentUserId && currentUserId === userId
      }
      return false
    },

    /**
     * 跳转至个人中心
     */
    launchPersonalHome: throttle((userId) => {
      if (window.StarPrinterJS) {
        window.StarPrinterJS.launchPersonalHome(userId)
      } else if (window.webkit !== undefined && window.webkit.messageHandlers !== undefined) {
        window.webkit.messageHandlers.launchPersonalHome.postMessage(userId)
      }
    }, throttleTime),

    /**
     * 添加评论的回复
     */
    addCommentReply: throttle(() => {
      if (window.StarPrinterJS && vm.comment) {
        vm.comment.callBackName = 'addCommentReplyCallBack'
        window.StarPrinterJS.launchCommentAdd(1, JSON.stringify(vm.comment))
      } else if (window.webkit !== undefined && window.webkit.messageHandlers !== undefined && vm.comment) {
        vm.comment.callBackName = 'addCommentReplyCallBack'
        const parms = {
          'type': 1,
          'detailData': vm.comment
        }
        window.webkit.messageHandlers.launchCommentAdd.postMessage(JSON.stringify(parms))
        // window.webkit.messageHandlers.launchCommentAdd.postMessage([1, JSON.stringify(vm.comment)])
      }
    }, throttleTime),

    /**
     * 评论的回复添加成功回调
     */
    addCommentReplyCallBack (comment) {
      try {
        if (typeof comment === 'string') {
          comment = JSON.parse(comment)
        }
        // 取第一条插入列表数据源
        this.replyList.splice(0, 0, comment.commentReplyDto[0])
        const replyNum = this.comment.replyNum + 1
        this.$set(this.comment, 'replyNum', replyNum)
        this.updateTitle(replyNum)
      } catch (error) {
        console.log(error)
      }
    },

    /**
     * 添加回复的回复
     */
    addReplyReply: throttle((reply) => {
      if (window.StarPrinterJS) {
        reply.callBackName = 'addReplyReplyCallBack'
        window.StarPrinterJS.launchCommentAdd(2, JSON.stringify(reply))
      } else if (window.webkit !== undefined && window.webkit.messageHandlers !== undefined) {
        reply.callBackName = 'addReplyReplyCallBack'
        const parms = {
          'type': 2,
          'detailData': reply
        }
        window.webkit.messageHandlers.launchCommentAdd.postMessage(JSON.stringify(parms))
        // window.webkit.messageHandlers.launchCommentAdd.postMessage([2, JSON.stringify(reply)])
      }
    }, throttleTime),

    addReplyReplyCallBack (reply) {
      try {
        if (typeof reply === 'string') {
          reply = JSON.parse(reply)
        }
        console.log('addReplyReplyCallBack', reply)
        this.replyList.splice(0, 0, reply)
        const replyNum = this.comment.replyNum + 1
        this.$set(this.comment, 'replyNum', replyNum)
        this.updateTitle(replyNum)
      } catch (error) {
        console.log(error)
      }
    },

    deleteDialog: throttle((index) => {
      if (window.StarPrinterJS) {
        vm.clickIndex = index
        window.StarPrinterJS.showCommonDialog(
          vm.$t('reply-list.delete-tip'),
          'deleteReply'
        )
      } else if (window.webkit !== undefined && window.webkit.messageHandlers !== undefined) {
        vm.clickIndex = index
        const parms = {
          'detailInfo': vm.$t('reply-list.delete-tip'),
          'callback': 'deleteReply'
        }
        window.webkit.messageHandlers.showCommonDialog.postMessage(JSON.stringify(parms))
      }
    }, throttleTime),

    async deleteReply () {
      if (this.clickIndex > -1) {
        const item = this.replyList[this.clickIndex]
        const { head, data } = await deleteReply(item.replyId)
        if (head.ret === 0) {
          this.replyList.splice(this.clickIndex, 1)
          const replyNum = this.comment.replyNum - 1
          this.$set(this.comment, 'replyNum', replyNum)
          this.updateTitle(replyNum)

          this.clickIndex = -1
          this.$toast(this.$t('reply-list.delete-success'))
        } else {
          this.$toast(head.msg)
        }
      }
    },

    /**
     * 更新页面标题
     */
    updateTitle (replyNum) {
      if (replyNum === undefined || replyNum < 0) {
        replyNum = 0
      }
      document.title = replyNum === 0 ? this.$t('reply-list.comment-replies') : `${replyNum}${this.$t('reply-list.replies')}`
      if (window.StarPrinterJS) {
        window.StarPrinterJS.setTitle(document.title)
      } else if (this.isiosSystem) {
        window.webkit.messageHandlers.setTitle.postMessage(document.title)
      }
    },

    /**
     * 跳转至评论详情
     */
    jumpToFeedDetail: throttle((feedid) => {
      if (window.StarPrinterJS) {
        const url = `${window.location.href
          .replace(window.location.search, '')
          .replace('reply-list', 'feed-detail')}?feedid=${feedid}`
        window.StarPrinterJS.jumpToApp(TYPE_JUMP_APP.H5, url)
      } else if (window.webkit !== undefined && window.webkit.messageHandlers !== undefined) {
        const url = `${window.location.href
          .replace(window.location.search, '')
          .replace('reply-list', 'feed-detail')}?feedid=${feedid}`
        const parms = {
          'type': TYPE_JUMP_APP.H5,
          'url': url
        }
        window.webkit.messageHandlers.jumpToApp.postMessage(JSON.stringify(parms))
        // window.webkit.messageHandlers.jumpToApp.postMessage([TYPE_JUMP_APP.H5, url])
      }
    }, throttleTime)
  }
}
</script>
<style lang="scss">
html {
  font-size: 16px;
  height: 100%;
  background: #efeff4;
}

.content {
  padding-bottom: 9.375rem;
  //   padding-bottom: 6.25rem;
  width: 100%;
  height: 100%;
  overflow-y: auto;

  .comment-item {
    display: flex;
    border-bottom: 0.5px solid #e6e6e6;
    padding-top: 12px;
    padding-left: 1rem;
    padding-right: 1rem;
    padding-bottom: 1rem;
    background: white;

    &.top {
      border-top: 0.75rem solid #efeff4;
      border-bottom: 0.75rem solid #efeff4;
    }

    &.reply {
      background: rgba($color: white, $alpha: 0.7);
    }

    .comment-userpic {
        height: 2.45rem;
        width: 2.45rem;
        margin: 0.88rem 0.78rem 0.78rem 0.78rem;
        border-radius: 30px;
        position: absolute;
    }

    .comment-userpic-official {
      width: 3.2727272725rem;
      height: 3.2727272725rem;
      margin: 0.3rem;
      position: absolute;
    }

    .comment-menu {
      img {
        width: 1.25rem;
        height: 1.25rem;
      }
      padding-left: 1rem;
    }

    .comment-name {
      font-size: 1rem;
      line-height: 1rem;
      color: black;
    }

    .comment-time {
      font-size: 0.75rem;
      line-height: 0.5rem;
      color: #bcbbbf;
      margin-top: 0.5rem;
    }

    .comment-content {
      color: #8e8e93;
      margin-top: 0.8125rem;
      &.top {
        color: black;
      }
    }

    .view-feed {
      color: #8e8e93;
      font-size: 0.875rem;
      margin-top: 0.7rem;
      img {
        width: 10px;
        height: 10px;
      }
    }

    .reply-name {
      color: #5a7dff;
      margin: 0;
    }
  }
}

.bottom-box {
  width: 100%;
  height: 3.0625rem;
  background: white;
  position: fixed;
  bottom: 0;
  padding: 0 0.4375rem;
  display: flex;
  font-size: 0.875rem;
  .view-comment {
    flex: 1;
    height: 2.25rem;
    line-height: 2.25rem;
    padding-left: 0.75rem;
    background: #efeff4;
    color: #bcbbbf;
    align-self: center;
    border-radius: 0.25rem;
  }
  .comment-send {
    align-self: center;
    margin-left: 0.75rem;
    margin-right: 0.25rem;
    color: #5a7dff;
  }
}
</style>
