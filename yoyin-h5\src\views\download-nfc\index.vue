<template>
  <div class="content">
    <img src="./assets/img/logo.png" />
    <div class="title">打印机手机客户端</div>
    <div class="tip">
      <span class="tip-separator"></span>
      <span class="tip-text">即刻下载 更多玩法等着你</span>
      <span class="tip-separator"></span>
    </div>
      <a href="https://m.starpany.cn/common/star/apk/nfc/Starpany_nfc.apk">点击下载</a>
    <img class="bottom-img" src="./assets/img/main.png" />
  </div>
</template>

<script>
export default {
  data () {
    return {
      moduleKey: 'download'
    }
  },
  async mounted () {
  },
  methods: {},
  components: {}
}
</script>

<style lang="scss">
.content {
  padding: 1.2rem;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  .title{
    font-size:28px;
    font-family:Microsoft YaHei;
    font-weight:bold;
    line-height:28px;
    margin-top: 48px;
  }
  .tip{
    margin-top: 12px;
    span{
      display: inline-block;
    }
    display: flex;
    justify-content: center;
    align-items: center;
    &-separator{
      width:42px;
      height:2px;
      background:rgba(224,224,224,1);
      opacity:1;
    }
    &-text{
      font-size:18px;
      font-family:Microsoft YaHei;
      font-weight:400;
      line-height:18px;
      color:rgba(62,62,62,1);
      opacity:1;
      margin: auto 8px;
    }
  }
  a{
      width:75%;
      margin-top: 48px;
      height:52px;
      line-height: 52px;
      text-align: center;
      background:rgba(63,63,63,1);
      border:1px solid rgba(112,112,112,1);
      font-size: 18px;
      color: #ffffff;
      border-radius:10px;
      color: #ffffff;
  }
  .bottom-img{
    margin-top: 48px;
  }
}
</style>
