<template>
  <div class="container">
    <transition name="fade">
      <side-bar @changeNav="changeNav" v-show="navShowFlag"></side-bar>
    </transition>

      <div class="currNavImg" :style="currNavImgStyle">
<!--        <transition name="dis">-->
<!--          <img src="../../assets/home_select.gif" v-if="navArrtShowFlag">-->
<!--        </transition>-->
        <img :src="gifimg" v-if="navArrtShowFlag">
      </div>

    <div class="contents">
      <transition name="dis">
        <top-bar :titleName="topTitle" v-show="headerShowFlag"></top-bar>
      </transition>
      <transition name="dis">
        <div v-if="componentShowFlag">
          <component v-bind:is="currentTabComponent" keep-alive></component>
        </div>
      </transition>
    </div>
  </div>
</template>

<script>
import SideBar from './components/SideBar'
import TopBar from './components/TopBar'
import HomePage from './components/HomePage'
import AboutUs from './components/AboutUs'
import ServiceInfo from './components/ServiceInfo'
import DriveDownload from './components/DriveDownload'
import GIF from '../../assets/mobile/home_select.gif'

export default {
  components: {TopBar, SideBar, HomePage, AboutUs, ServiceInfo, DriveDownload},
  name: 'Index',
  data () {
    return {
      topTitle: '首页',
      indexNum: 4,
      currentTabComponent: 'HomePage',
      navShowFlag: false,
      navArrtShowFlag: false,
      headerShowFlag: false,
      componentShowFlag: false,
      show: true,
      gifimg: GIF
      // currNavImgStyle: {'bottom': '0.7rem'}
      // currNavImgStyle: {'bottom': '24.9rem'}
    }
  },
  computed: {
    currNavImgStyle () {
      let height = 8.2 * this.indexNum - 1.3 - 3.4 - (this.indexNum * 0.1)
      // height = height - 1.3 - 3.4
      return {'bottom': height + 'rem'}
    }
  },
  mounted () {
    this.navShowFlag = true
    const that = this
    setTimeout(function () {
      that.navArrtShowFlag = true
    }, 800)
    setTimeout(function () {
      that.headerShowFlag = true
    }, 1500)
    setTimeout(function () {
      that.componentShowFlag = true
    }, 1700)
  },
  methods: {
    changeNav (parms) {
      this.gifimg = GIF + '?+' + Math.random()
      document.documentElement.scrollTop = 0
      this.indexNum = parms.index
      this.topTitle = parms.titleName
      this.currentTabComponent = parms.componentName
    }
  }
}
</script>

<style scoped>
html, body {
  overflow-x: hidden;
  touch-action:none;
  touch-action:pan-y;
}
  .container {
    position: relative;
    height: 100%;
  }
  .currNavImg {
    position: fixed;
    left: 5.7rem;
    z-index: -99999;
  }

  .currNavImg img{
    width: 2.2rem;
    height: 6.8rem;
  }
  .contents {
    padding-left: 7.67rem;
    padding-top: 2.4rem;
  }
  .foot {
    padding-bottom: 4rem;
    color: #999999;
    font-size: 1.2rem;
    line-height: 2rem;
  }

  .fade-enter{
    opacity:0;
    transform: translateX(-10rem);
  }
  .fade-enter-active{
    transition:transform 1.5s;
  }
  .fade-enter-to {
    transform: translateX(0);
  }
  .fade-leave-active{
    transition:transform 1s;
  }
  .fade-leave-to{
    transform: translateX(0);
  }

  .dis-enter{
    opacity:0;
  }
  .dis-enter-active{
    transition:opacity 1.5s;
  }
  .dis-leave-active{
    transition:transform 1s;
  }
  .dis-leave-to{
    transform: translateX(0);
  }
</style>
