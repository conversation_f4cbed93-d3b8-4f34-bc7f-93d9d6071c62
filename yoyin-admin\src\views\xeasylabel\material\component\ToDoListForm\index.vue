<template>
  <div class="todolist">
    <el-form :inline="true" class="demo-form-inline">
      <el-form-item label="标签">
        <el-checkbox-group v-model="label">
          <el-checkbox-button v-for="item in borderList" :key="item.id" :label="item.id">{{ item.name }}</el-checkbox-button>
        </el-checkbox-group>
      </el-form-item>
      <br>
      <el-form-item label="预览图：">
        <SingleUpload key="listUrl" key-word="listUrl" :init-url="(isEdit && files.listUrl) ? files.listUrl.pic: ''" @updateFile="updateFile" />
      </el-form-item>
      <el-form-item label="资源图：">
        <SingleUpload key="resUrl" key-word="resUrl" :init-url="(isEdit && files.resUrl) ? files.resUrl.pic: '' " @updateFile="updateFile" />
      </el-form-item>
      <br>
      <el-form-item v-if="mName === 'tz_todolist' " label="左侧添加图：">
        <SingleUpload key-word="addLeftUrl" :init-url="(isEdit && files.addLeftUrl) ? files.addLeftUrl.pic: '' " @updateFile="updateFile" />

      </el-form-item>
      <el-form-item label="顶部图：">
        <SingleUpload key-word="topUrl" :init-url="(isEdit && files.topUrl) ? files.topUrl.pic: '' " @updateFile="updateFile" />

      </el-form-item>
      <el-form-item label="底部图：">
        <SingleUpload key-word="downUrl" :init-url="(isEdit && files.downUrl) ? files.downUrl.pic: '' " @updateFile="updateFile" />

      </el-form-item>
      <el-form-item label="左侧图：">
        <SingleUpload key-word="leftUrl" :init-url="(isEdit && files.leftUrl) ? files.leftUrl.pic: '' " @updateFile="updateFile" />

      </el-form-item>
      <el-form-item label="右侧图：">
        <SingleUpload key-word="rightUrl" :init-url="(isEdit && files.rightUrl) ? files.rightUrl.pic: '' " @updateFile="updateFile" />
      </el-form-item>
      <br>
      <el-form-item label="左上角↖：">
        <SingleUpload key-word="leftTopUrl" :init-url="(isEdit && files.leftTopUrl) ? files.leftTopUrl.pic: '' " @updateFile="updateFile" />

      </el-form-item>
      <el-form-item label="右上角↗：">
        <SingleUpload key-word="rightTopUrl" :init-url="(isEdit && files.rightTopUrl) ? files.rightTopUrl.pic: '' " @updateFile="updateFile" />

      </el-form-item>
      <el-form-item label="左下角↙：">
        <SingleUpload key-word="leftDownUrl" :init-url="(isEdit && files.leftDownUrl) ? files.leftDownUrl.pic: '' " @updateFile="updateFile" />

      </el-form-item>
      <el-form-item label="右下角↘：">
        <SingleUpload key-word="rightDownUrl" :init-url="(isEdit && files.rightDownUrl) ? files.rightDownUrl.pic: '' " @updateFile="updateFile" />
      </el-form-item>
      <br>
      <el-form-item label="纸张类型：">
        <el-radio-group v-model="paperLabel">
          <el-radio-button v-for="(item, index) in paperList " :key="index" :label="item.label">{{ item.name }}</el-radio-button>
        </el-radio-group>
      </el-form-item>
      <br>
      <el-form-item label="编辑方向：">
        <el-radio-group v-model="placeType">
          <el-radio-button v-for="(item, index) in placeTypeList " :key="index" :label="item.label">{{ item.name }}</el-radio-button>
        </el-radio-group>
      </el-form-item>
      <br>
      <el-form-item label="排序" prop="sortNum">
        <el-input
          v-model="sortNum"
          type="text"
        />
      </el-form-item>
      <el-form-item style="margin-left: 40%;">
        <el-button @click="handleBack">返回</el-button>
        <el-button v-permission="'btn-xeasylabel-material-edit'" type="primary" @click="handleSave">保存</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>
<script>
import { saveRecord } from '@/api/xeasylabel/material'
import SingleUpload from '../SingleUpload/index'
import AliyunOSS from '@/utils/aliyunOSS'
import { fetchDictTypeList } from '@/api/xeasylabel/dict'
const oss = new AliyunOSS()
const TIP_MESSAGES = {
  listUrl: '请选择预览图',
  resUrl: '请选择资源图'// ,
  // topUrl: '请选择顶部图',
  // downUrl: '请选择底部图',
  // leftUrl: '请选择左边图',
  // rightUrl: '请选择右边图',
  // addLeftUrl: '请选择左侧添加图'
}

export default {
  components: {
    SingleUpload
  },
  props: {
    mId: {
      type: String,
      default: ''
    },
    mName: {
      type: String,
      default: ''
    },

    formData: {
      type: Object,
      default: () => { return {} }
    }
  },
  data() {
    return {
      uploadFiles: { listUrl: {}, resUrl: {}, topUrl: {}, downUrl: {}, leftUrl: {}, rightUrl: {}, leftTopUrl: {}, rightTopUrl: {}, leftDownUrl: {}, rightDownUrl: {}},
      paperLabel: '0',
      placeType: '0',
      sortNum: 0,
      label: [],
      borderList: [{ id: 'hot', name: '热门' }, { id: 'simple', name: '简约' }, { id: 'lovely', name: '可爱' }, { id: 'figure', name: '花纹' }, { id: 'chinese', name: '中国风' }, { id: 'classical', name: '古典' }]
    }
  },
  computed: {
    isEdit() {
      if (this.formData && this.formData.id) {
        return true
      } else {
        return false
      }
    },
    /** 编辑的数据 */
    files() {
      if (this.isEdit) {
        return this.formData.resMap
      } else {
        return {}
      }
    },
    /** 编辑ID */
    addId() {
      if (this.isEdit) {
        return this.formData.id
      } else {
        return ''
      }
    },
    /** 纸张类型枚举 */
    paperList() {
      return [
        {
          label: 9999,
          name: '全部'
        }, {
          label: -1,
          name: '标签连续纸'
        },
        // {
        //   label: 330,
        //   name: '15×22mm'
        // },
        // {
        //   label: 450,
        //   name: '15×30mm'
        // },
        // {
        //   label: 600,
        //   name: '15×40mm'
        // },
        // {
        //   label: 750,
        //   name: '15×50mm'
        // }
        {
          label: 308,
          name: '22×14mm'
        },
        {
          label: 420,
          name: '30×14mm'
        },
        {
          label: 560,
          name: '40×14mm'
        },
        {
          label: 700,
          name: '50×14mm'
        },
        {
          label: 1500,
          name: '50×30mm'
        },
        {
          label: 2000,
          name: '50×40mm'
        },
        {
          label: 3750,
          name: '50×75mm'
        }
      ]
    },
    /** 编辑方向类型枚举 */
    placeTypeList() {
      return [
        {
          label: -1,
          name: '全部'
        },
        {
          label: 0,
          name: '竖向'
        },
        {
          label: 1,
          name: '横向'
        }
      ]
    }

  },
  watch: {
    formData: {
      handler(newVal, oldVal) {
        this.paperLabel = newVal.length
        this.placeType = newVal.placeType
        if (newVal.label) {
          this.label = newVal.label.split(',')
        }
        this.sortNum = newVal.sortNum
      },
      immediate: true
    }
  },
  mounted() {
    this.getBorderList()
  },
  methods: {
    async getBorderList() {
      const params = {
        type: 'material_sub_type_bk'
      }
      const res = await fetchDictTypeList(params)
      if (res.head.ret === 0) {
        const result = [{ id: '', name: '全部' }]
        res.data.forEach(item => {
          const temp = {
            id: item.value,
            name: item.label
          }
          result.push(temp)
        })
        this.borderList = result
      }
    },
    handleBack(val) {
      this.$emit('handleBack', val)
    },
    /** 子组件上传文件 */
    updateFile(key, file) {
      this.uploadFiles[key] = {
        file,
        key
      }
    },
    /** 校验文件是否上传 */
    validateFiles() {
      let flag = true
      // 合并
      if (this.isEdit) {
        Object.keys(this.files).forEach(key => {
          const item = this.uploadFiles[key]
          if (!item || !item.hasOwnProperty('file')) {
            this.uploadFiles[key] = this.files[key]
          }
        })
      }
      // 校验
      Object.keys(this.uploadFiles).forEach(key => {
        if (!flag) {
          return
        }
        const file = this.uploadFiles[key]
        if (TIP_MESSAGES[key] && file && !file.file && (!file.pic || file.pic.indexOf('http') < 0)) {
          this.$message.error(TIP_MESSAGES[key])
          flag = false
        }
      })
      return flag
    },
    /** 保存 */
    async handleSave() {
      // 检验是否选纸张
      if (this.mName === 'tz_frame' && this.paperLabel === '0') {
        this.$message.error('请选择纸张')
        return
      }
      if (this.validateFiles()) {
        const upFiles = []
        const picDto = {}
        Object.keys(this.uploadFiles).forEach(key => {
          const item = this.uploadFiles[key]
          if (item.file) {
            item.file.key = key
            upFiles.push(item.file)
          } else {
            picDto[key] = { pic: item.pic }
          }
        })
        // 上传
        oss.uploadFiles(upFiles, (results, error) => {
          if (error) {
            this.$message.error('文件上传失败，请检查')
            return
          }
          if (results) {
            results.forEach(res => {
              picDto[res.key] = { pic: res.name }
            })
          }

          let labelTmp = ''
          if (this.label && this.label.length > 0) {
            this.label.forEach(item => {
              if (item) {
                labelTmp += item + ','
              }
            })
          }
          // 保存
          const params = {
            id: this.addId,
            mId: this.mId,
            length: this.paperLabel,
            placeType: this.placeType,
            label: labelTmp,
            sortNum: this.sortNum,
            picDto
          }
          saveRecord(params).then(res => {
            if (res.head.ret === 0) {
              this.$message.success('保存成功')
              this.handleBack('1')
            } else {
              this.$message.error(res.head.msg)
            }
          })
        })
      }
    }
  }
}
</script>
