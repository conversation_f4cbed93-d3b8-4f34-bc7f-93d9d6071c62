<template>
  <el-form ref="form" :model="form" label-width="100px" class="edit-form">
    <el-form-item label="圈子：" prop="labelType">
      <el-radio-group v-model="labelType">
        <el-radio-button label="study">学习圈</el-radio-button>
        <el-radio-button label="label">兴趣圈</el-radio-button>
      </el-radio-group>
    </el-form-item>
    <el-form-item label="发帖人：" prop="userId">
      <el-select
        v-model="form.userId"
        filterable
        remote
        reserve-keyword
        placeholder="请输入关键词"
        :remote-method="queryUserList"
        :loading="loading"
      >
        <el-option
          v-for="item in userQueryList"
          :key="item.id"
          :label="item.nickName"
          :value="item.userId"
        />
      </el-select>
      <el-button type="primary" @click="randonSelectUser">随机</el-button>
    </el-form-item>
    <el-form-item v-if="labelType==='study'" label="标题：" prop="title">
      <el-input v-model="form.title" placeholder="标题" maxlength="20" show-word-limit />
    </el-form-item>
    <el-form-item label="内容：" prop="content">
      <el-input v-model="form.content" type="textarea" :rows="4" placeholder="说点什么吧" :maxlength="labelType=='study' ? 1000 : 200" show-word-limit />
      <el-popover
        placement="right"
        width="800"
        trigger="click"
      >
        <div style="display:flex; flex-flow:row wrap;font-size:24px;">
          <div v-for="item in emojiList" :key="item" style="cursor: pointer" @click="selectEmoji(item)">{{ item }}</div>
        </div>
        <el-button slot="reference">插入表情</el-button>
      </el-popover>
    </el-form-item>
    <el-form-item v-if="labelType!=='study'" label="显示标签：" prop="isLabel">
      <el-radio-group v-model="form.isLabel">
        <el-radio-button label="1">是</el-radio-button>
        <el-radio-button label="0">否</el-radio-button>
      </el-radio-group>
    </el-form-item>

    <el-form-item v-if="labelType!=='study'" label="标签：" prop="labelIds">
      <el-checkbox-group v-model="form.labelIds">
        <el-checkbox v-for="(item, index) in labelList" :key="index" :label="item.id" @change="onChangeLabel(item)">{{ item.titleZh }}</el-checkbox>
      </el-checkbox-group>
    </el-form-item>

    <el-form-item v-if="labelType==='study'" label="学年：" prop="gradeLevel">
      <el-radio-group v-model="form.gradeLevel">
        <el-radio-button v-for="item in gradeLevelList" :key="item.value" :label="item.value">{{ item.label }}</el-radio-button>
      </el-radio-group>
    </el-form-item>

    <el-form-item v-if="labelType==='study'" label="年级：" prop="gradeNum">
      <el-radio-group v-model="form.gradeNum">
        <el-radio-button v-for="item in gradeNumList" :key="item.value" :label="item.value">{{ item.label }}</el-radio-button>
      </el-radio-group>
    </el-form-item>

    <el-form-item v-if="labelType==='study'" label="学科：" prop="subject">
      <el-radio-group v-model="form.subject">
        <el-radio-button v-for="item in subjectList" :key="item.id" :label="item.id">{{ item.name }}</el-radio-button>
      </el-radio-group>
    </el-form-item>

    <el-form-item label="图片：" prop="pic">
      <!-- 自定义上传 监听http-request获取文件  on-remove触发删除 最后自己点击提交 -->
      <el-upload
        action=""
        list-type="picture-card"
        :http-request="customUpload"
        :on-remove="handelRemove"
        multiple
        accept="image/png, image/jpeg"
      >
        <i class="el-icon-plus" />
      </el-upload>
      <img v-show="false" :src="testUrl" class="avatar">
    </el-form-item>

    <el-form-item>
      <el-button @click="handleClose">返回</el-button>
      <el-button type="primary" @click="submit">立即发布</el-button>
      <el-button type="primary" @click="submit2">定时发布</el-button>
      定期发布时间：
      <el-date-picker
        v-model="form.delayedTime"
        :value-format="timeFormat"
        type="datetime"
        placeholder="选择日期时间"
      />
    </el-form-item>
  </el-form>
</template>

<script>
import { saveRecord, findLabelPage, saveRecordDelayed } from '@/api/community/activity'
import { getRandomUser, fetchListByPage } from '@/api/user/list'
import AliyunOSS from '@/utils/aliyunOSS'
const oss = new AliyunOSS()
export default {
  name: 'EditDialog',
  props: {
    labelList: {
      type: Array,
      default: function() {
        return []
      }
    },
    subjectList: {
      type: Array,
      default: function() {
        return []
      }
    }
  },
  data() {
    return {
      labelType: 'study',
      userQueryList: [],
      /** 表单 */
      form: {
        userId: '',
        codeId: '',
        content: '',
        isLabel: '0',
        labelIds: [],
        pic: undefined
      },
      emojiList: ['😀', '😃', '😄', '😁', '😆', '😅', '😂', '🤣', '😇', '😉', '😊', '🙂', '🙃', '☺', '😋', '😌', '😍', '🥰', '😘', '😗', '😙', '😚', '🤪', '😜', '😝', '😛', '🤑', '😎', '🤓', '🧐', '🤠', '🥳', '🤗', '🤡', '😏', '😶', '😐', '😑', '😒', '🙄', '🤨', '🤔', '🤫', '🤭', '🤥', '😳', '😞', '😟', '😠', '😡', '🤬', '😔', '😕', '🙁', '☹', '😬', '🥺', '😣', '😖', '😫', '😩', '🥱', '😤', '😮', '😱', '😨', '😰', '😯', '😦', '😧', '😢', '😥', '😪', '🤤', '😓', '😭', '🤩', '😵', '🥴', '😲', '🤯', '🤐', '😷', '🤕', '🤒', '🤮', '🤢', '🤧', '🥵', '🥶', '😴', '💤', '😈', '👿', '👹', '👺', '💩', '👻', '💀', '☠', '👽', '🤖', '🎃', '😺', '😸', '😹', '😻', '😼', '😽', '🙀', '😿', '😾', '👐', '🤲', '🙌', '👏', '🙏', '🤝', '👍', '👎', '👊', '✊', '🤛', '🤜', '🤞', '✌', '🤘', '🤟', '👌', '🤏', '👈', '👉', '👆', '👇', '☝', '✋', '🤚', '🖐', '🖖', '👋', '🤙', '💪', '🦾', '🖕', '✍', '🤳', '💅', '🦵', '🦿', '🦶', '👄', '🦷', '👅', '👂', '🦻', '👃', '👁', '👀', '🧠', '🦴', '👤', '👥', '🗣', '👶', '👧', '🧒', '👦', '👩', '🧑', '👨', '👩‍🦱', '🧑‍🦱', '👨‍🦱', '👩‍🦰', '🧑‍🦰', '👨‍🦰', '👱‍♀️', '👱', '👱‍♂️', '👩‍🦳', '🧑‍🦳', '👨‍🦳', '👩‍🦲', '🧑‍🦲', '👨‍🦲', '🧔', '👵', '🧓', '👴', '👲', '👳‍♀️', '👳', '👳‍♂️', '🧕', '👼', '👸', '🤴', '👰', '🤵‍♀️', '🤵', '🤵‍♂️', '🙇‍♀️', '🙇', '🙇‍♂️', '💁‍♀️', '💁', '💁‍♂️', '🙅‍♀️', '🙅', '🙅‍♂️', '🙆‍♀️', '🙆', '🙆‍♂️', '🤷‍♀️', '🤷', '🤷‍♂️', '🙋‍♀️', '🙋', '🙋‍♂️', '🤦‍♀️', '🤦', '🤦‍♂️', '🧏‍♀️', '🧏', '🧏‍♂️', '🙎‍♀️', '🙎', '🙎‍♂️', '🙍‍♀️', '🙍', '🙍‍♂️', '💇‍♀️', '💇', '💇‍♂️', '💆‍♀️', '💆', '💆‍♂️', '🤰', '🤱', '🧎‍♀️', '🧎', '🧎‍♂️', '🧍‍♀️', '🧍', '🧍‍♂️', '🚶‍♀️', '🚶', '🚶‍♂️', '👩‍🦯', '🧑‍🦯', '👨‍🦯', '🏃‍♀️', '🏃', '🏃‍♂️', '👩‍🦼', '🧑‍🦼', '👨‍🦼', '👩‍🦽', '🧑‍🦽', '👨‍🦽', '💃', '🕺', '👫', '👭', '👬', '🧑‍🤝‍🧑', '👩‍❤️‍👨', '👩‍❤️‍👩', '💑', '👨‍❤️‍👨', '👩‍❤️‍💋‍👨', '👩‍❤️‍💋‍👩', '💏', '👨‍❤️‍💋‍👨', '❤', '🧡', '💛', '💚', '💙', '💜', '🤎', '🖤', '🤍', '💔', '❣', '💕', '💞', '💓', '💗', '💖', '💘', '💝', '💟'],
      /** 校验规则 */
      rules: {
        codeId: [
          { required: true, message: '请输入用户codeId', trigger: 'blur' },
          { min: 2, max: 20, message: '长度在 2 到 30 个字符', trigger: 'blur' }
        ],
        content: [
          { required: true, message: '请输入内容', trigger: 'blur' },
          { min: 1, max: 200, message: '长度在 1 到 200 个字符', trigger: 'blur' }
        ],
        isLabel: [
          { required: true, message: '请选择类型', trigger: 'change' }
        ],
        pic: [
          { required: true, message: '请输入选择图片', trigger: 'blur' }
        ]
      },
      /** 待上传的文件 */
      upFile: undefined,
      testUrl: '',
      /** 时间格式 */
      timeFormat: 'yyyy-MM-dd HH:mm:ss',
      /** 上传文件的列表 */
      fileList: [],
      loading: false,
      gradeLevelList: [{
        label: '小学',
        value: 'xx'
      },
      {
        label: '初中',
        value: 'cz'
      },
      {
        label: '高中',
        value: 'gz'
      }],
      xxList: [{
        label: '一年级',
        value: '1'
      },
      {
        label: '二年级',
        value: '2'
      },
      {
        label: '三年级',
        value: '3'
      }, {
        label: '四年级',
        value: '4'
      },
      {
        label: '五年级',
        value: '5'
      },
      {
        label: '六年级',
        value: '6'
      }],
      czList: [{
        label: '初一',
        value: '7'
      },
      {
        label: '初二',
        value: '8'
      },
      {
        label: '初三',
        value: '9'
      }],
      gzList: [{
        label: '高一',
        value: '10'
      },
      {
        label: '高二',
        value: '11'
      },
      {
        label: '高三',
        value: '12'
      }]
    }
  },
  computed: {
    gradeNumList() {
      let arr = []
      if (this.form.gradeLevel === 'xx') {
        arr = this.xxList
      } else if (this.form.gradeLevel === 'cz') {
        arr = this.czList
      } else if (this.form.gradeLevel === 'gz') {
        arr = this.gzList
      }
      return arr
    }
  },
  watch: {
  },
  mounted() {
  },
  methods: {
    selectEmoji(emoji) {
      console.log(emoji)
      this.form.content += emoji
    },
    randonSelectUser() {
      getRandomUser().then(res => {
        this.userQueryList = []
        this.userQueryList.push(res.data.userInfoDto)
        this.form.userId = res.data.userInfoDto.userId
      })
    },
    queryUserList(e) {
      const params = {
        pageNo: 1,
        pageSize: 12,
        sort: 'desc',
        sortType: 'time',
        codeId: e
      }
      fetchListByPage(params).then(res => {
        if (res.head.ret === 0 && res.data.result.length > 0) {
          this.userQueryList = res.data.result
        } else {
          delete params['codeId']
          params['nickName'] = e
          fetchListByPage(params).then(res2 => {
            if (res2.head.ret === 0) {
              this.userQueryList = res2.data.result
            }
          })
        }
      })
    },
    /** 返回 */
    handleClose(code) {
      this.$emit('closeDialog', code)
    },
    /** 移除图片 */
    handelRemove(file, fileList) {
      if (this.fileList.length > 0) {
        this.fileList.splice(this.fileList.findIndex(item => item.file.uid === file.uid), 1)
      }
    },
    /** 自定义上传  覆盖action */
    customUpload(arg) {
      console.log('customUpload', arg)
      const file = arg.file
      this.fileList.push({
        name: file.name,
        url: URL.createObjectURL(file),
        file
      })
      this.form.pic = [...this.fileList]
      console.log(this.fileList)
    },

    /** 提交 */
    submit() {
      this.form.labelType = this.labelType
      if (this.labelType === 'study') {
        // 标题不能为空
        if (!this.form.title) {
          this.$message.error('标题不能为空')
          return
        }
        // 学年不能为空
        if (!this.form.gradeLevel) {
          this.$message.error('学年不能为空')
          return
        }
        // 年级不能为空
        if (!this.form.gradeNum) {
          this.$message.error('年级不能为空')
          return
        }
        // 学科不能为空
        if (!this.form.subject) {
          this.$message.error('学科不能为空')
          return
        }
      } else if (this.labelType === 'label') {
        // 标签不能为空
        if (!this.form.labelIds || this.form.labelIds.length < 1) {
          this.$message.error('标签不能为空')
          return
        }
      } else {
        // 请选择labelType
        this.$message.error('请选择圈子类型')
        return
      }

      // userId不能为空
      if (!this.form.userId) {
        this.$message.error('用户不能为空')
        return
      }

      // 图片不能为空
      if (!this.fileList || this.fileList.length === 0) {
        this.$message.error('图片不能为空')
        return
      }

      const files = []
      this.fileList.forEach(f => {
        files.push(f.file)
      })
      if (files.length > 0) {
        oss.uploadFiles(files, (results, error) => {
          if (error) {
            this.$message.error('上传文件失败')
            return -1
          }
          if (results) {
            this.form.pic = []
            results.forEach(s => {
              this.form.pic.push(s.url)
            })
            this.addUpdateRecord()
          }
        })
      }
    },
    /** 提交 */
    submit2() {
      if (!this.form.delayedTime) {
        this.$message.error('定期发布时间不能为空')
        return
      }
      this.form.labelType = this.labelType
      if (this.labelType === 'study') {
        // 标题不能为空
        if (!this.form.title) {
          this.$message.error('标题不能为空')
          return
        }
        // 学年不能为空
        if (!this.form.gradeLevel) {
          this.$message.error('学年不能为空')
          return
        }
        // 年级不能为空
        if (!this.form.gradeNum) {
          this.$message.error('年级不能为空')
          return
        }
        // 学科不能为空
        if (!this.form.subject) {
          this.$message.error('学科不能为空')
          return
        }
      } else if (this.labelType === 'label') {
        // 标签不能为空
        if (!this.form.labelIds || this.form.labelIds.length < 1) {
          this.$message.error('标签不能为空')
          return
        }
      } else {
        // 请选择labelType
        this.$message.error('请选择圈子类型')
        return
      }

      // userId不能为空
      if (!this.form.userId) {
        this.$message.error('用户不能为空')
        return
      }

      // 图片不能为空
      if (!this.fileList || this.fileList.length === 0) {
        this.$message.error('图片不能为空')
        return
      }

      const files = []
      this.fileList.forEach(f => {
        files.push(f.file)
      })
      if (files.length > 0) {
        oss.uploadFiles(files, (results, error) => {
          if (error) {
            this.$message.error('上传文件失败')
            return -1
          }
          if (results) {
            this.form.pic = []
            results.forEach(s => {
              this.form.pic.push(s.url)
            })
            this.addUpdateRecord2()
          }
        })
      }
    },
    /** 保存 */
    async addUpdateRecord() {
      // 新增
      if (!this.form.id) {
        this.form.id = ''
      }
      // 更新
      const res = await saveRecord(this.form)
      if (res.head.ret === 0) {
        this.handleClose('1')
        this.$message.success('保存成功')
      } else {
        this.$message.error(res.head.msg)
      }
    },
    /** 保存 */
    async addUpdateRecord2() {
      // 新增
      if (!this.form.id) {
        this.form.id = ''
      }
      // 更新
      const res = await saveRecordDelayed(this.form)
      if (res.head.ret === 0) {
        this.handleClose('1')
        this.$message.success('保存成功')
      } else {
        this.$message.error(res.head.msg)
      }
    },
    /** 勾选标签 */
    onChangeLabel(item) {
      const str = '#' + item.titleZh + '#'
      // 偶数次加标签
      if (item.count % 2 === 0) {
        this.form.content += str
      } else {
        if (this.form.content.indexOf(str) > -1) {
          this.form.content = this.form.content.replace(str, '')
        }
      }
      // 奇数查找删除 对应标签
      item.count++
    },
    /** 获取列表 */
    async getLabelList() {
      const res = await findLabelPage({
        pageno: 1,
        pagesize: 100
      })
      if (res.head.ret === 0) {
        console.log('getLabelList', res.data)
        const list = res.data
        list.forEach(item => {
          item.count = 0
        })
        this.labelList = list
      }
    }
  }

}
</script>

<style lang='scss' scoped>
.label-span{
  line-height: 30px;
  height: 30px;
  border-radius: 5px;
  margin-right: 10px;
  cursor: pointer;
}
.check-label-span{
  background: #409EFF;
  padding: 5px;
}
 .avatar-uploader .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }
  .avatar-uploader .el-upload:hover {
    border-color: #409EFF;
  }
  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 178px;
    height: 178px;
    line-height: 178px;
    text-align: center;
    border: 1px solid gray;
  }
  .avatar {
    width: 178px;
    height: 178px;
    display: block;
  }
</style>
