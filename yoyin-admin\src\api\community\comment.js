import request from '@/utils/request'

// 翻页查询
const LOAD_DATA_URL = '/platform/gam/community/v1/comment/findcommentpage'
// 删除记录
const DELETE_RECORD_URL = '/platform/gam/community/v1/comment/delcomm'

// 保存记录
const SAVE_RECORD_URL = '/platform/gam/community/v1/comment/sendcomment'

/**
 * 保存记录
 * @param ids 记录id
 */
export const saveRecord = async params => {
  return request({
    url: SAVE_RECORD_URL,
    method: 'post',
    data: params
  })
}
/**
 * 查询列表数据
 * @param params 参数
 */
export const fetchListByPage = async params => {
  return request({
    url: LOAD_DATA_URL,
    method: 'get',
    params
  })
}

/**
 * 删除记录
 * @param ids 记录id
 */
export const deleteRecord = async params => {
  return request({
    url: DELETE_RECORD_URL,
    method: 'get',
    params
  })
}
