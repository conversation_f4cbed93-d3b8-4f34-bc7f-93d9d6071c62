{"msg": "success", "code": 200, "data": {"paperDesc": "", "downCount": 0, "paperType": "课时练习", "paperTypeCode": "13", "paperTotalCount": 19, "paperName": "北师大版七年级下册第1课时  对顶角、余角和补角(195)", "latexPaperName": null, "latexPaperDesc": null, "creatorGuid": 109948234, "grade": "1217", "gradeName": "七年级", "viewCount": 1, "source": 2, "province": null, "timeModified": 1652755124, "paperStructure": [{"segmentName": null, "isValid": 0, "sectionId": null, "name": null, "desc": null, "children": [{"bigExamQuestionId": null, "segmentId": null, "parentId": null, "parentIds": null, "bigExamQuestionName": null, "desc": null, "sort": null, "questionId": null, "score": 0.0, "type": 1, "questionInfo": null, "children": [{"bigExamQuestionId": null, "segmentId": null, "parentId": null, "parentIds": null, "bigExamQuestionName": null, "desc": null, "sort": 0, "questionId": "b731b897-c1d2-4554-bff5-abd50aa997f8", "score": 0.0, "type": 2, "questionInfo": {"explain": [], "questionId": "b731b897-c1d2-4554-bff5-abd50aa997f8", "subjective": {"code": false, "name": "客观题"}, "children": [], "grade": {"code": "1217", "name": "七年级"}, "context": {"stem": "如图，已知直线<img class=\"math-tex\"  style=\"vertical-align:-1.01px\" src=\"http://cp-cloud-files-tiku.oss-cn-hangzhou.aliyuncs.com/latex2svg/1212/b86fc6b051f63d73de262d4c34e3a0a9.svg\" />和<img class=\"math-tex\"  style=\"vertical-align:-1.98px\" src=\"http://cp-cloud-files-tiku.oss-cn-hangzhou.aliyuncs.com/latex2svg/1212/4170acd6af571e8d0d59fdad999cc605.svg\" />相交于点<img class=\"math-tex\"  style=\"vertical-align:-1.98px\" src=\"http://cp-cloud-files-tiku.oss-cn-hangzhou.aliyuncs.com/latex2svg/1212/f186217753c37b9b9f958d906208506e.svg\" />，<img class=\"math-tex\"  style=\"vertical-align:-1.98px\" src=\"http://cp-cloud-files-tiku.oss-cn-hangzhou.aliyuncs.com/latex2svg/1212/0150db2274414ad50b1c535efd9d96a1.svg\" />，<img class=\"math-tex\"  style=\"vertical-align:-1.98px\" src=\"http://cp-cloud-files-tiku.oss-cn-hangzhou.aliyuncs.com/latex2svg/1212/a77bec48afebcfcb102d7142373c4337.svg\" />,则<img class=\"math-tex\"  style=\"vertical-align:-1.98px\" src=\"http://cp-cloud-files-tiku.oss-cn-hangzhou.aliyuncs.com/latex2svg/1212/3e7becd76073e010d78cdda9896269b7.svg\" />的度数是<span class=\"fill\">&#8203;</span>度．\n</br><img src='http://qp-tiku.oss-cn-beijing.aliyuncs.com/ImageFile/cf11d37caaa311e892ee00163e2ea669/e71aea3ccb0000e74c35705967f7a9b9.png'/>", "audio": null, "original_text": null, "options": null}, "useCount": 0, "quesStruct": {"code": "13", "name": "填空模板"}, "quesType": {"code": "13", "name": "填空题"}, "difficulty": {"code": "2", "name": "较易"}, "knowledge": [{"code": "14466475499", "name": "余角和补角"}, {"code": "14467502507", "name": "邻补角性质"}], "updateTime": 1615962820, "year": {"isDeprecated": "0", "code": "2019", "isReplace": "0", "name": "2019年", "isHide": "0"}, "questionStatus": {"code": "1", "name": "启用"}, "isCollected": 0, "isAddedToBox": 0, "source": 1, "canCollect": true}, "children": [], "layeredLevelCode": null, "layeredLevelName": null, "showTypeScore": null, "perQuesScore": null, "count": 0}, {"bigExamQuestionId": null, "segmentId": null, "parentId": null, "parentIds": null, "bigExamQuestionName": null, "desc": null, "sort": 1, "questionId": "94c5dece-446e-4387-95e6-fac66bfc5c19", "score": 0.0, "type": 2, "questionInfo": {"explain": [], "questionId": "94c5dece-446e-4387-95e6-fac66bfc5c19", "subjective": {"code": true, "name": "主观题"}, "children": [{"explain": [], "questionId": "34a9c06e-dab5-42db-8174-18f6f717e066", "subjective": {"code": true, "name": "主观题"}, "children": [], "grade": {"code": null, "name": null}, "context": {"stem": "写出<span class=\"math-tex\"><img class=\"math-tex\"  style=\"vertical-align:-1.98px\" src=\"http://cp-cloud-files-tiku.oss-cn-hangzhou.aliyuncs.com/latex2svg/1212/3e7becd76073e010d78cdda9896269b7.svg\" /></span>的邻补角；", "audio": null, "original_text": null, "options": null}, "useCount": 0, "quesStruct": {"code": "18", "name": "解答模板"}, "quesType": {"code": "30", "name": "解答题"}, "difficulty": {"code": "2", "name": "较易"}, "knowledge": [{"code": "14467502505", "name": "对顶角、邻补角概念"}], "updateTime": 1669694157, "year": {}, "questionStatus": {"code": "1", "name": "启用"}, "isCollected": 0, "isAddedToBox": 0, "source": 0, "canCollect": true}, {"explain": [], "questionId": "44fb0436-1a3b-40a9-be06-60594b5e0735", "subjective": {"code": true, "name": "主观题"}, "children": [], "grade": {"code": null, "name": null}, "context": {"stem": "分别写出<span class=\"math-tex\"><img class=\"math-tex\"  style=\"vertical-align:-1.98px\" src=\"http://cp-cloud-files-tiku.oss-cn-hangzhou.aliyuncs.com/latex2svg/1212/3e7becd76073e010d78cdda9896269b7.svg\" /></span>和<span class=\"math-tex\"><img class=\"math-tex\"  style=\"vertical-align:-1.98px\" src=\"http://cp-cloud-files-tiku.oss-cn-hangzhou.aliyuncs.com/latex2svg/1212/49f600a7f6c5cf2f51994e5d50889893.svg\" /></span>的对顶角；", "audio": null, "original_text": null, "options": null}, "useCount": 0, "quesStruct": {"code": "18", "name": "解答模板"}, "quesType": {"code": "30", "name": "解答题"}, "difficulty": {"code": "2", "name": "较易"}, "knowledge": [{"code": "14467502505", "name": "对顶角、邻补角概念"}], "updateTime": 1669694157, "year": {}, "questionStatus": {"code": "1", "name": "启用"}, "isCollected": 0, "isAddedToBox": 0, "source": 0, "canCollect": true}, {"explain": [], "questionId": "f64b3ff3-c241-4263-a8a8-a8df841bc6a1", "subjective": {"code": true, "name": "主观题"}, "children": [], "grade": {"code": null, "name": null}, "context": {"stem": "如果<span class=\"math-tex\"><img class=\"math-tex\"  style=\"vertical-align:-1.98px\" src=\"http://cp-cloud-files-tiku.oss-cn-hangzhou.aliyuncs.com/latex2svg/1212/f3804e1600f201881263915cd17bd19f.svg\" /></span>，<span class=\"math-tex\"><img class=\"math-tex\"  style=\"vertical-align:-1.98px\" src=\"http://cp-cloud-files-tiku.oss-cn-hangzhou.aliyuncs.com/latex2svg/1212/e7b57084afd45bf1dec2b8cfd00d7f79.svg\" /></span>，求<span class=\"math-tex\"><img class=\"math-tex\"  style=\"vertical-align:-1.98px\" src=\"http://cp-cloud-files-tiku.oss-cn-hangzhou.aliyuncs.com/latex2svg/1212/0444d142e61c2f5c8848270f46d1989e.svg\" /></span>和<span class=\"math-tex\"><img class=\"math-tex\"  style=\"vertical-align:-1.98px\" src=\"http://cp-cloud-files-tiku.oss-cn-hangzhou.aliyuncs.com/latex2svg/1212/6749ba3b7b8d3984b23e5d2b6b764a48.svg\" /></span>的度数．", "audio": null, "original_text": null, "options": null}, "useCount": 0, "quesStruct": {"code": "18", "name": "解答模板"}, "quesType": {"code": "30", "name": "解答题"}, "difficulty": {"code": "2", "name": "较易"}, "knowledge": [{"code": "14467502506", "name": "对顶角性质"}, {"code": "14467502507", "name": "邻补角性质"}], "updateTime": 1669694157, "year": {}, "questionStatus": {"code": "1", "name": "启用"}, "isCollected": 0, "isAddedToBox": 0, "source": 0, "canCollect": true}], "grade": {"code": "1217", "name": "七年级"}, "context": {"stem": "如图，直线<span class=\"math-tex\"><img class=\"math-tex\"  style=\"vertical-align:-4.90px\" src=\"http://cp-cloud-files-tiku.oss-cn-hangzhou.aliyuncs.com/latex2svg/1212/8f2ca5b58661ba4832f20e56e793a0b8.svg\" /></span>相交于点<span class=\"math-tex\"><img class=\"math-tex\"  style=\"vertical-align:-1.98px\" src=\"http://cp-cloud-files-tiku.oss-cn-hangzhou.aliyuncs.com/latex2svg/1212/f186217753c37b9b9f958d906208506e.svg\" /></span>．\n<br><img src=\"http://qp-tiku.oss-cn-beijing.aliyuncs.com/ImageFile/cf11d37caaa311e892ee00163e2ea669/53ea91e8311325ce45a7f738c05e4927.png\">\n", "audio": null, "original_text": null, "options": null}, "useCount": 2, "quesStruct": {"code": "16", "name": "多小题模板"}, "quesType": {"code": "30", "name": "解答题"}, "difficulty": {"code": "2", "name": "较易"}, "knowledge": [{"code": "14467502506", "name": "对顶角性质"}, {"code": "14467502507", "name": "邻补角性质"}, {"code": "14467502505", "name": "对顶角、邻补角概念"}], "updateTime": 1669694157, "year": {"isDeprecated": "0", "code": "2019", "isReplace": "0", "name": "2019年", "isHide": "0"}, "questionStatus": {"code": "1", "name": "启用"}, "isCollected": 0, "isAddedToBox": 0, "source": 1, "canCollect": true}, "children": [], "layeredLevelCode": null, "layeredLevelName": null, "showTypeScore": null, "perQuesScore": null, "count": 0}, {"bigExamQuestionId": null, "segmentId": null, "parentId": null, "parentIds": null, "bigExamQuestionName": null, "desc": null, "sort": 2, "questionId": "2ba55d57-a815-4c50-b4d6-3d1015749f45", "score": 0.0, "type": 2, "questionInfo": {"explain": [], "questionId": "2ba55d57-a815-4c50-b4d6-3d1015749f45", "subjective": {"code": false, "name": "客观题"}, "children": [], "grade": {"code": "1217", "name": "七年级"}, "context": {"stem": "如图，已知<span class=\"math-tex\"><img class=\"math-tex\"  style=\"vertical-align:-1.98px\" src=\"http://cp-cloud-files-tiku.oss-cn-hangzhou.aliyuncs.com/latex2svg/1212/f186217753c37b9b9f958d906208506e.svg\" /></span>为<span class=\"math-tex\"><img class=\"math-tex\"  style=\"vertical-align:-1.01px\" src=\"http://cp-cloud-files-tiku.oss-cn-hangzhou.aliyuncs.com/latex2svg/1212/e182ebbc166d73366e7986813a7fc5f1.svg\" /></span>上一点，<span class=\"math-tex\"><img class=\"math-tex\"  style=\"vertical-align:-1.98px\" src=\"http://cp-cloud-files-tiku.oss-cn-hangzhou.aliyuncs.com/latex2svg/1212/71508ca556dc7937887a57d93b1c2aa1.svg\" /></span>与<span class=\"math-tex\"><img class=\"math-tex\"  style=\"vertical-align:-1.98px\" src=\"http://cp-cloud-files-tiku.oss-cn-hangzhou.aliyuncs.com/latex2svg/1212/a2e2d648791ec036111393eca17d8025.svg\" /></span>互补，<span class=\"math-tex\"><img class=\"math-tex\"  style=\"vertical-align:-1.98px\" src=\"http://cp-cloud-files-tiku.oss-cn-hangzhou.aliyuncs.com/latex2svg/1212/bfbebc0782222b0f8f991f1405ec537d.svg\" /></span>，<span class=\"math-tex\"><img class=\"math-tex\"  style=\"vertical-align:-1.98px\" src=\"http://cp-cloud-files-tiku.oss-cn-hangzhou.aliyuncs.com/latex2svg/1212/90651ebea9a35ec4e018c8157492e17c.svg\" /></span>分别为<span class=\"math-tex\"><img class=\"math-tex\"  style=\"vertical-align:-1.98px\" src=\"http://cp-cloud-files-tiku.oss-cn-hangzhou.aliyuncs.com/latex2svg/1212/71508ca556dc7937887a57d93b1c2aa1.svg\" /></span>，<span class=\"math-tex\"><img class=\"math-tex\"  style=\"vertical-align:-1.98px\" src=\"http://cp-cloud-files-tiku.oss-cn-hangzhou.aliyuncs.com/latex2svg/1212/a2e2d648791ec036111393eca17d8025.svg\" /></span>的平分线，若<span class=\"math-tex\"><img class=\"math-tex\"  style=\"vertical-align:-1.98px\" src=\"http://cp-cloud-files-tiku.oss-cn-hangzhou.aliyuncs.com/latex2svg/1212/7540112a61e9f1b8f372e148cfa06c52.svg\" /></span>，试求<span class=\"math-tex\"><img class=\"math-tex\"  style=\"vertical-align:-1.98px\" src=\"http://cp-cloud-files-tiku.oss-cn-hangzhou.aliyuncs.com/latex2svg/1212/71508ca556dc7937887a57d93b1c2aa1.svg\" /></span>与<span class=\"math-tex\"><img class=\"math-tex\"  style=\"vertical-align:-1.98px\" src=\"http://cp-cloud-files-tiku.oss-cn-hangzhou.aliyuncs.com/latex2svg/1212/a2e2d648791ec036111393eca17d8025.svg\" /></span>的度数．\n<br><img src=\"http://qp-tiku.oss-cn-beijing.aliyuncs.com/ImageFile/f2365d54f5d711e792fc00163e2ea669/2b368078f783165619f2aee3326c8566.jpg\">", "audio": null, "original_text": null, "options": null}, "useCount": 1, "quesStruct": {"code": "18", "name": "解答模板"}, "quesType": {"code": "30", "name": "解答题"}, "difficulty": {"code": "2", "name": "较易"}, "knowledge": [{"code": "14466475497", "name": "角的计算"}], "updateTime": 1615962820, "year": {"isDeprecated": "0", "code": "2018", "isReplace": "0", "name": "2018年", "isHide": "0"}, "questionStatus": {"code": "1", "name": "启用"}, "isCollected": 0, "isAddedToBox": 0, "source": 1, "canCollect": true}, "children": [], "layeredLevelCode": null, "layeredLevelName": null, "showTypeScore": null, "perQuesScore": null, "count": 0}, {"bigExamQuestionId": null, "segmentId": null, "parentId": null, "parentIds": null, "bigExamQuestionName": null, "desc": null, "sort": 3, "questionId": "28471e74-dcd7-4b55-ad8f-12621d399445", "score": 0.0, "type": 2, "questionInfo": {"explain": [], "questionId": "28471e74-dcd7-4b55-ad8f-12621d399445", "subjective": {"code": true, "name": "主观题"}, "children": [{"explain": [], "questionId": "c0f9d8a4-0fb9-4937-9de4-cf5fd91d4d4f", "subjective": {"code": true, "name": "主观题"}, "children": [], "grade": {"code": null, "name": null}, "context": {"stem": "求<img class=\"math-tex\"  style=\"vertical-align:-1.98px\" src=\"http://cp-cloud-files-tiku.oss-cn-hangzhou.aliyuncs.com/latex2svg/1212/49f600a7f6c5cf2f51994e5d50889893.svg\" />的度数；", "audio": null, "original_text": null, "options": null}, "useCount": 0, "quesStruct": {"code": "18", "name": "解答模板"}, "quesType": {"code": "30", "name": "解答题"}, "difficulty": {"code": "2", "name": "较易"}, "knowledge": [{"code": "14466475498", "name": "角平分线的定义"}, {"code": "14467502506", "name": "对顶角性质"}], "updateTime": 1582524434, "year": {}, "questionStatus": {"code": "1", "name": "启用"}, "isCollected": 0, "isAddedToBox": 0, "source": 0, "canCollect": true}, {"explain": [], "questionId": "8c4dd5d2-7762-452a-9571-c93989da39c8", "subjective": {"code": true, "name": "主观题"}, "children": [], "grade": {"code": null, "name": null}, "context": {"stem": "<img class=\"math-tex\"  style=\"vertical-align:-1.98px\" src=\"http://cp-cloud-files-tiku.oss-cn-hangzhou.aliyuncs.com/latex2svg/1212/7e4137add2dfe45e078e7f076aec84df.svg\" />是<img class=\"math-tex\"  style=\"vertical-align:-1.98px\" src=\"http://cp-cloud-files-tiku.oss-cn-hangzhou.aliyuncs.com/latex2svg/1212/71508ca556dc7937887a57d93b1c2aa1.svg\" />的平分线吗？请说明理由．", "audio": null, "original_text": null, "options": null}, "useCount": 0, "quesStruct": {"code": "18", "name": "解答模板"}, "quesType": {"code": "30", "name": "解答题"}, "difficulty": {"code": "3", "name": "中等"}, "knowledge": [{"code": "14466475499", "name": "余角和补角"}, {"code": "14466475498", "name": "角平分线的定义"}], "updateTime": 1582524434, "year": {}, "questionStatus": {"code": "1", "name": "启用"}, "isCollected": 0, "isAddedToBox": 0, "source": 0, "canCollect": true}], "grade": {"code": "1217", "name": "七年级"}, "context": {"stem": "如图所示,直线<img class=\"math-tex\"  style=\"vertical-align:-1.01px\" src=\"http://cp-cloud-files-tiku.oss-cn-hangzhou.aliyuncs.com/latex2svg/1212/b86fc6b051f63d73de262d4c34e3a0a9.svg\" />，<img class=\"math-tex\"  style=\"vertical-align:-1.98px\" src=\"http://cp-cloud-files-tiku.oss-cn-hangzhou.aliyuncs.com/latex2svg/1212/4170acd6af571e8d0d59fdad999cc605.svg\" />相交于点<img class=\"math-tex\"  style=\"vertical-align:-1.98px\" src=\"http://cp-cloud-files-tiku.oss-cn-hangzhou.aliyuncs.com/latex2svg/1212/f186217753c37b9b9f958d906208506e.svg\" />，<img class=\"math-tex\"  style=\"vertical-align:-1.98px\" src=\"http://cp-cloud-files-tiku.oss-cn-hangzhou.aliyuncs.com/latex2svg/1212/00e099a387e46b6681e536b05f110339.svg\" />是<img class=\"math-tex\"  style=\"vertical-align:-1.98px\" src=\"http://cp-cloud-files-tiku.oss-cn-hangzhou.aliyuncs.com/latex2svg/1212/e455b4334a67fa45e62c7b4314b23ac3.svg\" />的平分线，<img class=\"math-tex\"  style=\"vertical-align:-4.90px\" src=\"http://cp-cloud-files-tiku.oss-cn-hangzhou.aliyuncs.com/latex2svg/1212/17c7ae2655089e5206697325c0a725c6.svg\" />.\n</br><img src='http://qp-tiku.oss-cn-beijing.aliyuncs.com/ImageFile/cf11d37caaa311e892ee00163e2ea669/2b48840135e4cd5b42b6c373a2fa4299.png'/>\n", "audio": null, "original_text": null, "options": null}, "useCount": 0, "quesStruct": {"code": "16", "name": "多小题模板"}, "quesType": {"code": "30", "name": "解答题"}, "difficulty": {"code": "3", "name": "中等"}, "knowledge": [{"code": "14466475499", "name": "余角和补角"}, {"code": "14466475498", "name": "角平分线的定义"}, {"code": "14467502506", "name": "对顶角性质"}], "updateTime": 1582524434, "year": {"isDeprecated": "0", "code": "2019", "isReplace": "0", "name": "2019年", "isHide": "0"}, "questionStatus": {"code": "1", "name": "启用"}, "isCollected": 0, "isAddedToBox": 0, "source": 1, "canCollect": true}, "children": [], "layeredLevelCode": null, "layeredLevelName": null, "showTypeScore": null, "perQuesScore": null, "count": 0}, {"bigExamQuestionId": null, "segmentId": null, "parentId": null, "parentIds": null, "bigExamQuestionName": null, "desc": null, "sort": 4, "questionId": "83471018-bdd5-4ca4-b32a-63bbef676122", "score": 0.0, "type": 2, "questionInfo": {"explain": [], "questionId": "83471018-bdd5-4ca4-b32a-63bbef676122", "subjective": {"code": true, "name": "主观题"}, "children": [{"explain": [], "questionId": "b1ec3347-779e-4da7-ac0a-692170f70ce4", "subjective": {"code": true, "name": "主观题"}, "children": [], "grade": {"code": null, "name": null}, "context": {"stem": "如图<img src='http://qp-tiku.oss-cn-beijing.aliyuncs.com/ImageFile/cf11d37caaa311e892ee00163e2ea669/e627f8d01bf661e27a591232ddc616ce.png'/>，图中共有<span class=\"fill\">&#8203;</span>对对顶角；", "audio": null, "original_text": null, "options": null}, "useCount": 0, "quesStruct": {"code": "13", "name": "填空模板"}, "quesType": {"code": "13", "name": "填空题"}, "difficulty": {"code": "2", "name": "较易"}, "knowledge": [{"code": "14467502505", "name": "对顶角、邻补角概念"}], "updateTime": 1582524434, "year": {}, "questionStatus": {"code": "1", "name": "启用"}, "isCollected": 0, "isAddedToBox": 0, "source": 0, "canCollect": true}, {"explain": [], "questionId": "17c6e125-2a48-4d10-b01f-fbbf4dce149a", "subjective": {"code": true, "name": "主观题"}, "children": [], "grade": {"code": null, "name": null}, "context": {"stem": "如图<img src='http://qp-tiku.oss-cn-beijing.aliyuncs.com/ImageFile/cf11d37caaa311e892ee00163e2ea669/a714372de3684d5a118650f519ae644c.png'/>，图中共有<span class=\"fill\">&#8203;</span>对对顶角；", "audio": null, "original_text": null, "options": null}, "useCount": 0, "quesStruct": {"code": "13", "name": "填空模板"}, "quesType": {"code": "13", "name": "填空题"}, "difficulty": {"code": "2", "name": "较易"}, "knowledge": [{"code": "14467502505", "name": "对顶角、邻补角概念"}], "updateTime": 1582524434, "year": {}, "questionStatus": {"code": "1", "name": "启用"}, "isCollected": 0, "isAddedToBox": 0, "source": 0, "canCollect": true}, {"explain": [], "questionId": "b3aaad13-505e-4a73-abca-ae11591ce001", "subjective": {"code": true, "name": "主观题"}, "children": [], "grade": {"code": null, "name": null}, "context": {"stem": "如图<img src='http://qp-tiku.oss-cn-beijing.aliyuncs.com/ImageFile/cf11d37caaa311e892ee00163e2ea669/7f85d705ec3a7e39dfb52d932a6e136e.png'/>，图中共有<span class=\"fill\">&#8203;</span>对对顶角；", "audio": null, "original_text": null, "options": null}, "useCount": 0, "quesStruct": {"code": "13", "name": "填空模板"}, "quesType": {"code": "13", "name": "填空题"}, "difficulty": {"code": "2", "name": "较易"}, "knowledge": [{"code": "14467502505", "name": "对顶角、邻补角概念"}], "updateTime": 1582524434, "year": {}, "questionStatus": {"code": "1", "name": "启用"}, "isCollected": 0, "isAddedToBox": 0, "source": 0, "canCollect": true}, {"explain": [], "questionId": "dbcc5e19-f079-43a9-b60c-edd1f76e0fc8", "subjective": {"code": true, "name": "主观题"}, "children": [], "grade": {"code": null, "name": null}, "context": {"stem": "研究<img class=\"math-tex\"  style=\"vertical-align:-5.88px\" src=\"http://cp-cloud-files-tiku.oss-cn-hangzhou.aliyuncs.com/latex2svg/1212/7acce3193127d4b71a6c2b140c22dc95.svg\" />～<img class=\"math-tex\"  style=\"vertical-align:-5.88px\" src=\"http://cp-cloud-files-tiku.oss-cn-hangzhou.aliyuncs.com/latex2svg/1212/38560d3d403d928e8b6a1e4e8f9e93e1.svg\" />小题中直线条数与对顶角的对数之间的关系．若有<img class=\"math-tex\"  style=\"vertical-align:-1.98px\" src=\"http://cp-cloud-files-tiku.oss-cn-hangzhou.aliyuncs.com/latex2svg/1212/7b8b965ad4bca0e41ab51de7b31363a1.svg\" />条直线相交于一点，则可形成<span class=\"fill\">&#8203;</span>对对顶角;", "audio": null, "original_text": null, "options": null}, "useCount": 0, "quesStruct": {"code": "13", "name": "填空模板"}, "quesType": {"code": "13", "name": "填空题"}, "difficulty": {"code": "3", "name": "中等"}, "knowledge": [{"code": "14467502505", "name": "对顶角、邻补角概念"}], "updateTime": 1582524434, "year": {}, "questionStatus": {"code": "1", "name": "启用"}, "isCollected": 0, "isAddedToBox": 0, "source": 0, "canCollect": true}, {"explain": [], "questionId": "19b09f8d-bfa0-4dc2-ab5b-0d536292082f", "subjective": {"code": true, "name": "主观题"}, "children": [], "grade": {"code": null, "name": null}, "context": {"stem": "若有<img class=\"math-tex\"  style=\"vertical-align:-1.98px\" src=\"http://cp-cloud-files-tiku.oss-cn-hangzhou.aliyuncs.com/latex2svg/1212/84ddfb34126fc3a48ee38d7044e87276.svg\" />条直线相交于一点，则可形成<span class=\"fill\">&#8203;</span>对对顶角．", "audio": null, "original_text": null, "options": null}, "useCount": 0, "quesStruct": {"code": "13", "name": "填空模板"}, "quesType": {"code": "13", "name": "填空题"}, "difficulty": {"code": "2", "name": "较易"}, "knowledge": [{"code": "14467502505", "name": "对顶角、邻补角概念"}], "updateTime": 1582524434, "year": {}, "questionStatus": {"code": "1", "name": "启用"}, "isCollected": 0, "isAddedToBox": 0, "source": 0, "canCollect": true}], "grade": {"code": "1217", "name": "七年级"}, "context": {"stem": "观察下列各图，寻找对顶角(不含平角)：\n</br><img src='http://qp-tiku.oss-cn-beijing.aliyuncs.com/ImageFile/cf11d37caaa311e892ee00163e2ea669/e0a2ce3e649d6816137da498dcdda2fe.png'/>\n", "audio": null, "original_text": null, "options": null}, "useCount": 0, "quesStruct": {"code": "16", "name": "多小题模板"}, "quesType": {"code": "30", "name": "解答题"}, "difficulty": {"code": "3", "name": "中等"}, "knowledge": [{"code": "14467502505", "name": "对顶角、邻补角概念"}], "updateTime": 1582524434, "year": {"isDeprecated": "0", "code": "2019", "isReplace": "0", "name": "2019年", "isHide": "0"}, "questionStatus": {"code": "1", "name": "启用"}, "isCollected": 0, "isAddedToBox": 0, "source": 1, "canCollect": true}, "children": [], "layeredLevelCode": null, "layeredLevelName": null, "showTypeScore": null, "perQuesScore": null, "count": 0}, {"bigExamQuestionId": null, "segmentId": null, "parentId": null, "parentIds": null, "bigExamQuestionName": null, "desc": null, "sort": 5, "questionId": "9377981c-bb74-4fbd-bce1-cd8274402486", "score": 0.0, "type": 2, "questionInfo": {"explain": [], "questionId": "9377981c-bb74-4fbd-bce1-cd8274402486", "subjective": {"code": false, "name": "客观题"}, "children": [], "grade": {"code": "1217", "name": "七年级"}, "context": {"stem": "如图是对顶角量角器，用它测量角的原理是<span class=\"fill\"> &#8203;</span>．\n<br><img src=\"http://qp-tiku.oss-cn-beijing.aliyuncs.com/ImageFile/cf11d37caaa311e892ee00163e2ea669/d3f74d939b415b0229e986b44a148d7b.png\">\n", "audio": null, "original_text": null, "options": null}, "useCount": 1, "quesStruct": {"code": "13", "name": "填空模板"}, "quesType": {"code": "13", "name": "填空题"}, "difficulty": {"code": "2", "name": "较易"}, "knowledge": [{"code": "14467502506", "name": "对顶角性质"}], "updateTime": 1613992570, "year": {"isDeprecated": "0", "code": "2019", "isReplace": "0", "name": "2019年", "isHide": "0"}, "questionStatus": {"code": "1", "name": "启用"}, "isCollected": 0, "isAddedToBox": 0, "source": 1, "canCollect": true}, "children": [], "layeredLevelCode": null, "layeredLevelName": null, "showTypeScore": null, "perQuesScore": null, "count": 0}, {"bigExamQuestionId": null, "segmentId": null, "parentId": null, "parentIds": null, "bigExamQuestionName": null, "desc": null, "sort": 6, "questionId": "475f35c6-97ff-4a89-9141-db901337d685", "score": 0.0, "type": 2, "questionInfo": {"explain": [], "questionId": "475f35c6-97ff-4a89-9141-db901337d685", "subjective": {"code": false, "name": "客观题"}, "children": [], "grade": {"code": "1217", "name": "七年级"}, "context": {"stem": "如图，直线<span class=\"math-tex\"><img class=\"math-tex\"  style=\"vertical-align:-1.01px\" src=\"http://cp-cloud-files-tiku.oss-cn-hangzhou.aliyuncs.com/latex2svg/1212/b86fc6b051f63d73de262d4c34e3a0a9.svg\" /></span>和<span class=\"math-tex\"><img class=\"math-tex\"  style=\"vertical-align:-1.98px\" src=\"http://cp-cloud-files-tiku.oss-cn-hangzhou.aliyuncs.com/latex2svg/1212/4170acd6af571e8d0d59fdad999cc605.svg\" /></span>相交于点<span class=\"math-tex\"><img class=\"math-tex\"  style=\"vertical-align:-1.98px\" src=\"http://cp-cloud-files-tiku.oss-cn-hangzhou.aliyuncs.com/latex2svg/1212/f186217753c37b9b9f958d906208506e.svg\" /></span>，若<span class=\"math-tex\"><img class=\"math-tex\"  style=\"vertical-align:-1.98px\" src=\"http://cp-cloud-files-tiku.oss-cn-hangzhou.aliyuncs.com/latex2svg/1212/330a1ac7d0bef3672856f4319aef2651.svg\" /></span>，则<span class=\"math-tex\"><img class=\"math-tex\"  style=\"vertical-align:-1.98px\" src=\"http://cp-cloud-files-tiku.oss-cn-hangzhou.aliyuncs.com/latex2svg/1212/71508ca556dc7937887a57d93b1c2aa1.svg\" /></span>的度数为 （<span class=\"brack\"></span>）\n<br><img src=\"http://qp-tiku.oss-cn-beijing.aliyuncs.com/ImageFile/cf11d37caaa311e892ee00163e2ea669/2468c6dfee6de34f2af1d0c10ca42f99.png\">\n", "audio": null, "original_text": null, "options": ["<span class=\"math-tex\"><img class=\"math-tex\"  style=\"vertical-align:-1.01px\" src=\"http://cp-cloud-files-tiku.oss-cn-hangzhou.aliyuncs.com/latex2svg/1212/23972994c3649defe35e68de8321ab7b.svg\" /></span>", "<span class=\"math-tex\"><img class=\"math-tex\"  style=\"vertical-align:-1.98px\" src=\"http://cp-cloud-files-tiku.oss-cn-hangzhou.aliyuncs.com/latex2svg/1212/e38a4713a34639bbfdacf5b4adb97c1e.svg\" /></span>", "<span class=\"math-tex\"><img class=\"math-tex\"  style=\"vertical-align:-1.98px\" src=\"http://cp-cloud-files-tiku.oss-cn-hangzhou.aliyuncs.com/latex2svg/1212/4442327c35d77329bb03f44f18a9fa24.svg\" /></span>", "<span class=\"math-tex\"><img class=\"math-tex\"  style=\"vertical-align:-1.98px\" src=\"http://cp-cloud-files-tiku.oss-cn-hangzhou.aliyuncs.com/latex2svg/1212/5e707042e41a136ae5698ddb665c2b1c.svg\" /></span>"]}, "useCount": 0, "quesStruct": {"code": "11", "name": "单选模板"}, "quesType": {"code": "11", "name": "单选题"}, "difficulty": {"code": "2", "name": "较易"}, "knowledge": [{"code": "14467502507", "name": "邻补角性质"}], "updateTime": 1668587313, "year": {"isDeprecated": "0", "code": "2019", "isReplace": "0", "name": "2019年", "isHide": "0"}, "questionStatus": {"code": "1", "name": "启用"}, "isCollected": 0, "isAddedToBox": 0, "source": 1, "canCollect": true}, "children": [], "layeredLevelCode": null, "layeredLevelName": null, "showTypeScore": null, "perQuesScore": null, "count": 0}, {"bigExamQuestionId": null, "segmentId": null, "parentId": null, "parentIds": null, "bigExamQuestionName": null, "desc": null, "sort": 7, "questionId": "ff1440c8-b811-499e-a004-7b4699bb1c92", "score": 0.0, "type": 2, "questionInfo": {"explain": [], "questionId": "ff1440c8-b811-499e-a004-7b4699bb1c92", "subjective": {"code": false, "name": "客观题"}, "children": [], "grade": {"code": "1217", "name": "七年级"}, "context": {"stem": "如图,直线<span class=\"math-tex\"><img class=\"math-tex\"  style=\"vertical-align:-4.90px\" src=\"http://cp-cloud-files-tiku.oss-cn-hangzhou.aliyuncs.com/latex2svg/1212/6c7e726da684ff57aacce6f48c7b593f.svg\" /></span>交于点<span class=\"math-tex\"><img class=\"math-tex\"  style=\"vertical-align:-1.98px\" src=\"http://cp-cloud-files-tiku.oss-cn-hangzhou.aliyuncs.com/latex2svg/1212/f186217753c37b9b9f958d906208506e.svg\" /></span>,因为<span class=\"math-tex\"><img class=\"math-tex\"  style=\"vertical-align:-4.90px\" src=\"http://cp-cloud-files-tiku.oss-cn-hangzhou.aliyuncs.com/latex2svg/1212/dbe80d9acccd32091ba781e3cb6c7a97.svg\" /></span>,所以<span class=\"math-tex\"><img class=\"math-tex\"  style=\"vertical-align:-1.01px\" src=\"http://cp-cloud-files-tiku.oss-cn-hangzhou.aliyuncs.com/latex2svg/1212/fd9c41df89981858c39f3f679ffdd156.svg\" /></span>的依据是（<span class=\"brack\"></span>）\n<br><img src=\"http://qp-tiku.oss-cn-beijing.aliyuncs.com/ImageFile/68c8fd7e018511e8ad8d00163e2ea669/2d4f19131e5d0bc1216d31711e8f64af.jpg\">", "audio": null, "original_text": null, "options": ["同角的余角相等", "等角的余角相等", "同角的补角相等", "等角的补角相等"]}, "useCount": 2, "quesStruct": {"code": "11", "name": "单选模板"}, "quesType": {"code": "11", "name": "单选题"}, "difficulty": {"code": "2", "name": "较易"}, "knowledge": [{"code": "14466475499", "name": "余角和补角"}], "updateTime": 1662099303, "year": {"isDeprecated": "0", "code": "2018", "isReplace": "0", "name": "2018年", "isHide": "0"}, "questionStatus": {"code": "1", "name": "启用"}, "isCollected": 0, "isAddedToBox": 0, "source": 1, "canCollect": true}, "children": [], "layeredLevelCode": null, "layeredLevelName": null, "showTypeScore": null, "perQuesScore": null, "count": 0}, {"bigExamQuestionId": null, "segmentId": null, "parentId": null, "parentIds": null, "bigExamQuestionName": null, "desc": null, "sort": 8, "questionId": "f92e51d8-fe9f-4892-a4d7-4f0c8526df0c", "score": 0.0, "type": 2, "questionInfo": {"explain": [], "questionId": "f92e51d8-fe9f-4892-a4d7-4f0c8526df0c", "subjective": {"code": true, "name": "主观题"}, "children": [], "grade": {"code": "1217", "name": "七年级"}, "context": {"stem": "已知<img class=\"math-tex\"  style=\"vertical-align:-1.98px\" src=\"http://cp-cloud-files-tiku.oss-cn-hangzhou.aliyuncs.com/latex2svg/1212/45d03e26c9b3b899225d1f7653c75bcc.svg\" />，则<img class=\"math-tex\"  style=\"vertical-align:-1.98px\" src=\"http://cp-cloud-files-tiku.oss-cn-hangzhou.aliyuncs.com/latex2svg/1212/8dc749405ed48672d65ae5d828970705.svg\" />的余角的度数是<span class=\"fill\">&#8203;</span>．", "audio": null, "original_text": null, "options": null}, "useCount": 1, "quesStruct": {"code": "13", "name": "填空模板"}, "quesType": {"code": "13", "name": "填空题"}, "difficulty": {"code": "2", "name": "较易"}, "knowledge": [{"code": "14466475499", "name": "余角和补角"}], "updateTime": 1582524434, "year": {"isDeprecated": "0", "code": "2018", "isReplace": "0", "name": "2018年", "isHide": "0"}, "questionStatus": {"code": "1", "name": "启用"}, "isCollected": 0, "isAddedToBox": 0, "source": 1, "canCollect": true}, "children": [], "layeredLevelCode": null, "layeredLevelName": null, "showTypeScore": null, "perQuesScore": null, "count": 0}, {"bigExamQuestionId": null, "segmentId": null, "parentId": null, "parentIds": null, "bigExamQuestionName": null, "desc": null, "sort": 9, "questionId": "af8988c8-898d-4c4f-9ec5-8cd6851c33c5", "score": 0.0, "type": 2, "questionInfo": {"explain": [], "questionId": "af8988c8-898d-4c4f-9ec5-8cd6851c33c5", "subjective": {"code": true, "name": "主观题"}, "children": [], "grade": {"code": "1217", "name": "七年级"}, "context": {"stem": "如图，将一副三角尺叠放在一起，使直角顶点重合于点<img class=\"math-tex\"  style=\"vertical-align:-1.98px\" src=\"http://cp-cloud-files-tiku.oss-cn-hangzhou.aliyuncs.com/latex2svg/1212/f186217753c37b9b9f958d906208506e.svg\" />，绕点<img class=\"math-tex\"  style=\"vertical-align:-1.98px\" src=\"http://cp-cloud-files-tiku.oss-cn-hangzhou.aliyuncs.com/latex2svg/1212/f186217753c37b9b9f958d906208506e.svg\" />任意转动其中一个三角尺，则与<img class=\"math-tex\"  style=\"vertical-align:-1.98px\" src=\"http://cp-cloud-files-tiku.oss-cn-hangzhou.aliyuncs.com/latex2svg/1212/9debad3142742ba63376e8984f8a1e69.svg\" />始终相等的角是<span class=\"fill\">&#8203;</span>．\n</br><img src='http://qp-tiku.oss-cn-beijing.aliyuncs.com/ImageFile/cf11d37caaa311e892ee00163e2ea669/ac505aa32d908b2070cb166635371ef1.png'/>\n", "audio": null, "original_text": null, "options": null}, "useCount": 0, "quesStruct": {"code": "13", "name": "填空模板"}, "quesType": {"code": "13", "name": "填空题"}, "difficulty": {"code": "2", "name": "较易"}, "knowledge": [{"code": "14466475499", "name": "余角和补角"}], "updateTime": 1582524434, "year": {"isDeprecated": "0", "code": "2019", "isReplace": "0", "name": "2019年", "isHide": "0"}, "questionStatus": {"code": "1", "name": "启用"}, "isCollected": 0, "isAddedToBox": 0, "source": 1, "canCollect": true}, "children": [], "layeredLevelCode": null, "layeredLevelName": null, "showTypeScore": null, "perQuesScore": null, "count": 0}, {"bigExamQuestionId": null, "segmentId": null, "parentId": null, "parentIds": null, "bigExamQuestionName": null, "desc": null, "sort": 10, "questionId": "297c99c8-2212-4151-8305-6421de28a8ad", "score": 0.0, "type": 2, "questionInfo": {"explain": [], "questionId": "297c99c8-2212-4151-8305-6421de28a8ad", "subjective": {"code": false, "name": "客观题"}, "children": [], "grade": {"code": "1217", "name": "七年级"}, "context": {"stem": "如果一个角的补角是<span class=\"math-tex\"><img class=\"math-tex\"  style=\"vertical-align:-1.98px\" src=\"http://cp-cloud-files-tiku.oss-cn-hangzhou.aliyuncs.com/latex2svg/1212/ea891026f238c4483986bf4efffa1ac9.svg\" /></span>，那么这个角的余角是（<span class=\"brack\"></span>）", "audio": null, "original_text": null, "options": ["<span class=\"math-tex\"><img class=\"math-tex\"  style=\"vertical-align:-1.98px\" src=\"http://cp-cloud-files-tiku.oss-cn-hangzhou.aliyuncs.com/latex2svg/1212/021924c0f6a483b67a498c027ad1a005.svg\" /></span>", "<span class=\"math-tex\"><img class=\"math-tex\"  style=\"vertical-align:-1.98px\" src=\"http://cp-cloud-files-tiku.oss-cn-hangzhou.aliyuncs.com/latex2svg/1212/cbb05423d43193dc7a8067a59d4bd316.svg\" /></span>", "<span class=\"math-tex\"><img class=\"math-tex\"  style=\"vertical-align:-1.98px\" src=\"http://cp-cloud-files-tiku.oss-cn-hangzhou.aliyuncs.com/latex2svg/1212/23829213233f0fa5d36e06b1c80e5db9.svg\" /></span>", "<span class=\"math-tex\"><img class=\"math-tex\"  style=\"vertical-align:-1.98px\" src=\"http://cp-cloud-files-tiku.oss-cn-hangzhou.aliyuncs.com/latex2svg/1212/6ca6317dd2a458af42244417c133698f.svg\" /></span>"]}, "useCount": 0, "quesStruct": {"code": "11", "name": "单选模板"}, "quesType": {"code": "11", "name": "单选题"}, "difficulty": {"code": "2", "name": "较易"}, "knowledge": [{"code": "14466475499", "name": "余角和补角"}, {"code": "14466475497", "name": "角的计算"}], "updateTime": 1662542590, "year": {"isDeprecated": "0", "code": "2018", "isReplace": "0", "name": "2018年", "isHide": "0"}, "questionStatus": {"code": "1", "name": "启用"}, "isCollected": 0, "isAddedToBox": 0, "source": 1, "canCollect": true}, "children": [], "layeredLevelCode": null, "layeredLevelName": null, "showTypeScore": null, "perQuesScore": null, "count": 0}, {"bigExamQuestionId": null, "segmentId": null, "parentId": null, "parentIds": null, "bigExamQuestionName": null, "desc": null, "sort": 11, "questionId": "d9d7943a-8214-4bac-86c7-2e40fdf29a12", "score": 0.0, "type": 2, "questionInfo": {"explain": [], "questionId": "d9d7943a-8214-4bac-86c7-2e40fdf29a12", "subjective": {"code": false, "name": "客观题"}, "children": [], "grade": {"code": "1217", "name": "七年级"}, "context": {"stem": "如图所示，已知点<img class=\"math-tex\"  style=\"vertical-align:-1.01px\" src=\"http://cp-cloud-files-tiku.oss-cn-hangzhou.aliyuncs.com/latex2svg/1212/7fc56270e7a70fa81a5935b72eacbe29.svg\" />，<img class=\"math-tex\"  style=\"vertical-align:-1.98px\" src=\"http://cp-cloud-files-tiku.oss-cn-hangzhou.aliyuncs.com/latex2svg/1212/f186217753c37b9b9f958d906208506e.svg\" />，<img class=\"math-tex\"  style=\"vertical-align:-1.01px\" src=\"http://cp-cloud-files-tiku.oss-cn-hangzhou.aliyuncs.com/latex2svg/1212/9d5ed678fe57bcca610140957afab571.svg\" />在同一条直线上，且<img class=\"math-tex\"  style=\"vertical-align:-1.98px\" src=\"http://cp-cloud-files-tiku.oss-cn-hangzhou.aliyuncs.com/latex2svg/1212/861c15b1d3a8fd4c464317a52b2d80ae.svg\" />，则<img class=\"math-tex\"  style=\"vertical-align:-1.98px\" src=\"http://cp-cloud-files-tiku.oss-cn-hangzhou.aliyuncs.com/latex2svg/1212/022b885425912a88e3e47301046ecf7c.svg\" />的余角有（<span class='brack'></span>）\n\n</br><img src='http://qp-tiku.oss-cn-beijing.aliyuncs.com/ImageFile/cf11d37caaa311e892ee00163e2ea669/6b24ea97a5f3d278bdd76e040223b6a2.png'/>", "audio": null, "original_text": null, "options": ["<img class=\"math-tex\"  style=\"vertical-align:-1.01px\" src=\"http://cp-cloud-files-tiku.oss-cn-hangzhou.aliyuncs.com/latex2svg/1212/c4ca4238a0b923820dcc509a6f75849b.svg\" />个", "<img class=\"math-tex\"  style=\"vertical-align:-1.01px\" src=\"http://cp-cloud-files-tiku.oss-cn-hangzhou.aliyuncs.com/latex2svg/1212/c81e728d9d4c2f636f067f89cc14862c.svg\" />个", "<img class=\"math-tex\"  style=\"vertical-align:-1.98px\" src=\"http://cp-cloud-files-tiku.oss-cn-hangzhou.aliyuncs.com/latex2svg/1212/eccbc87e4b5ce2fe28308fd9f2a7baf3.svg\" />个", "<img class=\"math-tex\"  style=\"vertical-align:-1.01px\" src=\"http://cp-cloud-files-tiku.oss-cn-hangzhou.aliyuncs.com/latex2svg/1212/a87ff679a2f3e71d9181a67b7542122c.svg\" />个"]}, "useCount": 0, "quesStruct": {"code": "11", "name": "单选模板"}, "quesType": {"code": "11", "name": "单选题"}, "difficulty": {"code": "2", "name": "较易"}, "knowledge": [{"code": "14466475499", "name": "余角和补角"}], "updateTime": 1582524434, "year": {"isDeprecated": "0", "code": "2019", "isReplace": "0", "name": "2019年", "isHide": "0"}, "questionStatus": {"code": "1", "name": "启用"}, "isCollected": 0, "isAddedToBox": 0, "source": 1, "canCollect": true}, "children": [], "layeredLevelCode": null, "layeredLevelName": null, "showTypeScore": null, "perQuesScore": null, "count": 0}, {"bigExamQuestionId": null, "segmentId": null, "parentId": null, "parentIds": null, "bigExamQuestionName": null, "desc": null, "sort": 12, "questionId": "e04ebe05-53d1-4ce5-9a6c-19219a611398", "score": 0.0, "type": 2, "questionInfo": {"explain": [], "questionId": "e04ebe05-53d1-4ce5-9a6c-19219a611398", "subjective": {"code": false, "name": "客观题"}, "children": [], "grade": {"code": "1217", "name": "七年级"}, "context": {"stem": "一副三角尺按如图的方式摆放，且<span class=\"math-tex\"><img class=\"math-tex\"  style=\"vertical-align:-1.01px\" src=\"http://cp-cloud-files-tiku.oss-cn-hangzhou.aliyuncs.com/latex2svg/1212/326ed78701f2b5816bbe9788cc858f6b.svg\" /></span>比<span class=\"math-tex\"><img class=\"math-tex\"  style=\"vertical-align:-1.01px\" src=\"http://cp-cloud-files-tiku.oss-cn-hangzhou.aliyuncs.com/latex2svg/1212/1d6093d2c231bf4605adf938f6139e0b.svg\" /></span>大<span class=\"math-tex\"><img class=\"math-tex\"  style=\"vertical-align:-1.98px\" src=\"http://cp-cloud-files-tiku.oss-cn-hangzhou.aliyuncs.com/latex2svg/1212/1b3c855f5251e6e24f45ec76ff6d7fa1.svg\" /></span>，则<span class=\"math-tex\"><img class=\"math-tex\"  style=\"vertical-align:-1.01px\" src=\"http://cp-cloud-files-tiku.oss-cn-hangzhou.aliyuncs.com/latex2svg/1212/326ed78701f2b5816bbe9788cc858f6b.svg\" /></span>的度数是（<span class=\"brack\"></span>）\n<br><img src=\"http://qp-tiku.oss-cn-beijing.aliyuncs.com/ImageFile/cf11d37caaa311e892ee00163e2ea669/a03e9888a0aae8ad5e79a89e425db636.png\">\n", "audio": null, "original_text": null, "options": ["<span class=\"math-tex\"><img class=\"math-tex\"  style=\"vertical-align:-1.98px\" src=\"http://cp-cloud-files-tiku.oss-cn-hangzhou.aliyuncs.com/latex2svg/1212/abf3f08d9175c23c8bff782ce4b97b32.svg\" /></span>", "<span class=\"math-tex\"><img class=\"math-tex\"  style=\"vertical-align:-1.98px\" src=\"http://cp-cloud-files-tiku.oss-cn-hangzhou.aliyuncs.com/latex2svg/1212/1b3c855f5251e6e24f45ec76ff6d7fa1.svg\" /></span>", "<span class=\"math-tex\"><img class=\"math-tex\"  style=\"vertical-align:-1.98px\" src=\"http://cp-cloud-files-tiku.oss-cn-hangzhou.aliyuncs.com/latex2svg/1212/e0ee195a676b143ee37728ddbc136a8c.svg\" /></span>", "<span class=\"math-tex\"><img class=\"math-tex\"  style=\"vertical-align:-1.98px\" src=\"http://cp-cloud-files-tiku.oss-cn-hangzhou.aliyuncs.com/latex2svg/1212/10feb55c74e48673981228514d2d263e.svg\" /></span>"]}, "useCount": 0, "quesStruct": {"code": "11", "name": "单选模板"}, "quesType": {"code": "11", "name": "单选题"}, "difficulty": {"code": "3", "name": "中等"}, "knowledge": [{"code": "14466475499", "name": "余角和补角"}], "updateTime": 1609936587, "year": {"isDeprecated": "0", "code": "2019", "isReplace": "0", "name": "2019年", "isHide": "0"}, "questionStatus": {"code": "1", "name": "启用"}, "isCollected": 0, "isAddedToBox": 0, "source": 1, "canCollect": true}, "children": [], "layeredLevelCode": null, "layeredLevelName": null, "showTypeScore": null, "perQuesScore": null, "count": 0}, {"bigExamQuestionId": null, "segmentId": null, "parentId": null, "parentIds": null, "bigExamQuestionName": null, "desc": null, "sort": 13, "questionId": "c8b9fa2c-f092-4bbd-a49b-9a185624d3ef", "score": 0.0, "type": 2, "questionInfo": {"explain": [], "questionId": "c8b9fa2c-f092-4bbd-a49b-9a185624d3ef", "subjective": {"code": false, "name": "客观题"}, "children": [], "grade": {"code": "1217", "name": "七年级"}, "context": {"stem": "下列说法中，正确的有（<span class=\"brack\"></span>）\n\n<br/>①在同一平面内,不相交的两条线段必平行;\n\n<br/>②在同一平面内,不相交的两条直线必平行;\n\n<br/>③在同一平面内,不平行的两条线段必相交;\n\n<br/>④在同一平面内,不平行的两条直线必相交.", "audio": null, "original_text": null, "options": ["<img class=\"math-tex\"  style=\"vertical-align:-1.01px\" src=\"http://cp-cloud-files-tiku.oss-cn-hangzhou.aliyuncs.com/latex2svg/1212/c4ca4238a0b923820dcc509a6f75849b.svg\" />个", "<img class=\"math-tex\"  style=\"vertical-align:-1.01px\" src=\"http://cp-cloud-files-tiku.oss-cn-hangzhou.aliyuncs.com/latex2svg/1212/c81e728d9d4c2f636f067f89cc14862c.svg\" />个", "<img class=\"math-tex\"  style=\"vertical-align:-1.98px\" src=\"http://cp-cloud-files-tiku.oss-cn-hangzhou.aliyuncs.com/latex2svg/1212/eccbc87e4b5ce2fe28308fd9f2a7baf3.svg\" />个", "<img class=\"math-tex\"  style=\"vertical-align:-1.01px\" src=\"http://cp-cloud-files-tiku.oss-cn-hangzhou.aliyuncs.com/latex2svg/1212/a87ff679a2f3e71d9181a67b7542122c.svg\" />个"]}, "useCount": 0, "quesStruct": {"code": "11", "name": "单选模板"}, "quesType": {"code": "11", "name": "单选题"}, "difficulty": {"code": "2", "name": "较易"}, "knowledge": [{"code": "14466474485", "name": "线段基本事实-两点之间线段最短"}, {"code": "14467502504", "name": "两条直线的位置关系"}], "updateTime": 1615962820, "year": {"isDeprecated": "0", "code": "2019", "isReplace": "0", "name": "2019年", "isHide": "0"}, "questionStatus": {"code": "1", "name": "启用"}, "isCollected": 0, "isAddedToBox": 0, "source": 1, "canCollect": true}, "children": [], "layeredLevelCode": null, "layeredLevelName": null, "showTypeScore": null, "perQuesScore": null, "count": 0}, {"bigExamQuestionId": null, "segmentId": null, "parentId": null, "parentIds": null, "bigExamQuestionName": null, "desc": null, "sort": 14, "questionId": "b992806c-add1-490d-8f36-821a4ac46592", "score": 0.0, "type": 2, "questionInfo": {"explain": [], "questionId": "b992806c-add1-490d-8f36-821a4ac46592", "subjective": {"code": false, "name": "客观题"}, "children": [], "grade": {"code": "1218", "name": "八年级"}, "context": {"stem": "同一平面内有两两相交的三条直线，若最多有<img class=\"math-tex\"  style=\"vertical-align:-1.98px\" src=\"http://cp-cloud-files-tiku.oss-cn-hangzhou.aliyuncs.com/latex2svg/1212/6f8f57715090da2632453988d9a1501b.svg\" />个交点，最少有<img class=\"math-tex\"  style=\"vertical-align:-1.98px\" src=\"http://cp-cloud-files-tiku.oss-cn-hangzhou.aliyuncs.com/latex2svg/1212/7b8b965ad4bca0e41ab51de7b31363a1.svg\" />个交点，则<img class=\"math-tex\"  style=\"vertical-align:-2.96px\" src=\"http://cp-cloud-files-tiku.oss-cn-hangzhou.aliyuncs.com/latex2svg/1212/cf1ade4c34301c43d30481ed5d6c52c1.svg\" />等于（<span class=\"brack\"></span>）", "audio": null, "original_text": null, "options": ["<img class=\"math-tex\"  style=\"vertical-align:-1.01px\" src=\"http://cp-cloud-files-tiku.oss-cn-hangzhou.aliyuncs.com/latex2svg/1212/c4ca4238a0b923820dcc509a6f75849b.svg\" />", "<img class=\"math-tex\"  style=\"vertical-align:-1.01px\" src=\"http://cp-cloud-files-tiku.oss-cn-hangzhou.aliyuncs.com/latex2svg/1212/c81e728d9d4c2f636f067f89cc14862c.svg\" />", "<img class=\"math-tex\"  style=\"vertical-align:-1.98px\" src=\"http://cp-cloud-files-tiku.oss-cn-hangzhou.aliyuncs.com/latex2svg/1212/eccbc87e4b5ce2fe28308fd9f2a7baf3.svg\" />", "<img class=\"math-tex\"  style=\"vertical-align:-1.01px\" src=\"http://cp-cloud-files-tiku.oss-cn-hangzhou.aliyuncs.com/latex2svg/1212/a87ff679a2f3e71d9181a67b7542122c.svg\" />"]}, "useCount": 0, "quesStruct": {"code": "11", "name": "单选模板"}, "quesType": {"code": "11", "name": "单选题"}, "difficulty": {"code": "2", "name": "较易"}, "knowledge": [{"code": "13368382399", "name": "两直线相交问题"}], "updateTime": 1615962820, "year": {"isDeprecated": "0", "code": "2018", "isReplace": "0", "name": "2018年", "isHide": "0"}, "questionStatus": {"code": "1", "name": "启用"}, "isCollected": 0, "isAddedToBox": 0, "source": 1, "canCollect": true}, "children": [], "layeredLevelCode": null, "layeredLevelName": null, "showTypeScore": null, "perQuesScore": null, "count": 0}, {"bigExamQuestionId": null, "segmentId": null, "parentId": null, "parentIds": null, "bigExamQuestionName": null, "desc": null, "sort": 15, "questionId": "eedb5cb3-5f93-4588-920b-b8b9f6a9b857", "score": 0.0, "type": 2, "questionInfo": {"explain": [], "questionId": "eedb5cb3-5f93-4588-920b-b8b9f6a9b857", "subjective": {"code": true, "name": "主观题"}, "children": [], "grade": {"code": "1217", "name": "七年级"}, "context": {"stem": "在同一平面内，两条直线的位置关系有<span class=\"fill\">&#8203;</span>和<span class=\"fill\">&#8203;</span>．", "audio": null, "original_text": null, "options": null}, "useCount": 1, "quesStruct": {"code": "13", "name": "填空模板"}, "quesType": {"code": "13", "name": "填空题"}, "difficulty": {"code": "2", "name": "较易"}, "knowledge": [{"code": "14467502504", "name": "两条直线的位置关系"}], "updateTime": 1582524434, "year": {"isDeprecated": "0", "code": "2019", "isReplace": "0", "name": "2019年", "isHide": "0"}, "questionStatus": {"code": "1", "name": "启用"}, "isCollected": 0, "isAddedToBox": 0, "source": 1, "canCollect": true}, "children": [], "layeredLevelCode": null, "layeredLevelName": null, "showTypeScore": null, "perQuesScore": null, "count": 0}, {"bigExamQuestionId": null, "segmentId": null, "parentId": null, "parentIds": null, "bigExamQuestionName": null, "desc": null, "sort": 16, "questionId": "11976d65-4494-4a36-995c-495b8ef82c20", "score": 0.0, "type": 2, "questionInfo": {"explain": [], "questionId": "11976d65-4494-4a36-995c-495b8ef82c20", "subjective": {"code": false, "name": "客观题"}, "children": [], "grade": {"code": "1217", "name": "七年级"}, "context": {"stem": "下列图形中，<span class=\"math-tex\"><img class=\"math-tex\"  style=\"vertical-align:-1.01px\" src=\"http://cp-cloud-files-tiku.oss-cn-hangzhou.aliyuncs.com/latex2svg/1212/326ed78701f2b5816bbe9788cc858f6b.svg\" /></span>和<span class=\"math-tex\"><img class=\"math-tex\"  style=\"vertical-align:-1.01px\" src=\"http://cp-cloud-files-tiku.oss-cn-hangzhou.aliyuncs.com/latex2svg/1212/1d6093d2c231bf4605adf938f6139e0b.svg\" /></span>是对顶角的是（<span class=\"brack\"></span>）", "audio": null, "original_text": null, "options": ["<img src=\"http://qp-tiku.oss-cn-beijing.aliyuncs.com/ImageFile/cf11d37caaa311e892ee00163e2ea669/bf8e352ec64f19fc1df5b7d0f2817529.png\" />", "<img src=\"http://qp-tiku.oss-cn-beijing.aliyuncs.com/ImageFile/cf11d37caaa311e892ee00163e2ea669/519db29f9adbef000e4f1688c8b178c1.png\" />", "<img src=\"http://qp-tiku.oss-cn-beijing.aliyuncs.com/ImageFile/cf11d37caaa311e892ee00163e2ea669/5b2476bde043c714a300d1a2a03efd06.png\" />", "<img src=\"http://qp-tiku.oss-cn-beijing.aliyuncs.com/ImageFile/cf11d37caaa311e892ee00163e2ea669/bac0ed9e1075859cb3f44adf973e5e02.png\" />"]}, "useCount": 1, "quesStruct": {"code": "11", "name": "单选模板"}, "quesType": {"code": "11", "name": "单选题"}, "difficulty": {"code": "2", "name": "较易"}, "knowledge": [{"code": "14467502505", "name": "对顶角、邻补角概念"}], "updateTime": 1652866013, "year": {"isDeprecated": "0", "code": "2019", "isReplace": "0", "name": "2019年", "isHide": "0"}, "questionStatus": {"code": "1", "name": "启用"}, "isCollected": 0, "isAddedToBox": 0, "source": 1, "canCollect": true}, "children": [], "layeredLevelCode": null, "layeredLevelName": null, "showTypeScore": null, "perQuesScore": null, "count": 0}, {"bigExamQuestionId": null, "segmentId": null, "parentId": null, "parentIds": null, "bigExamQuestionName": null, "desc": null, "sort": 17, "questionId": "398029e0-cd97-4157-bf4b-0fe506249765", "score": 0.0, "type": 2, "questionInfo": {"explain": [], "questionId": "398029e0-cd97-4157-bf4b-0fe506249765", "subjective": {"code": false, "name": "客观题"}, "children": [], "grade": {"code": "1217", "name": "七年级"}, "context": {"stem": "如图，直线<img class=\"math-tex\"  style=\"vertical-align:-1.01px\" src=\"http://cp-cloud-files-tiku.oss-cn-hangzhou.aliyuncs.com/latex2svg/1212/b86fc6b051f63d73de262d4c34e3a0a9.svg\" />与<img class=\"math-tex\"  style=\"vertical-align:-1.98px\" src=\"http://cp-cloud-files-tiku.oss-cn-hangzhou.aliyuncs.com/latex2svg/1212/4170acd6af571e8d0d59fdad999cc605.svg\" />相交于点<img class=\"math-tex\"  style=\"vertical-align:-1.98px\" src=\"http://cp-cloud-files-tiku.oss-cn-hangzhou.aliyuncs.com/latex2svg/1212/f186217753c37b9b9f958d906208506e.svg\" />，若<img class=\"math-tex\"  style=\"vertical-align:-2.96px\" src=\"http://cp-cloud-files-tiku.oss-cn-hangzhou.aliyuncs.com/latex2svg/1212/19c82ee2f5c4df23ec0b85b103c5b89f.svg\" />，则<img class=\"math-tex\"  style=\"vertical-align:-1.98px\" src=\"http://cp-cloud-files-tiku.oss-cn-hangzhou.aliyuncs.com/latex2svg/1212/8025631d6c129903d4fdb89a38538b8b.svg\" />\n<br/>等于（<span class=\"brack\"></span>）\n</br><img src='http://qp-tiku.oss-cn-beijing.aliyuncs.com/ImageFile/cf11d37caaa311e892ee00163e2ea669/7d3012014d3a73327a82d67450d6c8b9.png'/>", "audio": null, "original_text": null, "options": ["<img class=\"math-tex\"  style=\"vertical-align:-1.98px\" src=\"http://cp-cloud-files-tiku.oss-cn-hangzhou.aliyuncs.com/latex2svg/1212/5352ca61516d8763c7f30f4f62a847aa.svg\" />", "<img class=\"math-tex\"  style=\"vertical-align:-1.98px\" src=\"http://cp-cloud-files-tiku.oss-cn-hangzhou.aliyuncs.com/latex2svg/1212/ea891026f238c4483986bf4efffa1ac9.svg\" />", "<img class=\"math-tex\"  style=\"vertical-align:-1.98px\" src=\"http://cp-cloud-files-tiku.oss-cn-hangzhou.aliyuncs.com/latex2svg/1212/df5486be3ddef9a81aa3f1e0aa7b2052.svg\" />", "<img class=\"math-tex\"  style=\"vertical-align:-1.98px\" src=\"http://cp-cloud-files-tiku.oss-cn-hangzhou.aliyuncs.com/latex2svg/1212/fdfbec871cb947c722e029c54fd9c3ab.svg\" />"]}, "useCount": 0, "quesStruct": {"code": "11", "name": "单选模板"}, "quesType": {"code": "11", "name": "单选题"}, "difficulty": {"code": "2", "name": "较易"}, "knowledge": [{"code": "14467502506", "name": "对顶角性质"}, {"code": "14467502507", "name": "邻补角性质"}], "updateTime": 1615962820, "year": {"isDeprecated": "0", "code": "2019", "isReplace": "0", "name": "2019年", "isHide": "0"}, "questionStatus": {"code": "1", "name": "启用"}, "isCollected": 0, "isAddedToBox": 0, "source": 1, "canCollect": true}, "children": [], "layeredLevelCode": null, "layeredLevelName": null, "showTypeScore": null, "perQuesScore": null, "count": 0}, {"bigExamQuestionId": null, "segmentId": null, "parentId": null, "parentIds": null, "bigExamQuestionName": null, "desc": null, "sort": 18, "questionId": "1689174f-47a9-49de-b70c-c7da2f250b7e", "score": 0.0, "type": 2, "questionInfo": {"explain": [], "questionId": "1689174f-47a9-49de-b70c-c7da2f250b7e", "subjective": {"code": true, "name": "主观题"}, "children": [], "grade": {"code": "1217", "name": "七年级"}, "context": {"stem": "用剪刀剪东西时，剪刀张开的角度如图所示．若<img class=\"math-tex\"  style=\"vertical-align:-1.98px\" src=\"http://cp-cloud-files-tiku.oss-cn-hangzhou.aliyuncs.com/latex2svg/1212/b7ff79d0329c66bdada75d14f76c4373.svg\" />，则<img class=\"math-tex\"  style=\"vertical-align:-1.01px\" src=\"http://cp-cloud-files-tiku.oss-cn-hangzhou.aliyuncs.com/latex2svg/1212/452075f6d79d07b274b7f540cc9a84a3.svg\" /><span class=\"fill\">&#8203;</span>度．\n<br/>\n<br/>\n<img src=\"http://qp-tiku.oss-cn-beijing.aliyuncs.com/media%2Fquestion%2F20%2F2922c1bee923b2a27ecc4ed1b0a6a36d.png\">", "audio": null, "original_text": null, "options": null}, "useCount": 0, "quesStruct": {"code": "13", "name": "填空模板"}, "quesType": {"code": "13", "name": "填空题"}, "difficulty": {"code": "2", "name": "较易"}, "knowledge": [{"code": "14467502506", "name": "对顶角性质"}], "updateTime": 1582524434, "year": {"isDeprecated": "0", "code": "2019", "isReplace": "0", "name": "2019年", "isHide": "0"}, "questionStatus": {"code": "1", "name": "启用"}, "isCollected": 0, "isAddedToBox": 0, "source": 1, "canCollect": true}, "children": [], "layeredLevelCode": null, "layeredLevelName": null, "showTypeScore": null, "perQuesScore": null, "count": 0}], "layeredLevelCode": null, "layeredLevelName": null, "showTypeScore": false, "perQuesScore": null, "count": 19}]}], "schoolId": null, "year": "2019", "layeredLevelCode": null}}