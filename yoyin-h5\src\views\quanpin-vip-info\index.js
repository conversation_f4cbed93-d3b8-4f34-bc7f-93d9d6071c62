import { Vue } from 'js/base'
import App from './index.vue'

// import VueI18n from 'vue-i18n'
// import zhCN from '../../locales/zh_CN'
// import enUS from '../../locales/en_US'
// Vue.use(VueI18n) // 通过插件的形式挂载
// const i18n = new VueI18n({
//   locale: 'zh_CN', // 语言标识
//   messages: {
//     'zh_CN': zhCN, // 中文语言包
//     'en_US': enUS, // 英文语言包
//     silentTranslationWarn: true
//   }
// })
// import localesMixin from '@/mixin/locales'
// Vue.mixin(localesMixin)

import ElementUI from 'element-ui'
Vue.use(ElementUI)

new Vue({
  // i18n, // 不要忘记
  render: h => h(App)
}).$mount('#app')
