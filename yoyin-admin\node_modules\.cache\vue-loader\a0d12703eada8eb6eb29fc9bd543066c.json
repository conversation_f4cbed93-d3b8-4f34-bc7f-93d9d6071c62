{"remainingRequest": "D:\\workspace\\xplife\\yoyin-ui\\yoyin-admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\workspace\\xplife\\yoyin-ui\\yoyin-admin\\src\\views\\office\\material\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\workspace\\xplife\\yoyin-ui\\yoyin-admin\\src\\views\\office\\material\\index.vue", "mtime": 1751274032261}, {"path": "D:\\workspace\\xplife\\yoyin-ui\\yoyin-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\workspace\\xplife\\yoyin-ui\\yoyin-admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\workspace\\xplife\\yoyin-ui\\yoyin-admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\workspace\\xplife\\yoyin-ui\\yoyin-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\workspace\\xplife\\yoyin-ui\\yoyin-admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\r\nimport {\r\n  fetchOfficeMaterialPageList,\r\n  fetchOfficeMaterialDetail\r\n} from '@/api/office/material'\r\nimport { fetchDictList } from '@/api/system/dict'\r\nimport Card from './component/Card/index'\r\nimport CommonForm from './component/CommonForm/index'\r\nimport ToDoListForm from './component/ToDoListForm/index'\r\nimport TextPopForm from './component/TextPopForm/index'\r\nimport FontForm from './component/FontForm/index'\r\n\r\nexport default {\r\n  components: {\r\n    Card,\r\n    CommonForm,\r\n    ToDoListForm,\r\n    TextPopForm,\r\n    FontForm\r\n  },\r\n  data() {\r\n    return {\r\n      list: [],\r\n      industryList: [],\r\n      page: {\r\n        size: 20,\r\n        no: 1,\r\n        total: 0\r\n      },\r\n      showAdd: false,\r\n      currentView: 'CommonForm',\r\n      currentForm: {},\r\n      currentTitile: '办公素材编辑',\r\n      currentId: '',\r\n      currentName: ''\r\n    }\r\n  },\r\n  async mounted() {\r\n    // 先获取行业数据，然后查询列表\r\n    await this.fetchIndustryData()\r\n    this.fetchList()\r\n  },\r\n  methods: {\r\n    handleAdd() {\r\n      this.industryList.forEach(m => {\r\n        if (m.code === this.currentId) {\r\n          this.currentName = m.name\r\n          this.currentTitile = m.name\r\n        }\r\n      })\r\n      this.currentForm = {\r\n        mId: this.currentId\r\n      }\r\n      this.currentTitile = this.currentTitile + '新增'\r\n      this.currentView = 'CommonForm'\r\n      this.showAdd = true\r\n    },\r\n    notifyUpdate() {\r\n      this.fetchList()\r\n    },\r\n    async notifyEdit(item) {\r\n      await this.getDetail(item.id)\r\n    },\r\n    async getDetail(id) {\r\n      try {\r\n        const res = await fetchOfficeMaterialDetail({ id })\r\n        if (res.head && res.head.ret === 0) {\r\n          this.currentForm = res.data\r\n          this.currentForm.id = id\r\n          this.industryList.forEach(m => {\r\n            if (m.code === this.currentId) {\r\n              this.currentName = m.name\r\n              this.currentTitile = m.name\r\n            }\r\n          })\r\n          this.currentTitile = this.currentTitile + '编辑'\r\n          this.currentView = 'CommonForm'\r\n          this.showAdd = true\r\n        }\r\n      } catch (error) {\r\n        console.error('获取办公素材详情失败:', error)\r\n      }\r\n    },\r\n    handleBack(val) {\r\n      console.log('handleBack called with val:', val, 'type:', typeof val)\r\n      this.currentForm = {}\r\n      this.showAdd = false\r\n      if (val === '1') {\r\n        console.log('新增/编辑成功，开始刷新列表...')\r\n        // 重置到第一页，确保能看到新增的数据\r\n        this.page.no = 1\r\n        this.fetchList()\r\n      }\r\n    },\r\n    handleCurrentChange(val) {\r\n      this.page.no = val\r\n      this.fetchList()\r\n    },\r\n    handleSizeChange(val) {\r\n      this.page.size = val\r\n      this.fetchList()\r\n    },\r\n\r\n    handleMaterialChange() {\r\n      this.page = {\r\n        size: 20,\r\n        no: 1,\r\n        total: 0\r\n      }\r\n      this.industryList.forEach(m => {\r\n        if (m.code === this.currentId) {\r\n          this.currentName = m.name\r\n          this.currentTitile = m.name\r\n        }\r\n      })\r\n      this.fetchList()\r\n    },\r\n    async fetchList() {\r\n      try {\r\n        const params = {\r\n          pageno: this.page.no,\r\n          pagesize: this.page.size\r\n        }\r\n        if (this.currentId) {\r\n          params.category = this.currentId\r\n        }\r\n\r\n        console.log('fetchList 请求参数:', params)\r\n        const res = await fetchOfficeMaterialPageList(params)\r\n        console.log('fetchList 响应结果:', res)\r\n        if (res.head && res.head.ret === 0) {\r\n          this.list = (res.data && res.data.result) || []\r\n          this.page.total = (res.data && res.data.totalCount) || 0\r\n          console.log('列表更新成功，当前数据量:', this.list.length, '总数:', this.page.total)\r\n        }\r\n      } catch (error) {\r\n        console.error('获取办公素材列表失败:', error)\r\n      }\r\n    },\r\n\r\n    async fetchIndustryData() {\r\n      try {\r\n        const res = await fetchDictList({ type: 'industry' })\r\n        if (res.head && res.head.ret === 0) {\r\n          this.industryList = res.data || []\r\n          // 默认选中\"全部\"\r\n          this.currentId = ''\r\n        }\r\n      } catch (error) {\r\n        console.error('获取行业列表失败:', error)\r\n      }\r\n    }\r\n  }\r\n}\r\n", null]}