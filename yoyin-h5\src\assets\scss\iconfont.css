
@font-face {font-family: "iconfont";
  src: url('../font/iconfont.eot?t=1496590936907'); /* IE9*/
  src: url('../font/iconfont.eot?t=1496590936907#iefix') format('embedded-opentype'), /* IE6-IE8 */
  url('../font/iconfont.woff?t=1496590936907') format('woff'), /* chrome, firefox */
  url('../font/iconfont.ttf?t=1496590936907') format('truetype'), /* chrome, firefox, opera, Safari, Android, iOS 4.2+*/
  url('../font/iconfont.svg?t=1496590936907#iconfont') format('svg'); /* iOS 4.1- */
}

.iconfont {
  font-family:"iconfont" !important;
  font-size:16px;
  font-style:normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
.icon-birthday:before { content: "\e603"; }

