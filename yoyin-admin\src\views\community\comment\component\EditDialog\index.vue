<template>
  <el-form ref="form" :model="form" :rules="rules" label-width="100px" class="edit-form">
    <el-form-item label="评论人：" prop="codeid">
      <el-select
        v-model="form.codeid"
        filterable
        remote
        reserve-keyword
        placeholder="请输入关键词"
        :remote-method="queryUserList"
        :loading="loading"
      >
        <el-option
          v-for="item in userQueryList"
          :key="item.id"
          :label="item.nickName"
          :value="item.codeId"
        />
      </el-select>
      <el-button type="primary" @click="randonSelectUser">随机</el-button>
    </el-form-item>
    <el-form-item label="动态ID：" prop="feedid">
      <el-input v-model="form.feedid" placeholder="请输入动态ID" />
    </el-form-item>
    <el-form-item label="内容：" prop="content">
      <el-input v-model="form.content" type="textarea" :rows="4" placeholder="请输入评论" />
    </el-form-item>

    <el-form-item>
      <el-button @click="handleClose">返回</el-button>
      <el-button type="primary" @click="submit">保存</el-button>
    </el-form-item>
  </el-form>
</template>

<script>
import { saveRecord } from '@/api/community/comment'
import { getRandomUser, fetchListByPage } from '@/api/user/list'
export default {
  name: 'EditDialog',
  props: {
    feedId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      /** 表单 */
      form: {
        codeid: '',
        feedid: this.feedId,
        content: ''
      },
      userQueryList: [],
      loading: false,
      /** 校验规则 */
      rules: {
        codeid: [
          { required: true, message: '请输入评论人ID', trigger: 'blur' }
        ],
        feedid: [
          { required: true, message: '请输入动态ID', trigger: 'blur' }
        ],
        content: [
          { required: true, message: '请输入内容', trigger: 'blur' },
          { min: 1, max: 200, message: '长度在 1 到 200 个字符', trigger: 'blur' }
        ]

      }
    }
  },
  mounted() {
  },
  methods: {
    /** 返回 */
    handleClose(code) {
      this.$emit('closeDialog', code)
    },
    queryUserList(e) {
      const params = {
        pageNo: 1,
        pageSize: 12,
        sort: 'desc',
        sortType: 'time',
        codeId: e
      }
      fetchListByPage(params).then(res => {
        if (res.head.ret === 0 && res.data.result.length > 0) {
          this.userQueryList = res.data.result
        } else {
          delete params['codeId']
          params['nickName'] = e
          fetchListByPage(params).then(res2 => {
            if (res2.head.ret === 0) {
              this.userQueryList = res2.data.result
            }
          })
        }
      })
    },
    randonSelectUser() {
      getRandomUser().then(res => {
        this.userQueryList = []
        this.userQueryList.push(res.data.userInfoDto)
        this.form.codeid = res.data.userInfoDto.codeId
      })
    },
    /** 提交 */
    submit() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          this.addUpdateRecord()
        } else {
          console.log('submit error')
          return -1
        }
      })
    },
    /** 保存 */
    async addUpdateRecord() {
      const res = await saveRecord(this.form)
      if (res.head.ret === 0) {
        this.handleClose('1')
        this.$message.success('保存成功')
      } else {
        this.$message.error(res.head.msg)
      }
    }
  }

}
</script>

<style lang='scss' scoped>
.label-span{
  line-height: 30px;
  height: 30px;
  border-radius: 5px;
  margin-right: 10px;
  cursor: pointer;
}
.check-label-span{
  background: #409EFF;
  padding: 5px;
}
 .avatar-uploader .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }
  .avatar-uploader .el-upload:hover {
    border-color: #409EFF;
  }
  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 178px;
    height: 178px;
    line-height: 178px;
    text-align: center;
    border: 1px solid gray;
  }
  .avatar {
    width: 178px;
    height: 178px;
    display: block;
  }
</style>
