<template>
  <div v-if="visiable" class="edit-dialog">
    <el-dialog
      ref="dialog"
      :show-close="false"
      :title="form.id === '' ? '新增标签' : '编辑标签' "
      :visible.sync="visiable"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      width="50%"
      center
    >
      <el-form ref="form" :model="form" :rules="rules" label-width="100px" class="edit-form">
        <el-form-item label="标题" prop="msgTitle">
          <el-input v-model="form.msgTitle" />
        </el-form-item>
        <el-form-item label="消息内容" prop="msgContent">
          <el-input v-model="form.msgContent" type="textarea" :autosize="{ minRows:2, maxRows: 5}" />
        </el-form-item>
        <el-form-item label="类型" prop="msgSubType">
          <el-radio-group v-model="form.msgSubType">
            <el-radio-button label="100">无跳转</el-radio-button>
            <el-radio-button label="101">H5</el-radio-button>
            <el-radio-button label="105">原生</el-radio-button>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="开始展示的版本号" prop="version">
          <el-input
            v-model="form.version"
            type="text"
            maxlength="200"
            placeholder="请输入格式x.x.x"
          />
          {主版本号} + "." + {版本小号} + "." + {修复版本号} 例如:  3.3.1
        </el-form-item>
        <el-form-item v-if="form.msgSubType === 101 || form.msgSubType === '101' " label="链接" prop="jumpVal">
          <el-input v-model="form.jumpVal" />
        </el-form-item>

        <el-form-item label="起始时间" prop="startDate">
          <el-date-picker
            v-model="form.startDate"
            :value-format="timeFormat"
            type="datetime"
            placeholder="选择日期时间"
          />
        </el-form-item>

        <el-form-item label="结束时间" prop="endDate">
          <el-date-picker
            v-model="form.endDate"
            type="datetime"
            :value-format="timeFormat"
            placeholder="选择日期时间"
          />
        </el-form-item>

        <el-form-item v-if="form.msgSubType === 105 || form.msgSubType === '105' " label="andriod调用参数" prop="paramAndroid">
          <el-input v-model="form.paramAndroid" />
        </el-form-item>
        <el-form-item v-if="form.msgSubType === 105 || form.msgSubType === '105' " label="ios调用参数" prop="paramIos">
          <el-input v-model="form.paramIos" />
        </el-form-item>

        <el-form-item label="图片" prop="pic">
          <el-upload
            class="avatar-uploader"
            action=""
            :show-file-list="false"
            :on-change="changePic"
            :before-upload="beforeUpload"
            accept="image/png, image/jpeg"
          >
            <img v-if="form.pic" :src="form.pic" class="avatar">
            <i v-else class="el-icon-plus avatar-uploader-icon" />
          </el-upload>
          <img v-show="false" :src="testUrl" class="avatar">
        </el-form-item>

        <el-form-item>
          <el-button @click="handleClose">返回</el-button>
          <el-button type="primary" @click="submit">保存</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import { saveRecord } from '@/api/message/list'
import { parseTime } from '@/utils/index'
import AliyunOSS from '@/utils/aliyunOSS'
const oss = new AliyunOSS()

export default {
  name: 'EditDialog',
  props: {
    visiable: {
      type: Boolean,
      required: true,
      default: false
    },
    formData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      /** 表单 */
      form: {},
      /** 校验规则 */
      rules: {
        msgTitle: [
          { required: true, message: '请输入标题', trigger: 'blur' },
          { min: 3, max: 20, message: '长度在 2 到 30 个字符', trigger: 'blur' }
        ],
        msgContent: [
          { required: true, message: '请输入内容', trigger: 'blur' },
          { min: 1, max: 500, message: '长度在 2 到 500个字符', trigger: 'blur' }
        ],
        msgSubType: [
          { required: true, message: '请选择类型', trigger: 'change' }
        ],
        jumpVal: [
          { required: true, message: '请输入链接', trigger: 'blur' }
        ],

        startDate: [
          { required: true, message: '请选择时间', trigger: 'change' }
        ],
        endDate: [
          { required: true, message: '请选择时间', trigger: 'change' }
        ],
        pic: [
          { required: true, message: '请输入选择图片', trigger: 'blur' }
        ]
      },
      /** 待上传的文件 */
      upFile: undefined,
      testUrl: '',
      /** 时间格式 */
      timeFormat: 'yyyy-MM-dd HH:mm:ss'

    }
  },
  computed: {
  },
  watch: {
    formData: function(val) {
      this.form = { ...val }
    }
  },
  mounted() {
    this.show = this.visiable
  },
  methods: {
    /** 返回 */
    handleClose(code) {
      this.$emit('closeDialog', code)
    },
    /** 选择图片*/
    changePic(file) {
      this.upFile = file.raw
      this.form.pic = URL.createObjectURL(file.raw)
      this.testUrl = URL.createObjectURL(file.raw)
      return false
    },
    /** 上传前 */
    beforeUpload(file) {
      return false
    },
    /** 提交 */
    submit() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          if (this.upFile) {
            oss.uploadFile(this.upFile, (result, error) => {
              if (error) {
                this.$message.error('上传文件失败')
                return -1
              }
              if (result) {
                this.form.pic = result.url
                this.addUpdateRecord()
              }
            })
          } else {
            // 直接保存
            this.addUpdateRecord()
          }
        } else {
          return -1
        }
      })
    },
    async addUpdateRecord() {
      // 新增
      if (!this.form.id) {
        const now = parseTime(new Date())
        this.form.id = ''
        this.form.creattime = now
        this.form.msgType = 2
      }
      // 更新
      const res = await saveRecord(this.form)
      if (res.head.ret === 0) {
        this.$message.success('保存成功')
        this.handleClose('1')
      }
    }
  }

}
</script>

<style lang='scss' scoped>
  .avatar-uploader .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }
  .avatar-uploader .el-upload:hover {
    border-color: #409EFF;
  }
  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 178px;
    height: 178px;
    line-height: 178px;
    text-align: center;
  }
  .avatar {
    width: 178px;
    height: 178px;
    display: block;
  }
</style>
