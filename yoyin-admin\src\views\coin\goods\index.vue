<template>
  <div class="app-container">
    <div class="table-query-container">
      <div class="query-code-btn">
        <el-button v-permission="'btn-menuCoin-goods-edit'" size="medium" style="margin-left:20px;" @click="handleAdd">新增</el-button>
      </div>
      <div class="query-code-btn">
        <el-radio-group v-model="tabTypeId" @change="onChangeType">
          <el-radio-button label="">全部</el-radio-button>
          <el-radio-button v-for="item in tabList" :key="item.value" :label="item.value">{{ item.name }}</el-radio-button>
        </el-radio-group>
      </div>
    </div>
    <div class="table-container">
      <el-table
        ref="multipleTable"
        :data="list"
        tooltip-effect="dark"
        style="width: 100%"
        border
      >
        <el-table-column type="selection" width="55" />
        <el-table-column label="封面" width="100">
          <template slot-scope="scope">
            <a :href="scope.row.coverUrl" target="_blank">
              <el-avatar shape="square" :size="80" :src="scope.row.coverUrl" />
            </a>
          </template>
        </el-table-column>
        <el-table-column label="排序值">
          <template slot-scope="scope">{{ scope.row.sortNum }}</template>
        </el-table-column>
        <el-table-column label="商品名称">
          <template slot-scope="scope">{{ scope.row.name }}</template>
        </el-table-column>
        <el-table-column label="兑换积分">
          <template slot-scope="scope">{{ scope.row.costNum }}</template>
        </el-table-column>
        <el-table-column label="兑换限制">
          <template slot-scope="scope">{{ scope.row.changeNum }}</template>
        </el-table-column>
        <el-table-column label="库存">
          <template slot-scope="scope">{{ scope.row.stockNum }}</template>
        </el-table-column>
        <el-table-column label="商品类型">
          <template slot-scope="scope">{{ formatGoodsType(scope.row.type) }}</template>
        </el-table-column>
        <el-table-column label="是否新品">
          <template slot-scope="scope">{{ scope.row.isNews==0?'否':'是' }}</template>
        </el-table-column>
        <el-table-column label="操作">
          <template slot-scope="scope">
            <el-button size="mini" @click="handleEdit(scope.$index, scope.row)">编辑</el-button>
            <el-button
              v-permission="'btn-menuCoin-goods-edit'"
              size="mini"
              type="danger"
              @click="handleDeleteRow(scope.$index, scope.row)"
            >删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination-container" style="margin-top:20px;">
        <el-pagination
          :current-page="params.pageno"
          :page-sizes="[10, 20, 30, 40]"
          :page-size="params.pagesize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
    <div class="dialog-container">
      <EditDialog :visiable="showDialog" :form-data="goodsObj" @closeDialog="closeDialog" />
    </div>
  </div>
</template>
<script>
import { fetchGoodsListByPage, deleteGoodsRecord, getGoodsTypeTab } from '@/api/coin/goods'
import EditDialog from './component/EditDialog'
export default {
  name: 'GoodsList',
  components: {
    EditDialog
  },
  data() {
    return {
      // 查询参数
      params: {
        pageno: 1,
        pagesize: 10,
        type: ''
      },
      /** 列表 */
      list: [],
      tabList: [],
      /** 总条数 */
      total: 0,
      showDialog: false,
      goodsObj: {},
      tabTypeId: ''
    }
  },
  mounted() {
    this.getGoodsTypeList()
    this.getList()
  },
  methods: {
    onChangeType(e) {
      this.params.type = e
      this.params.pageno = 1
      this.getList()
    },
    formatGoodsType(type) {
      let val = '商品'
      this.tabList.forEach(item => {
        if (item.value === (type + '')) {
          val = item.name
        }
      })
      return val
    },
    async getGoodsTypeList() {
      const res = await getGoodsTypeTab({})
      if (res.head.ret === 0) {
        this.tabList = res.data
      }
      console.log(res)
    },
    /** 获取列表 */
    async getList() {
      const res = await fetchGoodsListByPage(this.params)
      if (res.head.ret === 0) {
        this.list = res.data.result
        this.total = res.data.totalCount
      }
    },
    /** 切换每页条数 */
    handleSizeChange(val) {
      this.params.pagesize = val
      this.getList()
    },
    /** 切换当前页 */
    handleCurrentChange(val) {
      this.params.pageno = val
      this.getList()
    },
    /** 删除单行 */
    async handleDeleteRow(index, row) {
      const params = {
        id: row.id
      }
      this.$confirm('此操作将永久删除该记录, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteGoodsRecord(params).then(res => {
          if (res.head.ret === 0) {
            this.$message({
              type: 'success',
              message: '删除成功!'
            })
            this.getList()
          }
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
    },
    handleEdit(index, row) {
      this.goodsObj = row
      this.showDialog = true
    },
    handleAdd() {
      this.goodsObj = {}
      this.showDialog = true
    },
    closeDialog() {
      this.goodsObj = {}
      this.showDialog = false
      this.getList()
    }
  }
}
</script>
<style lang="scss" scoped>

</style>
