<template>
  <div>
    <div class="lc-head">
      <div class="lc-head-content">
        <div class="lc-head-content-all">年里程数：<span>{{ userObj.yearTotal }}m</span></div>
        <div class="lc-head-content-sub">
          <div class="lc-head-content-sub-month">月里程：<span>{{ userObj.monthTotal }}m</span></div>
          <div class="lc-head-content-sub-sort" v-if="myRank!=='未入榜'">
            <div style="width:60%; text-align:right">排名：</div><div class="myRank">{{myRank}}</div>
          </div>
          <div class="lc-head-content-sub-sort2" v-if="myRank==='未入榜'">
            排名：未入榜
          </div>
        </div>
      </div>
    </div>
    <div class="lc-contentpic">
        <img src="./assets/title.png">
    </div>
    <div class="lc-content">
      <div class="lc-content-title">
        <div :class="monthTitle" @click="titleSelectTab=true">
        </div>
        <div :class="totalTitle" @click="titleSelectTab=false">
        </div>
      </div>
      <div :class="item.userInfoDto && item.userInfoDto.userId===userId ? 'lc-content-row cssIsMe' : 'lc-content-row'" v-for="item in dataList" :key="item.id" >
        <div class="lc-content-row-sort">
          <img src="./assets/frist.png" v-if="item.sort==1">
          <img src="./assets/second.png" v-if="item.sort==2">
          <img src="./assets/three.png" v-if="item.sort==3">
          <span v-if="item.sort>3">NO.{{item.sort}}</span>
        </div>
        <div class="lc-content-row-head" @click="launchPersonalHome(item.userInfoDto)">
          <img src="./assets/default-head.png" v-if="item.userInfoDto==null"/>
          <img :src="item.userInfoDto.userPic" v-if="item.userInfoDto"/>
          <img src="./assets/round.png" class="lc-content-row-head-round" />
        </div>
        <div class="lc-content-row-name" @click="launchPersonalHome(item.userInfoDto)">{{ item.userInfoDto ? item.userInfoDto.nickName : '未知用户'}}</div>
        <div class="lc-content-row-mile">{{ item.mileage }}m</div>
      </div>
    </div>
    <div class="lc-bottom">
        <img src="./assets/background-bottom.png" />
    </div>
  </div>
</template>

<script>
import { appJS, getUrlParameter, throttle } from '@/utils/common'
import { getInfoByUserId, getRankingMonth, getRankingYear, getUserInfo } from './assets/js/api'
import Cookies from 'js-cookie'
import { THROTTLE_TIME } from '@/consts/index'
let vm
const throttleTime = THROTTLE_TIME()
export default {
  async created () {
    this.$nextTick(() => {
      vm = this
    })
  },
  async mounted () {
    const query = getUrlParameter()
    window.addEventListener('contextmenu', function (e) {
      e.preventDefault()
    })
    // this.userId = query.userid
    this.getUserInfo()
    this.getMyPrint()
    this.getRankingMonthList()
    this.getRankingYearList()
  },
  data () {
    return {
      rankingYearList: [],
      rankingMonthList: [],
      userObj: {},
      userId: '',
      titleSelectTab: true
    }
  },
  components: {},
  methods: {
    async getUserInfo() {
      const { data, head } = await getUserInfo(this.userId)
      if (head.ret === 0) {
        this.userId = data.userInfoDto.userId
      }
    },
    async getMyPrint () {
      const { data, head } = await getInfoByUserId(this.userId)
      if (head.ret === 0) {
        this.userObj = data
      }
    },
    async getRankingMonthList () {
      const { data, head } = await getRankingMonth(this.userId)
      if (head.ret === 0) {
        this.rankingMonthList = data
      }
    },
    async getRankingYearList () {
      const { data, head } = await getRankingYear(this.userId)
      if (head.ret === 0) {
        this.rankingYearList = data
      }
    },
    launchPersonalHome: throttle((item) => {
      if (item && item.userId) {
        if (window.webkit && window.webkit.messageHandlers) {
          window.webkit.messageHandlers.launchPersonalHome.postMessage(item.userId)
        } else {
          window.StarPrinterJS.launchPersonalHome(item.userId)
        }
      } else {
        this.$toast('当前用户未知')
      }
    }, throttleTime)
  },
  computed: {
    monthTitle() {
      const style = this.titleSelectTab ? ' active' : ''
      return 'lc-content-title-item-month' + style
    },
    totalTitle() {
      const style = this.titleSelectTab ? '' : ' active'
      return 'lc-content-title-item-total' + style
    },
    dataList() {
      return this.titleSelectTab ? this.rankingMonthList : this.rankingYearList
    },
    myRank() {
      let rank = '未入榜'
      if (this.rankingMonthList && this.rankingMonthList.length > 0) {
        this.rankingMonthList.forEach(element => {
          if (element.userInfoDto && element.userInfoDto.userId === this.userId) {
            rank = element.sort
          }
        })
      }
      return rank
    },
    isiosSystem() {
      if (window.webkit !== undefined && window.webkit.messageHandlers !== undefined) {
        return true
      } else {
        return false
      }
    }
  }
}
</script>

<style lang="scss">
  html {
    font-size: 16px;
    background: #FFB400;
  }
  .cssIsMe {
    background-color: #FFF5DC !important;
  }
  .lc-head {
    min-height: 20.46875rem;
    width: 100%;
    background-image: url(./assets/head.png);
    background-size: 100% 20.46875rem;
    position: relative;
    &-title {
      min-height: 2.6875rem;
      position: relative;
      text-align: center;
      line-height: 2.6875rem;
      color:  #ffffff;
      font-size: 1.09375rem;
      &-return {
        min-width: 0.5625rem;
        min-height: 1.03125rem;
        position: absolute;
        left: 1.625rem;
        img {
          width: 0.5625rem;
          height: 1.03125rem;
        }
      }
      span {
        line-height: 2.7875rem;
      }
    }
    &-content {
      position: absolute;
      min-height: 6.875rem;
      bottom: 0px;
      width: 100%;
      &-all {
        text-align: center;
        font-size: 1.15625rem;
        font-family: FZY4K--GBK1-0;
        line-height: 2rem;
        color: #333333;
        font-weight: bold;
        span {
          color:#FF9403;
        }
      }
      &-sub {
        margin: 0.5rem 3.125rem;
        line-height: 1.96875rem;
        height: 1.96875rem;
        font-size: 0.71875rem;
        // display: flex;
        position: relative;
        text-align: center;
        font-weight: bold;
        &-month {
          position: absolute;
          float: left;
          width: 45%;
          border: 2px dashed #FFB400;
          border-radius: 10px;
          line-height: 1.96875rem;
          height: 2.06875rem;
          span {
            color:#FF9403;
          }
        }
        &-sort {
          float: right;
          position: absolute;
          right: 0;
          width: 45%;
          margin-left: 1.3125rem;
          border: 2px dashed #FFB400;
          border-radius: 10px;
          line-height: 2.06875rem;
          height: 2.06875rem;
          display:flex;
          .myRank {
            font-size: 1.4375rem;
            color:#FF9403;
            line-height: 2.06875rem;
          }
        }
        &-sort2 {
          float: right;
          right: 0;
          width: 45%;
          position: absolute;
          margin-left: 1.3125rem;
          border: 2px dashed #FFB400;
          border-radius: 10px;
          line-height: 2.06875rem;
          height: 2.06875rem;
        }
      }
    }
  }
  .lc-contentpic {
    margin: 1.15625rem 1.6875rem;
    min-height: 4.68rem;
    img {
      // position: absolute;
      width: 100%;
      height: 4.68rem;
    }
  }
  .lc-content {
    //background-color: #ffffff;
    margin: 1.25rem 1.4375rem 0 1.4375rem;
    padding-bottom: 3rem;
    position: relative;
    // box-shadow: #FF9403 10px 10px 30px 5px ;
    z-index: 99999;
    &-title {
      display: flex;
      background-color: #FFB400;
      min-height: 2rem;
      &-item-month {
        position: relative;
        text-align: center;
        width: 50%;
        background-image: url(./assets/month-hide.png);
        background-size: 100% auto;
        background-repeat: no-repeat;
        background-position: bottom;
        &.active {
          background-image: url(./assets/month-select.png);
        }
        background-color: #FFB400;
      }
      &-item-total {
        position: relative;
        background-color: #FFB400;
        text-align: center;
        width: 50%;
        background-image: url(./assets/year-hide.png);
        background-size: 100% auto;
        background-repeat: no-repeat;
        background-position: bottom;
        &.active {
          background-image: url(./assets/year-select.png);
        }
      }
    }
    &-row {
      display: flex;
      position: relative;
      height: 3.21875rem;
      background-color: #ffffff;
      &-sort {
        width: 4.59375rem;
        line-height: 3.21875rem;
        text-align: center;
        img {
          width: 1.75rem;
          height: 1.5rem;
        }
      }
      &-head {
        min-width: 2.6375rem;
        position: relative;
        vertical-align: middle;
        text-align: center;
        img {
          height: 2.4375rem;
          width: 2.4375rem;
          border-radius: 50%;
          position:absolute;
          top:0;
          left:0;
          bottom:0;
          right:0;
          margin:auto;
          // margin:0.390625rem 0.15rem;
        }
      }
      &-name {
        line-height: 3.21875rem;
        width: 8rem;
        padding-left: 0.656rem;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
      &-mile {
        line-height: 3.21875rem;
        position: absolute;
        right: 1rem;
      }
    }
  }
  .lc-bottom {
    // min-height: 10.03125rem;
    // position: absolute;
    // width: 100%;
    // background-image: url(./assets/background-bottom.png);
    // background-size: 100% 10.03125rem;
    img {
      width: 100%;
      height: 10.03125rem;
    }
    position: absolute;
    width: 100%;
    margin-top: -10.03125rem;
  }
</style>
