<template>
  <div class="card">
    <div class="user">
      <div class="user-avatar">
        <el-avatar :size="50" :src="item.userPic" />
      </div>
      <div class="user-info">
        <div class="user-info-name">{{ item.userName }}</div>
        <div class="user-info-gender">{{ item.sex }} codeId: {{ item.codeId }} </div>
        <div class="user-info-date">{{ item.createTime }}</div>
      </div>

    </div>
    <div class="tag-title">
      {{ item.title }}
    </div>
    <div class="tag">
      {{ item.content }}
    </div>
    <div class="photo">
      <el-image
        v-for="picItem in item.smallPicDto"
        :key="picItem.pic"
        :src="picItem.pic"
        fit="cover"
        style="width:100px;height:100px;padding:5px"
      />
    </div>
    <div class="itemtags">
      {{ parseTags }}
    </div>
    <div class="count">
      <div class="el-icon-chat-round">{{ item.commentNum }}</div>
      <div class="el-icon-download">{{ item.downLoadNum }}/<font color="red">{{ item.downLoadNum-item.forgeDownloadNum }}</font></div>
      <div class="el-icon-share">{{ item.shareNum }}/<font color="red">{{ item.shareNum - item.forgeShareNum }}</font></div>
      <div class="el-icon-thumb">{{ item.giveLikeNum }}/<font color="red">{{ item.giveLikeNum - item.forgeGiveLikeNum }}</font></div>
      <div class="el-icon-printer">{{ item.printNum }}/<font color="red">{{ item.printNum - item.forgePrintNum }}</font></div>
    </div>
    <div class="control">
      <el-tooltip content="复制链接" placement="top">
        <el-button
          v-clipboard:copy="item.htmlUrl"
          v-clipboard:success="onCopy"
          class="el-icon-document-copy"
          @click.stop="handleCopy"
        />

      </el-tooltip>
      <el-tooltip content="评论" placement="top">
        <el-button class="el-icon-chat-line-round " @click.stop="handleComment" />
      </el-tooltip>

      <el-tooltip content="删除" placement="top">
        <el-button class="el-icon-delete" @click.stop="handleDel" />
      </el-tooltip>

      <el-tooltip content="点赞" placement="top">
        <el-button class="el-icon-thumb" @click.stop="handleGiveLike" />
      </el-tooltip>

      <el-tooltip content="设置标签" placement="top">
        <el-button v-if="item.isLabelObject===1" class="el-icon-collection-tag" type="primary" @click.stop="handleSettingTag" />
        <el-button v-if="item.isLabelObject===0" class="el-icon-collection-tag" @click.stop="handleSettingTag" />
      </el-tooltip>
    </div>
    <div class="stick">
      <el-button type="danger" @click.stop="handleStickTop">{{ (item.stickObj && item.stickObj.key==999) ? "取消置顶":"置顶" }}</el-button>
      <el-button type="primary" @click.stop="handleStickEssence">{{ (item.stickObj && item.stickObj.key==1) ? "取消精选":"精选" }}</el-button>
    </div>
    <el-dialog title="设置标签" :visible.sync="dialogVisible">
      <tag-dialog :feed-id="item.feedId" :is-label-object="item.isLabelObject" @closeDialog="closeDialog" />
    </el-dialog>
    <el-dialog title="添加评论" :visible.sync="showCommentDialog">
      <comment-edit-dialog :feed-id="item.feedId" @closeDialog="closeDialog" />
    </el-dialog>
  </div>
</template>
<script>
import { deleteRecord, updateStickFeed, giveLike } from '@/api/community/activity'
import TagDialog from '@/views/community/activity/component/TagDialog'
import CommentEditDialog from '@/views/community/comment/component/EditDialog'
export default {
  name: 'Card',
  components: { TagDialog, CommentEditDialog },
  props: {
    item: {
      type: Object,
      required: true
    },
    labelList: {
      type: Array,
      default: function() {
        return []
      }
    },
    subjectList: {
      type: Array,
      default: function() {
        return []
      }
    }
  },
  data() {
    return {
      dialogVisible: false,
      showCommentDialog: false
    }
  },
  computed: {
    parseTags() {
      let rtn = ''
      if (this.item) {
        const subject = this.item.subject
        const labelMap = this.item.labelMap
        if (this.labelList && labelMap) {
          this.labelList.forEach(e => {
            if (labelMap[e.name]) {
              rtn += '#' + e.titleZh + ' '
            }
          })
        }

        if (this.subjectList && subject) {
          this.subjectList.forEach(e => {
            if (e.id === subject) {
              rtn += '#' + e.name + ' '
            }
          })
        }
      }
      return rtn
    }
  },
  mounted() {
  },
  methods: {
    handleGiveLike() {
      const params = {
        id: this.item.feedId
      }
      giveLike(params).then(res => {
        if (res.head.ret === 0) {
          this.$message.success('操作成功')
          this.notifyUpdate()
        }
      })
    },
    closeDialog() {
      this.dialogVisible = false
      this.showCommentDialog = false
    },
    onCopy(e) {
      this.$message.success('复制成功')
    },
    handleCopy() {
    },
    handleStickTop() {
      const caozuo = (this.item.stickObj && this.item.stickObj.key === 999) ? '取消置顶' : '置顶'
      const stickNum = (this.item.stickObj && this.item.stickObj.key === 999) ? 0 : 999
      this.$confirm('此操作将' + caozuo + '该动态, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        updateStickFeed({
          id: this.item.feedId,
          stickType: stickNum
        }).then(res => {
          if (res.head.ret === 0) {
            this.$message.success('操作成功')
            this.notifyUpdate()
          }
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消操作'
        })
      })
    },
    handleStickEssence() {
      const caozuo = (this.item.stickObj && this.item.stickObj.key === 1) ? '取消精选' : '精选'
      const stickNum = (this.item.stickObj && this.item.stickObj.key === 1) ? 0 : 1
      this.$confirm('此操作将该动态设置' + caozuo + ', 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        updateStickFeed({
          id: this.item.feedId,
          stickType: stickNum
        }).then(res => {
          if (res.head.ret === 0) {
            this.$message.success('操作成功')
            this.notifyUpdate()
          }
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消操作'
        })
      })
    },
    /** 删除 */
    async handleDel() {
      this.$confirm('此操作将永久删除该文件, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteRecord({
          id: this.item.feedId
        }).then(res => {
          if (res.head.ret === 0) {
            this.$message.success('删除成功')
            this.notifyUpdate()
          }
        }
        )
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
    },
    /** 通知更新 */
    notifyUpdate() {
      this.$emit('notifyUpdate')
    },
    /** 点击评论 */
    handleComment() {
      this.showCommentDialog = true
      // this.$router.push({
      //   path: '/community/comment',
      //   query: {
      //     feedId: this.item.feedId
      //   }
      // })
    },
    handleSettingTag() {
      this.dialogVisible = true
    }
  }
}

</script>
<style lang="scss" scoped>
.itemtags {
  display: flex;
  color:cadetblue;
}
.card{
    position: relative;
    color: rgba(0, 0, 0, 0.65);
    font-size: 14px;
    font-variant: tabular-nums;
    line-height: 1.5;
    border: 1px solid gray;
    display: flex;
    flex-direction: column;
    padding: 8px;
    .user{
        margin-bottom: 5px;
        display: flex;
        flex-direction: row;
        &-avatar{
            padding: 8px;
        }
        &-info{
            display: flex;
            flex-direction: column;
        }
    }
    .photo{
        width: 100%;
        margin-bottom: 5px;
    }
    .count{
        display: flex;
        margin-bottom: 5px;
        justify-content: space-around;
    }
    .tag{
      overflow:hidden;
      text-overflow:ellipsis;
      white-space:nowrap;
      margin-bottom: 5px;
      &-title {
        font-weight: bold;
        font-size: 20px;
        overflow:hidden;
        text-overflow:ellipsis;
        white-space:nowrap;
        margin-bottom: 5px;
      }
    }
    .control{
        display: flex;
        flex-direction: row;
        justify-content: space-around;
            margin: 0;
            padding: 0;
            list-style: none;
            background: #fafafa;
            border-top: 1px solid #e8e8e8;
            zoom: 1;
            padding-top: 10px;
             padding-bottom: 10px;
    }
    .stick {
      position: absolute;
      right: 10px;
      z-index: 999;
    }
}
</style>
