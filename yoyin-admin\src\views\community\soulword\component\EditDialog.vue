<template>
  <div v-if="visiable" class="edit-dialog">
    <el-dialog
      ref="dialog"
      :show-close="false"
      :title="form.id === '' ? '新增' : '编辑' "
      :visible.sync="visiable"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      width="80%"
      center
    >
      <el-form ref="form" :model="form" :rules="rules" label-width="100px" class="edit-form">
        <el-form-item label="文案：" prop="content">
          <el-input
            v-model="form.content"
            type="textarea"
            :rows="3"
            maxlength="500"
            placeholder="请输入文案"
          />
        </el-form-item>
        <el-form-item>
          <el-button @click="handleClose">返回</el-button>
          <el-button v-permission="'btn-menu2cunkuan-soulword-edit'" type="primary" @click="submit">保存</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import { saveRecord } from '@/api/community/soulword'
export default {
  name: 'EditDialog',
  props: {
    visiable: {
      type: Boolean,
      required: true,
      default: false
    },
    formData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      /** 表单 */
      form: {
      },
      /** 校验规则 */
      rules: {
        content: [
          { required: true, message: '请输入文案', trigger: 'blur' }
        ]
      },
      /** 待上传的文件 */
      upFile: undefined,
      testUrl: '',
      /** 时间格式 */
      timeFormat: 'yyyy-MM-dd HH:mm:ss',
      showDialog: false
    }
  },
  computed: {
  },
  watch: {
    formData: function(val) {
      this.upFile = undefined
      this.form = { ...val }
    }
  },
  mounted() {
    this.show = this.visiable
  },
  methods: {
    /** 返回 */
    handleClose(code) {
      this.$emit('closeDialog', code)
    },
    /** 提交 */
    submit() {
      this.addUpdateRecord()
    },
    async addUpdateRecord() {
      // 新增
      if (!this.form.id) {
        this.form.id = ''
        // 新增
        const res = await saveRecord(this.form)
        if (res.head.ret === 0) {
          this.$message.success('保存成功')
          this.handleClose('1')
        }
      } else {
        // 更新
        const res = await saveRecord(this.form)
        if (res.head.ret === 0) {
          this.$message.success('保存成功')
          this.handleClose('1')
        }
      }
    }
  }

}
</script>

<style lang='scss' scoped>
  .avatar-uploader .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }
  .avatar-uploader .el-upload:hover {
    border-color: #409EFF;
  }
  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 178px;
    height: 178px;
    line-height: 178px;
    text-align: center;
  }
  .avatar {
    width: 178px;
    height: 178px;
    display: block;
  }
</style>
