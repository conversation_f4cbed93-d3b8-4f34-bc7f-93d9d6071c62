/*
 * @Description: In User Settings Edit
 * @Author: your name
 * @Date: 2019-10-16 10:58:32
 * @LastEditTime: 2019-10-16 18:13:52
 * @LastEditors: Please set LastEditors
 */
import Vue from 'vue'
import Router from 'vue-router'

Vue.use(Router)

/* Layout */
import Layout from '@/layout'

/**
 * Note: sub-menu only appear when route children.length >= 1
 * Detail see: https://panjiachen.github.io/vue-element-admin-site/guide/essentials/router-and-nav.html
 *
 * hidden: true                   if set true, item will not show in the sidebar(default is false)
 * alwaysShow: true               if set true, will always show the root menu
 *                                if not set alwaysShow, when item has more than one children route,
 *                                it will becomes nested mode, otherwise not show the root menu
 * redirect: noRedirect           if set noRedirect will no redirect in the breadcrumb
 * name:'router-name'             the name is used by <keep-alive> (must set!!!)
 * meta : {
    roles: ['admin','editor']    control the page roles (you can set multiple roles)
    title: 'title'               the name show in sidebar and breadcrumb (recommend set)
    icon: 'svg-name'             the icon show in the sidebar
    breadcrumb: false            if set false, the item will hidden in breadcrumb(default is true)
    activeMenu: '/example/list'  if set path, the sidebar will highlight the path you set
  }
 */

/**
 * constantRoutes
 * a base page that does not have permission requirements
 * all roles can be accessed
 */
export const constantRoutes = [
  {
    path: '/login',
    component: () => import('@/views/login/index'),
    hidden: true
  },

  {
    path: '/404',
    component: () => import('@/views/404'),
    hidden: true
  },

  // {
  //   path: '/',
  //   component: Layout,
  //   redirect: '/dashboard',
  //   children: [{
  //     path: 'dashboard',
  //     name: 'Dashboard',
  //     component: () => import('@/views/dashboard/index'),
  //     meta: { title: '首页', icon: 'dashboard' }
  //   }]
  // },

  {
    path: '/',
    component: Layout,
    redirect: '/dataanalysis',
    meta: { title: 'Home', authCode: 'menuAnalysis' },
    children: [
      {
        path: 'dataanalysis',
        name: 'dataanalysis',
        component: () => import('@/views/dataanalysis/index'),
        meta: { title: '数据统计', icon: 'analysis', authCode: 'menuAnalysis' }
      }
    ]
  },

  {
    path: '/community',
    component: Layout,
    redirect: 'noRedirect',
    name: 'community',
    meta: { title: '2寸、宽幅', icon: 'community', authCode: 'menu2cunkuanfu' },
    children: [
      {
        path: 'material',
        name: 'material',
        component: () => import('@/views/community/material/index?menuType=a2'),
        meta: { title: '素材管理', icon: 'material', authCode: 'menu2cunkuan-material' }
      },
      {
        path: 'soulword',
        name: 'soulword',
        component: () => import('@/views/community/soulword/index'),
        meta: { title: '每日一言', icon: 'table', authCode: 'menu2cunkuan-soulword' }
      },
      {
        path: 'simplepicture',
        name: 'simplepicture',
        component: () => import('@/views/community/simplepicture/index'),
        meta: { title: '简笔画', icon: 'table', authCode: 'menu2cunkuan-simplepicture' }
      },
      {
        path: 'englishword',
        name: 'englishword',
        component: () => import('@/views/words/list/index'),
        meta: { title: '单词本', icon: 'material', authCode: 'menu2cunkuan-englishword' }
      },
      {
        path: 'knowledge',
        name: 'knowledge',
        component: () => import('@/views/community/knowledge/index'),
        meta: { title: '干货区列表', icon: 'table', authCode: 'menu2cunkuan-knowledge' }
      }
    ]
  },

  {
    path: '/xeasylabel',
    component: Layout,
    redirect: 'noRedirect',
    name: 'xeasylabel',
    meta: { title: '标签打印机', icon: 'xeasylabel', authCode: 'xeasylabel' },
    children: [
      {
        path: 'xeasy-dict',
        name: 'xeasy-dict',
        component: () => import('@/views/xeasylabel/dict/index'),
        meta: { title: '字典项管理', icon: 'dict', authCode: 'xeasylabel-dict' }
      },
      {
        path: 'xeasy-material',
        name: 'xeasy-material',
        component: () => import('@/views/xeasylabel/material/index'),
        meta: { title: '素材库', icon: 'table', authCode: 'xeasylabel-material' }
      },
      {
        path: 'xeasy-paperinfo',
        name: 'xeasy-paperinfo',
        component: () => import('@/views/xeasylabel/paperinfo/index'),
        meta: { title: '耗材', icon: 'paperinfo', authCode: 'xeasylabel-paperinfo' }
      },
      {
        path: 'xeasy-templatedraft',
        name: 'xeasy-templatedraft',
        component: () => import('@/views/xeasylabel/templatedraft/index'),
        meta: { title: '标签模板', icon: 'templatedraft', authCode: 'xeasylabel-templatedraft' }
      },
      {
        path: 'xeasy-userdraft',
        name: 'xeasy-userdraft',
        component: () => import('@/views/xeasylabel/userdraft/index'),
        meta: { title: '用户素材模板', icon: 'table', authCode: 'xeasylabel-userdraft' }
      }
    ]
  },
  {
    path: '/formula',
    component: Layout,
    redirect: 'noRedirect',
    name: 'formula',
    meta: { title: '学公式', icon: 'setting', authCode: 'menuFormula' },
    children: [
      {
        path: 'formula-subject',
        name: 'formula-subject',
        component: () => import('@/views/community/formulainfo/formulasubject'),
        meta: { title: '年段学科配置', icon: 'dict', authCode: 'menuFormula-subject' }
      },
      {
        path: 'formula-info',
        name: 'formula-info',
        component: () => import('@/views/community/formulainfo/formulaInfo'),
        meta: { title: '公式管理', icon: 'dict', authCode: 'menuFormula-info' }
      }
    ]
  },
  {
    path: '/initialTraining',
    component: Layout,
    redirect: 'noRedirect',
    name: 'initialTraining',
    meta: { title: '启蒙训练', icon: 'setting', authCode: 'menuTraining' },
    children: [
      {
        path: 'initialTraining-info',
        name: 'initialTraining-info',
        component: () => import('@/views/initialtraining/info'),
        meta: { title: '资源内容', icon: 'dict', authCode: 'menuTraining-info' }
      },
      {
        path: 'initialTraining-dict',
        name: 'initialTraining-dict',
        component: () => import('@/views/initialtraining/dict'),
        meta: { title: '类目管理', icon: 'dict', authCode: 'menuTraining-dict' }
      }
    ]
  },
  {
    path: '/community2',
    component: Layout,
    redirect: 'noRedirect',
    name: 'community2',
    meta: { title: 'A4', icon: 'community', authCode: 'menuA4' },
    children: [
      {
        path: 'material2',
        name: 'material2',
        component: () => import('@/views/community/material-a4/index'),
        meta: { title: '模板素材A4/A5', icon: 'material', authCode: 'menuA4-material' }
      },
      // {
      //   path: 'materialUsLetter',
      //   name: 'materialUsLetter',
      //   component: () => import('@/views/community/material-usletter/index'),
      //   meta: { title: '模板素材-usletter', icon: 'material', authCode: 'menuA4-usletter' }
      // },
      {
        path: 'simplepicture2',
        name: 'simplepicture2',
        component: () => import('@/views/community/simplepicture-a4/index'),
        meta: { title: '图片素材', icon: 'table', authCode: 'menuA4-simplepicture' }
      },
      {
        path: 'examination',
        name: 'examination',
        component: () => import('@/views/community/examination/index'),
        meta: { title: '学习资源', icon: 'table', authCode: 'menuA4-examination' }
      },
      {
        path: 'office-material',
        name: 'office-material',
        component: () => import('@/views/office/material/index'),
        meta: { title: '办公素材', icon: 'material', authCode: 'menuA4-office-material' }
      }
    ]
  },

  {
    path: '/user',
    component: Layout,
    redirect: '/user/usermanage',
    name: 'user',
    alwaysShow: true,
    meta: { title: '用户管理', icon: 'usermanage', authCode: 'menuUsermanage' },
    children: [
      {
        path: 'usermanage',
        name: 'usermanage',
        component: () => import('@/views/user/list/index'),
        meta: { title: '用户列表', icon: 'table', authCode: 'menuUsermanage-user' }
      },
      {
        path: 'userdraft',
        name: 'userdraft',
        component: () => import('@/views/user/userdraft/index'),
        meta: { title: '用户动态', icon: 'table', authCode: 'menuUsermanage-draft' }
      },
      {
        path: 'feedback',
        name: 'feedback',
        component: () => import('@/views/feedback/list/index'),
        meta: { title: '用户反馈', icon: 'feedback', authCode: 'menuUsermanage-feedback' }
      }
    ]
  },

  {
    path: '/device',
    component: Layout,
    redirect: '/device/usermanage',
    name: 'user',
    alwaysShow: true,
    meta: { title: '设备管理', icon: 'usermanage', authCode: 'menuUsermanage' },
    children: [
      {
        path: 'device',
        name: 'device',
        component: () => import('@/views/device/index'),
        meta: { title: '设备管理', icon: 'table', authCode: 'menuUsermanage-device' }
      }
    ]
  },
  {
    path: '/systemsetting',
    component: Layout,
    redirect: '/systemsetting/index',
    name: 'systemsetting',
    meta: { title: '系统设置', icon: 'setting', authCode: 'menuSystemsetting' },
    children: [{
      path: 'banner',
      name: 'banner',
      component: () => import('@/views/community/banner/index'),
      meta: { title: 'banner管理', icon: 'table', authCode: 'menuSystemsetting-banner' }
    },
    {
      path: 'activion',
      name: 'activion',
      component: () => import('@/views/systemsetting/index'),
      meta: { title: '活动设置', icon: 'setting', authCode: 'menuSystemsetting-activion' }
    },
    {
      path: 'help',
      name: 'help',
      component: () => import('@/views/systemhelp/index'),
      meta: { title: '帮助FAQ', icon: 'setting', authCode: 'menuSystemsetting-help' }
    }, {
      path: 'installpackage',
      name: 'installpackage',
      component: () => import('@/views/version/list/index'),
      meta: { title: '安装包管理', icon: 'table', authCode: 'menuSystemsetting-installpackage' }
    }, {
      path: 'version',
      name: 'version',
      component: () => import('@/views/version/list2/index'),
      meta: { title: '固件版本管理', icon: 'table', authCode: 'menuSystemsetting-version' }
    }, {
      path: 'functionssetting',
      name: 'functionssetting',
      component: () => import('@/views/functionssetting/index'),
      meta: { title: '功能显示设定', icon: 'setting', authCode: 'menuSystemsetting-setting' }
    }, {
      path: 'dict',
      name: 'dict',
      component: () => import('@/views/system/dict/index'),
      meta: { title: '字典管理', icon: 'dict', authCode: 'menuSystemsetting-dict' }
    }]
  },
  {
    path: '/quanpin',
    component: Layout,
    redirect: '/quanpin/memgergoods',
    name: 'memgergoods',
    meta: { title: 'vip会员管理', icon: 'setting', authCode: 'menuQuanpin' },
    children: [{
      path: 'vipMember',
      name: 'vipMember',
      component: () => import('@/views/quanpin/vipmember/index'),
      meta: { title: 'vip会员', icon: 'table', authCode: 'menuQuanpin-vipmember' }
    }, {
      path: 'orderRecord',
      name: 'orderRecord',
      component: () => import('@/views/quanpin/orderrecord/index'),
      meta: { title: '购买记录', icon: 'table', authCode: 'menuQuanpin-orderrecord' }
    }, {
      path: 'memgergoodsvip',
      name: 'memgergoodsvip',
      component: () => import('@/views/quanpin/membergoods/index'),
      meta: { title: 'vip页面管理', icon: 'table', authCode: 'menuQuanpin-memgergoodsvip' }
    }]
  },
  {
    path: '/coin',
    component: Layout,
    name: 'coin',
    alwaysShow: true,
    meta: { title: '积分商城', icon: 'usermanage', authCode: 'menuCoin' },
    children: [
      {
        path: 'titleList',
        name: 'titleList',
        component: () => import('@/views/user/titlelist/index'),
        meta: { title: '挂件管理', icon: 'table', authCode: 'menuCoin-title' }
      },
      {
        path: 'goodslist',
        name: 'goodslist',
        component: () => import('@/views/coin/goods/index'),
        meta: { title: '商品列表', icon: 'table', authCode: 'menuCoin-goods' }
      },
      {
        path: 'missionlist',
        name: 'missionlist',
        component: () => import('@/views/coin/mission/index'),
        meta: { title: '积分任务列表', icon: 'table', authCode: 'menuCoin-mission' }
      },
      {
        path: 'orderlist',
        name: 'orderlist',
        component: () => import('@/views/coin/order/index'),
        meta: { title: '兑换订单列表', icon: 'table', authCode: 'menuCoin-order' }
      },
      // {
      //   path: 'finishcount',
      //   name: 'finishcount',
      //   component: () => import('@/views/coin/analysiscountfinish/index'),
      //   meta: { title: '任务次数统计', icon: 'table' }
      // },
      // {
      //   path: 'rankcoin',
      //   name: 'rankcoin',
      //   component: () => import('@/views/coin/analysiscountrank/index'),
      //   meta: { title: '新增积分统计', icon: 'table' }
      // },
      {
        path: 'statisticscoin',
        name: 'statisticscoin',
        component: () => import('@/views/coin/analysis/index'),
        meta: { title: '积分统计', icon: 'table', authCode: 'menuCoin-statisticscoin' }
      }
    ]
  },
  {
    path: '/other',
    component: Layout,
    name: '其他配置',
    alwaysShow: true,
    meta: { title: '其他配置', icon: 'usermanage', authCode: 'menuOther' },
    children: [
      {
        path: 'materialtype',
        name: 'materialtype',
        component: () => import('@/views/community/materialtype/index'),
        meta: { title: '素材库类型', icon: 'material', authCode: 'menuOther-materialtype' }
      },
      {
        path: 'tag',
        name: 'tag',
        component: () => import('@/views/community/tag/index'),
        meta: { title: '社区标签', icon: 'table', authCode: 'menuOther-tag' }
      },
      {
        path: 'wordTypeList',
        name: 'wordTypeList',
        component: () => import('@/views/wordtype/index'),
        meta: { title: '教材类型', icon: 'material', authCode: 'menuOther-wordtype' }
      },
      {
        path: 'account',
        name: 'account',
        component: () => import('@/views/auth/account/index'),
        meta: { title: '账号管理', icon: 'setting', authCode: 'menuOther-account' }
      },
      {
        path: 'role',
        name: 'role',
        component: () => import('@/views/auth/role/index'),
        meta: { title: '角色管理', icon: 'setting', authCode: 'menuOther-role' }
      },
      {
        path: 'resources',
        name: 'resources',
        component: () => import('@/views/auth/resources/index'),
        meta: { title: '资源点设置', icon: 'setting', authCode: 'menuOther-resources' }
      }
    ]
  },
  // 404 page must be placed at the end !!!
  { path: '*', redirect: '/404', hidden: true }
]

const createRouter = () => new Router({
  // mode: 'history', // require service support
  scrollBehavior: () => ({ y: 0 }),
  routes: constantRoutes
})

const router = createRouter()

// Detail see: https://github.com/vuejs/vue-router/issues/1234#issuecomment-*********
export function resetRouter() {
  const newRouter = createRouter()
  router.matcher = newRouter.matcher // reset router
}

export default router
