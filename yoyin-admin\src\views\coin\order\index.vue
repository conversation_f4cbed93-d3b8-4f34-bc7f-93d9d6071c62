<template>
  <div class="app-container">
    <div class="table-container">
      <el-table
        ref="multipleTable"
        :data="list"
        tooltip-effect="dark"
        style="width: 100%"
        border
      >
        <el-table-column type="selection" width="55" />
        <el-table-column label="收件人名称" prop="userName" />
        <el-table-column label="用户联系电话">
          <template slot-scope="scope">{{ scope.row.userPhone }}</template>
        </el-table-column>
        <el-table-column label="用户收件地址">
          <template slot-scope="scope">{{ scope.row.userAddress }}</template>
        </el-table-column>
        <el-table-column label="商品名称">
          <template slot-scope="scope">{{ scope.row.goodsName }}</template>
        </el-table-column>
        <el-table-column label="兑换数量">
          <template slot-scope="scope">{{ scope.row.goodsCount }}</template>
        </el-table-column>
        <el-table-column label="用户昵称">
          <template slot-scope="scope">{{ scope.row.userInfo? scope.row.userInfo.userInfoDto.nickName : '' }}</template>
        </el-table-column>
        <el-table-column label="用户code">
          <template slot-scope="scope">{{ scope.row.userInfo? scope.row.userInfo.userInfoDto.codeId : '' }}</template>
        </el-table-column>
        <el-table-column label="兑换时间">
          <template slot-scope="scope">{{ scope.row.createtime }}</template>
        </el-table-column>
        <el-table-column label="快递公司">
          <template slot-scope="scope">{{ scope.row.deliveryCompany }}</template>
        </el-table-column>
        <el-table-column label="快递单号">
          <template slot-scope="scope">{{ scope.row.deliveryCode }}</template>
        </el-table-column>
        <el-table-column label="状态">
          <template slot-scope="scope">{{ scope.row.deliveryStatus }}</template>
        </el-table-column>
        <el-table-column label="操作">
          <template slot-scope="scope">
            <el-button size="mini" @click="handleEdit(scope.$index, scope.row)">编辑</el-button>
            <el-button
              v-permission="'btn-menuCoin-order-edit'"
              size="mini"
              type="danger"
              @click="handleDeleteRow(scope.$index, scope.row)"
            >删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination-container" style="margin-top:20px;">
        <el-pagination
          :current-page="params.pageno"
          :page-sizes="[10, 20, 30, 40]"
          :page-size="params.pagesize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
    <div class="dialog-container">
      <EditDialog :visiable="showDialog" :form-data="ordersObj" @closeDialog="closeDialog" />
    </div>
  </div>
</template>
<script>
import { fetchOrdersListByPage, deleteOrdersRecord } from '@/api/coin/order'
import EditDialog from './component/EditDialog'
export default {
  name: 'GoodsList',
  components: {
    EditDialog
  },
  data() {
    return {
      // 查询参数
      params: {
        pageno: 1,
        pagesize: 10
      },
      /** 列表 */
      list: [],
      /** 总条数 */
      total: 0,
      showDialog: false,
      ordersObj: {}
    }
  },
  mounted() {
    this.getList()
  },
  methods: {
    /** 获取列表 */
    async getList() {
      const res = await fetchOrdersListByPage(this.params)
      if (res.head.ret === 0) {
        this.list = res.data.result
        this.total = res.data.totalCount
      }
    },
    /** 切换每页条数 */
    handleSizeChange(val) {
      this.params.pagesize = val
      this.getList()
    },
    /** 切换当前页 */
    handleCurrentChange(val) {
      this.params.pageno = val
      this.getList()
    },
    handleEdit(index, row) {
      this.ordersObj = row
      this.showDialog = true
    },
    handleAdd() {
      this.ordersObj = {}
      this.showDialog = true
    },
    closeDialog() {
      this.ordersObj = {}
      this.showDialog = false
      this.getList()
    },
    handleDeleteRow(index, row) {
      const params = {
        id: row.id
      }
      this.$confirm('此操作将永久删除该订单, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteOrdersRecord(params).then(res => {
          if (res.head.ret === 0) {
            this.$message({
              type: 'success',
              message: '删除成功!'
            })
            this.getList()
          }
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
    }
  }
}
</script>
<style lang="scss" scoped>

</style>
