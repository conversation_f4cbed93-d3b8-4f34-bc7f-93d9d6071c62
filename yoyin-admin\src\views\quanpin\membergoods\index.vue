<!--
 * @Description: In User Settings Edit
 * @Author: your name
 * @Date: 2019-10-16 11:28:20
 * @LastEditTime: 2019-10-16 11:38:48
 * @LastEditors: Please set LastEditors
 -->
<template>
  <div class="app-container">
    <div class="table-query-container">
      <el-row>
        <el-col :span="3"><el-button v-permission="'btn-menuQuanpin-memgergoodsvip-edit'" @click="handleAdd">新增</el-button></el-col>
      </el-row>
    </div>
    <div class="dialog-container">
      <EditDialog :visiable="showDialog" :form-data="memberGoodsItem" @closeDialog="closeDialog" />
    </div>

    <div class="table-container">
      <el-table
        ref="multipleTable"
        :data="list"
        tooltip-effect="dark"
        style="width: 100%"
        border
      >
        <el-table-column
          type="index"
          width="50"
          align="center"
        />
        <el-table-column label="是否一次性">
          <template slot-scope="scope">{{ scope.row.onlyOnceFlag ? '是': '否' }}</template>
        </el-table-column>
        <el-table-column label="显示限时优惠">
          <template slot-scope="scope">{{ scope.row.showLimitFlag ? '是': '否' }}</template>
        </el-table-column>
        <el-table-column label="标题">
          <template slot-scope="scope">{{ scope.row.title }}</template>
        </el-table-column>
        <el-table-column label="副标题">
          <template slot-scope="scope">{{ scope.row.subTitle }}</template>
        </el-table-column>
        <el-table-column label="数量/单位">
          <template slot-scope="scope">{{ formatUnitAndAmount(scope.row) }}</template>
        </el-table-column>
        <el-table-column label="原价">
          <template slot-scope="scope">{{ scope.row.price }}</template>
        </el-table-column>
        <el-table-column label="优惠后">
          <template slot-scope="scope">{{ scope.row.realPrice }}</template>
        </el-table-column>
        <el-table-column label="排序">
          <template slot-scope="scope">{{ scope.row.sortNum }}</template>
        </el-table-column>
        <el-table-column label="操作" width="230" align="center">
          <template slot-scope="scope">
            <el-button size="mini" @click="handleEdit(scope.$index, scope.row)">编辑</el-button>
            <el-button
              v-permission="'btn-menuQuanpin-memgergoodsvip-edit'"
              size="mini"
              type="danger"
              @click="handleDeleteRow(scope.$index, scope.row)"
            >删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="pagination-container" style="margin-top:20px;">
      <el-pagination
        :current-page="params.pageNo"
        :page-sizes="[10, 20, 30, 40]"
        :page-size="params.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>
<script>
import { fetchListByPage, deleteRecord } from '@/api/quanpin/membergoods'
import EditDialog from './component/EditDialog'
// import AliyunOSS from '@/utils/aliyunOSS'
// const oss = new AliyunOSS()
export default {
  components: {
    EditDialog
  },
  data() {
    return {
      // 查询参数
      params: {
        pageNo: 1,
        pageSize: 10,
        type: ''
      },
      /** 列表 */
      list: [],
      /** 总条数 */
      total: 0,
      showDialog: false,
      fileList: [],
      memberGoodsItem: {}
    }
  },
  mounted() {
    this.getList()
  },
  methods: {
    formatUnitAndAmount(item) {
      if (item.unit === 'day') {
        return item.amount + '天'
      } else if (item.unit === 'month') {
        return item.amount + '月'
      } else if (item.unit === 'year') {
        return item.amount + '年'
      } else {
        return item.amount + '天'
      }
    },
    /** 获取列表 */
    async getList() {
      const res = await fetchListByPage(this.params)
      if (res.head.ret === 0) {
        this.list = res.data.result
        this.total = res.data.totalCount
      }
    },
    /** 切换每页条数 */
    handleSizeChange(val) {
      this.params.pageSize = val
      this.getList()
    },
    /** 切换当前页 */
    handleCurrentChange(val) {
      this.params.pageNo = val
      this.getList()
    },
    /** 编辑 */
    handleEdit(index, row) {
      this.memberGoodsItem = row
      this.showDialog = true
    },
    /** 删除单行 */
    async handleDeleteRow(index, row) {
      const params = {
        id: row.id
      }
      this.$confirm('此操作将永久删除该记录, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteRecord(params).then(res => {
          if (res.head.ret === 0) {
            this.$message({
              type: 'success',
              message: '删除成功!'
            })
            this.getList()
          }
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
    },
    /** 查询 */
    handleQuery() {
      this.getList()
    },
    /** 新增 */
    handleAdd() {
      this.memberGoodsItem = {
        unit: 'day',
        onlyOnceFlag: 'false',
        amount: 0,
        showLimitFlag: 'false'

      }
      this.showDialog = true
    },
    /** 关闭弹框 */
    closeDialog(code) {
      this.showDialog = false
      this.memberGoodsItem = {}
      // 保存code
      if (code === '1') {
        this.getList()
      }
    }
  }
}
</script>

<style lang="scss" scoped>

</style>
