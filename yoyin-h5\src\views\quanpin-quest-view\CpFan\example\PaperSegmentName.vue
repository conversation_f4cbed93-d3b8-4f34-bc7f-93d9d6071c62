<template>
  <div>
    <div v-if="ques.hasQues">
      <div class="big-exam-question" v-if="segmentName">
        <span class="big-ques-name"
          >{{ segmentName }}
          <span v-if="ques && ques.count"
            >（共 <strong class="num"> {{ ques.count }}</strong> 小题
            <span v-if="ques.showTypeScore">
              {{
                ques.perQuesScore
                  ? '，每小题' +
                    ques.perQuesScore +
                    '分，满分' +
                    ques.score +
                    '分'
                  : '，满分' + ques.score + '分'
              }}
            </span>
            ）
          </span></span
        >
        <span class="big-ques-lines">
          <span class="double-triangle"></span>
          <span class="dotted-line"></span>
        </span>
        <span class="big-ques-button" @click="onAddQuestionType()">
          <i class="iconfont iconadd">+</i>
          添加题型下试题
        </span>
      </div>
    </div>
    <div class="paper-segment" v-else>
      {{ segmentName || '' }}
    </div>
  </div>
</template>

<script>
  export default {
    name: 'paper-segment-name',
    props: {
      ques: Object
    },
    watch: {
      ques() {
        this.initSegmentName()
      }
    },
    data() {
      return {
        segmentName: ''
      }
    },
    mounted() {
      this.initSegmentName()
    },
    methods: {
      initSegmentName() {
        this.segmentName =
          this.ques.segmentName || this.ques.bigExamQuestionName || ''
        if (this.ques.isValid === 0) {
          this.segmentName = ''
        }
      },
      onAddQuestionType() {
        this.$emit('addQuestionType', this.ques)
      }
    },
    destroyed() {}
  }
</script>

<style scoped lang="scss"></style>
