<script>
import { TYPE_JUMP_APP, THROTTLE_TIME } from '@/consts/index'
import { deleteCommentById } from '../assets/js/api.js'
import throttle from 'lodash/throttle'
import Cookies from 'js-cookie'

let vm
const throttleTime = THROTTLE_TIME()
/**
 * 评论item
 */
export default {
  props: {
    totalSize: {
      type: Number
    },
    data: {
      type: Array
    }
  },
  watch: {
    totalSize (newValue, oldVaule) {
      this.totalCount = newValue
    }
  },
  data () {
    return {
      moduleKey: 'feed-detail',
      totalCount: this.totalSize,
      comments: this.data,
      clickIndex: -1
    }
  },
  async created () {
    this.$nextTick(() => {
      vm = this
      window.deleteComment = () => {
        window.setTimeout(() => {
          vm.deleteComment()
        }, 1)
      }
      window.addReplyCallBack = item => {
        this.addReplyCallBack(item)
      }
    })
  },
  computed: {
    isiosSystem() {
      if (window.webkit !== undefined && window.webkit.messageHandlers !== undefined) {
        return true
      } else {
        return false
      }
    }
  },
  methods: {

    launchPersonalHome: throttle((userId) => {
      if (window.StarPrinterJS) {
        window.StarPrinterJS.launchPersonalHome(userId)
      } else if (window.webkit !== undefined && window.webkit.messageHandlers !== undefined) {
        window.webkit.messageHandlers.launchPersonalHome.postMessage(userId)
      }
    }, throttleTime),
    // this.$t(`feed-detail.delete-tip`)
    deleteDialog: throttle((index) => {
      if (window.StarPrinterJS) {
        vm.clickIndex = index
        window.StarPrinterJS.showCommonDialog(
          vm.$t('feed-detail.delete-tip'),
          'deleteComment'
        )
      } else if (window.webkit !== undefined && window.webkit.messageHandlers !== undefined) {
        vm.clickIndex = index
        const parms = {
          'detailInfo': vm.$t('feed-detail.delete-tip'),
          'callback': 'deleteComment'
        }
        window.webkit.messageHandlers.showCommonDialog.postMessage(JSON.stringify(parms))
      }
    }, throttleTime),

    async deleteComment () {
      if (this.clickIndex > -1) {
        const commentItem = this.comments[this.clickIndex]
        const { head, data } = await deleteCommentById(commentItem.commentId)
        if (head.ret === 0) {
          this.comments.splice(this.clickIndex, 1)
          if (this.totalCount > 0) {
            this.totalCount--
          }
          this.clickIndex = -1
          this.$toast(this.$t(`feed-detail.delete-success`))
        } else {
          this.$toast(head.msg)
        }
      }
    },

    /**
     * 回复
     */
    addReply: throttle((item, commentIndex) => {
      if (window.StarPrinterJS) {
        // 指定回复成功后回调方法名称
        vm.clickIndex = commentIndex
        item.callBackName = 'addReplyCallBack'
        window.StarPrinterJS.launchCommentAdd(1, JSON.stringify(item))
      } else if (window.webkit !== undefined && window.webkit.messageHandlers !== undefined) {
        vm.clickIndex = commentIndex
        item.callBackName = 'addReplyCallBack'
        const parms = {
          'type': 1,
          'detailData': item
        }
        window.webkit.messageHandlers.launchCommentAdd.postMessage(JSON.stringify(parms))
        // window.webkit.messageHandlers.launchCommentAdd.postMessage([1, JSON.stringify(item)])
      }
    }, 350),

    addReplyCallBack (comment) {
      try {
        if (typeof comment === 'string') {
          comment = JSON.parse(comment)
        }
        // let d = new Date()
        // let myHours = d.getHours()
        // let myMinutes = d.getMinutes()
        // let myHoursStr = (myHours < 10 ? '0' : '') + myHours
        // let myMinutesStr = (myMinutes < 10 ? '0' : '') + myMinutes
        // comment['fmtTime'] = myHoursStr + ':' + myMinutesStr
        const fmtTime = this.comments[this.clickIndex].fmtTime
        comment['fmtTime'] = fmtTime

        this.comments.splice(this.clickIndex, 1, comment)
        this.clickIndex = -1
      } catch (error) {
        console.log(error)
      }
    },
    /** 版本号 > 3.1.0 头像头衔用最新的 */
    isNewerThan310() {
      try {
        if (window.StarPrinterJS && typeof (window.StarPrinterJS.getAppVersion) === 'function') {
          const version = window.StarPrinterJS.getAppVersion()
          const arr = version.split('.')
          if (parseInt(arr[0]) < 3) {
            return false
          } else if (parseInt(arr[0]) === 3 && parseInt(arr[1]) < 2) {
            return false
          } else {
            return true
          }
        } else if (this.isiosSystem) {
          const version = Cookies.get('appVersion')
          const arr = version.split('.')
          if (parseInt(arr[0]) < 3) {
            return false
          } else if (parseInt(arr[0]) === 3 && parseInt(arr[1]) < 2) {
            return false
          } else {
            return true
          }
        }
      } catch (error) {
        return false
      }
      return false
    },
    /**
     * 是否为当前用户
     */
    isMySelf (userId) {
      if (window.StarPrinterJS) {
        const currentUserId = window.StarPrinterJS.getCurrentUserId()
        return currentUserId && currentUserId === userId
      } else if (this.isiosSystem) {
        const currentUserId = Cookies.get('userId')
        return currentUserId && currentUserId === userId
      }
      return false
    },

    /**
     * 跳转至评论详情
     */
    jumpToCommentDetail: throttle((commentId) => {
      if (window.StarPrinterJS) {
        const url = `${window.location.href
          .replace(window.location.search, '')
          .replace('label-detail-old', 'reply-list')
          .replace('label-detail', 'reply-list')}?commid=${commentId}&from=feed`

        window.StarPrinterJS.jumpToApp(TYPE_JUMP_APP.H5, url)
      } else if (window.webkit !== undefined && window.webkit.messageHandlers !== undefined) {
        const url = `${window.location.href
          .replace(window.location.search, '')
          .replace('label-detail-old', 'reply-list')
          .replace('label-detail', 'reply-list')}?commid=${commentId}&from=feed`
        const parms = {
          'type': TYPE_JUMP_APP.H5,
          'url': url
        }
        window.webkit.messageHandlers.jumpToApp.postMessage(JSON.stringify(parms))
      }
    }, throttleTime)
  }
}
</script>

<template>
  <div class="comment-list">
    <div class="title">
      {{$t(`feed-detail.comment`)}}{{comments===null ? 0 : comments.length}}
    </div>
    <div class="comment-empty" v-if="totalCount===0">{{$t(`feed-detail.no-comments`)}}~</div>

    <div class="comment-item" @click.stop="addReply(comment,commentIndex)" :key="commentIndex" v-if="comments.length>0" v-for="(comment,commentIndex) in comments">
      <img class="comment-userpic" :src="comment.commentUserPic" @click.stop="launchPersonalHome(comment.commentUserId)" />
      <img class="comment-userpic-official" v-if="isNewerThan310() && comment.userTitleObj && comment.userTitleObj.borderUrl" :src="comment.userTitleObj.borderUrl" @click.stop="launchPersonalHome(comment.commentUserId)"/>
      <img class="comment-userpic-official" v-if="!isNewerThan310() && comment.isOfficial==1" src="../assets/img/community_ic_official.png" @click="launchPersonalHome"/>
      <div style="width: 100%;padding-left:4.25rem;padding-top:0.875rem;"">
        <div style="display:flex;">
          <div class="comment-name">{{comment.commentUserName}}</div>
          <div style="flex:1"></div>
          <div class="comment-menu"><img src="../assets/img/community_ic_comment.png" /></div>
          <div class="comment-menu" @click.stop="deleteDialog(commentIndex)" v-if="isMySelf(comment.commentUserId)">
            <img src="../assets/img/community_ic_delete.png" />
          </div>
        </div>
        <div class="comment-time">{{comment.fmtTime}}</div>
        <div class="comment-content">{{comment.content}}</div>

        <div class="reply-box" v-if="comment.commentReplyDto && comment.commentReplyDto.length>0" @click.stop="jumpToCommentDetail(comment.commentId)">
          <div class="reply-item" v-for="(item,replyIndex) in comment.commentReplyDto" :key="replyIndex">
            <span class="reply-name" @click.stop="launchPersonalHome(item.replyUserId)">{{item.replyUserName}}</span><span v-if="item.replyObjName">{{$t(`feed-detail.reply`)}}</span><span class="reply-name" v-if="item.replyObjName" @click.stop="launchPersonalHome(item.replyObjId)">{{item.replyObjName}}</span><span class="replay-name">：</span>{{item.content}}
          </div>
          <div class="reply-item" style="display:flex" v-if="comment&&comment.replyNum>3">{{$t(`feed-detail.view-all`)}}{{comment.replyNum}}{{$t(`feed-detail.comments`)}}
            <div class="more-img"></div>
          </div>
        </div>

      </div>

    </div>
  </div>
</template>

<style lang="scss">
.comment-list {
  font-size: 0.875rem;
  padding: 0 0.3rem;
  margin-top: 0.75rem;
  margin-left: 0.75rem;
  margin-right: 0.75rem;
  background: white;
  border-radius: 10px;
  .title {
    width: 100%;
    height: 3.125rem;
    line-height: 3.125rem;
    padding-left: 0.7rem;
    color: black;
  }
  .comment-empty {
    width: 100%;
    padding-top: 40px;
    padding-bottom: 40px;
    text-align: center;
    color: #999;
    border-top: 0.5px solid #e6e6e6;
  }

  .comment-item {
    display: flex;
    border-top: 0.5px solid #e6e6e6;
    // padding-top: 12px;
    padding-bottom: 16px;
    padding-right: 0.7rem;
    .comment-userpic {
        height: 2.45rem;
        width: 2.45rem;
        margin: 0.88rem 0.78rem 0.78rem 0.78rem;
        border-radius: 30px;
        position: absolute;
    }

    .comment-userpic-official {
      width: 3.2727272725rem;
      height: 3.2727272725rem;
      margin: 0.3rem;
      position: absolute;
    }

    .comment-menu {
      img {
        width: 1.25rem;
        height: 1.25rem;
      }
      padding-left: 0.5rem;
      padding-right: 0.5rem;
      padding-bottom: 5px;
    }

    .comment-name {
      font-size: 1rem;
      line-height: 1rem;
      color: black;
    }

    .comment-time {
      font-size: 0.75rem;
      line-height: 0.5rem;
      color: #bcbbbf;
      margin-top: 0.5rem;
    }

    .comment-content {
      color: black;
      margin-top: 0.8125rem;
      word-break: break-all;
    }

    .reply-box {
      width: 100%;
      padding: 0.375rem 0.75rem;
      background: #F5F5F5;
      border-radius: 4px;
      margin-top: 0.75rem;
      color: #8e8e93;
      font-size: 0.75rem;
      font-weight: 400;
      .reply-item {
        margin: 0.25rem 0;
        .more-img {
          width: 0.75rem;
          height: 0.75rem;
          min-width: 0.75rem;
          min-height: 1.25rem;
          background: url('../assets/img/common_ic_narrows.png');
          background-size: 75%;
          background-repeat: no-repeat;
          background-position: 0 48%;
        }
        span {
          color: #8e8e93;
          margin: 0 0.25rem;
        }
        .reply-name {
          color: #000000;
          font-weight: bold;
          margin: 0;
        }
      }
    }
  }
}
</style>
