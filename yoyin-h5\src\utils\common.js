import each from 'lodash/each'

import axios from 'axios'
import Cookies from 'js-cookie'
// import throttle as lThrottle from 'lodash/throttle'
const lThrottle = require('lodash/throttle')
import {
  TOKEN_KEY,
  KEY_LOGIN_WAY,
  KEY_LOGIN_ACCOUNT,
  THIRD_AUTH_INFO
} from '@/consts/index'

/**
 * 保存第三方授权信息
 * @param {Object} info 第三方授权信息
 */
export const setThirdAuthInfo = info => {
  Cookies.set(THIRD_AUTH_INFO, info)
}

/**
 * 获取第三方授权信息
 */
export const getThirdAuthInfo = () => {
  const str = Cookies.get(THIRD_AUTH_INFO)
  const obj = str ? JSON.parse(str) : undefined
  return obj
}

/**
 * 获取TokenId
 */
export const getTokenId = () => {
  const tokenid = Cookies.get(TOKEN_KEY)
  return tokenid
}

/**
 * 设置tokenid
 * @param {string} tokenid tokenid
 */
export const setTokenId = tokenid => Cookies.set(TOKEN_KEY, tokenid)
export const refreshToken = tokenid => {
  if (!tokenid) return
  setTokenId(tokenid) // 更新用户tokenid
  return tokenid
}
/**
 * 获取登录方式
 */
export const getLoginway = () => {
  const query = getUrlParameter()
  if (query && query[KEY_LOGIN_WAY]) {
    setLoginway(query[KEY_LOGIN_WAY])
  }
  const loginway = Cookies.get(KEY_LOGIN_WAY)
  return loginway
}

/**
 * 设置登录方式
 * @param {string} loginway 登录方式
 */
export const setLoginway = loginway => Cookies.set(KEY_LOGIN_WAY, loginway)

/**
 * 获取登录账号
 */
export const getLoginAccount = () => {
  const query = getUrlParameter()
  if (query && query[KEY_LOGIN_ACCOUNT]) {
    setLoginAccount(query[KEY_LOGIN_ACCOUNT])
  }
  const account = Cookies.get(KEY_LOGIN_ACCOUNT)
  return account
}

/**
 * 设置登录账号
 * @param {string} loginAccount 登录方式
 */
export const setLoginAccount = loginAccount =>
  Cookies.set(KEY_LOGIN_ACCOUNT, loginAccount)

/**
 * 获取语系
 */
export const getLanguage = () => {
  let locale = 'zh_CN'
  if (window.StarPrinterJS && window.StarPrinterJS.getLanguage()) {
    locale = window.StarPrinterJS.getLanguage()
  } else if (window.webkit && window.webkit.messageHandlers) {
    locale = Cookies.get('language')
    // if (locale) {
    //   alert('language=                                ' + JSON.stringify(locale))
    // } else {
    //   alert('没办法获取cookies中的语言')
    // }
    // alert(locale)
    // locale = window.webkit.messageHandlers.getLanguage.postMessage(null)
  } else {
    const params = getUrlParameter()
    locale = params && params.language
  }
  return locale
}

/**
 * 获取地址参数
 *
 * @param {string} data N 链接
 * @returns JSON
 */
export const getUrlParameter = data => {
  let query = {}
  let url = data
    ? data.substr(data.indexOf('?') + 1)
    : document.location.search.replace('?', '')
  let urlParams = []
  if (url) {
    urlParams = url.split('&')
    urlParams.forEach(item => {
      let value = item.split('=')
      query[value[0]] = value[1]
    })
  }
  return query
}

/**
 * 获取请求API HOST
 */
const getApiHost = (() => {
  const query = getUrlParameter()
  return query.apiHost || 'api'
})()

/**
 * 是否APP客户端打开页面
 */
export const isAppClient = (() => {
  if (window.StarPrinterJS || (window.webkit && window.webkit.messageHandlers)) {
    return true
  } else {
    return false
  }
})()

export const appJS = () => {
  return window.StarPrinterJS
}

/**
 * 获取host
 */
export const host = `https://${getApiHost}.sythealth.com`

function getAllowHost() {
  let allowhost = 5
  let hostname = window.location.hostname
  switch (hostname) {
    case 'test.sythealth.com':
      allowhost = 3
      break

    case 'm.sythealth.com':
      allowhost = 2
      break
  }
  return allowhost
}

export const tokenid = 'tokenid_undefined'
export const allowhost = getAllowHost()

/**
 * 事件统计
 * @param {string} eventid  事件ID
 * @param {*} id 自定义事件
 */
export const statisticsFireye = (eventid, id) => {
  let data = {
    tokenid: tokenid,
    allowhost: allowhost,
    eventid: eventid
  }
  let userid = getUrlParameter().userid
  if (userid) {
    data.userid = userid
  }
  if (id) {
    data.id = id
  }
  axios.get('https://fireye.sythealth.com/ws/fireye/v2/event/saveevent', {
    params: data
  })
}

// 判断有无eventId事件统计
;(() => {
  const query = getUrlParameter()
  const eventId = query.eventId || query.eventid || query.event_id
  if (eventId) {
    statisticsFireye(eventId)
  }
})()

/**
 * 拼接参数
 *
 * @param {Object} data Y 参数对象
 * @returns string
 */
export const setUrlParameter = data => {
  let string = ''
  each(data, (item, key) => {
    string += '&' + key + '=' + item
  })
  return '?' + string.replace('&', '')
}

/**
 * 返回App版本
 *
 * @returns 版本号
 */
export const getAppVersion = () => {
  if (!getUrlParameter('appversion')) return 0

  var version = getUrlParameter('appversion').split('.')
  var _version = ['0', '0', '0']

  each(version, function(index, s) {
    _version[index] = version[index]
  })

  var str = ''

  each(_version, function(index, s) {
    str += s
  })

  version = parseInt(str)

  return version
}

/**
 * 获取手机操作系统
 */
export const getMobileOperatingSystem = () => {
  var userAgent = navigator.userAgent || navigator.vendor || window.opera

  if (
    userAgent.match(/iPad/i) ||
    userAgent.match(/iPhone/i) ||
    userAgent.match(/iPod/i)
  ) {
    return 'iOS'
  } else if (userAgent.match(/Android/i)) {
    return 'Android'
  } else {
    return 'unknown'
  }
}

export const jumpPage = url => {
  const body = document.body
  const a = document.createElement('a')
  a.href = url
  a.style.display = 'none'
  body.appendChild(a)

  const event = document.createEvent('MouseEvents')
  event.initEvent('click', true, true)
  a.dispatchEvent(event)
  body.removeChild(a)
}

export const browserInfo = () => {
  const u = navigator.userAgent
  return {
    isMobile: !!u.match(/AppleWebKit.*Mobile.*/), // 是否为移动终端
    isiOS: !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/), // ios终端
    isAndroid: u.indexOf('Android') > -1 || u.indexOf('Adr') > -1, // android终端
    isiPhone: u.indexOf('iPhone') > -1, // 是否为iPhone或者QQHD浏览器
    isiPad: u.indexOf('iPad') > -1, // 是否iPad
    isWeixin: u.indexOf('MicroMessenger') > -1, // 是否微信 （2015-01-22新增）
    isQQ: u.match(/\sQQ/i) === 'qq' // 是否QQ
  }
}
const browser = browserInfo()
export const throttle = (func, time = 200) => {
  if (!browser && browser.isAndroidr) {
    lThrottle(func, time)
  } else {
    console.log('直接执行')
    return func
  }
}
