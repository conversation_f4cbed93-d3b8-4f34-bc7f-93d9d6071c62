<!--
 * @Description: In User Settings Edit
 * @Author: your name
 * @Date: 2019-10-16 11:28:20
 * @LastEditTime: 2019-10-22 17:03:31
 * @LastEditors: Please set LastEditors
 -->
<template>
  <div class="app-container">
    <div class="control-container">
      <el-button type="primary" @click="queryRole">查询</el-button>
      <el-button v-permission="'btn-menuOther-role-edit'" @click="addRole">添加</el-button>
    </div>
    <div class="table-container">
      <el-table
        ref="multipleTable"
        :data="tableList"
        tooltip-effect="dark"
        style="width: 100%"
        border
      >
        <el-table-column label="角色名称">
          <template slot-scope="scope">{{ scope.row.roleName }}</template>
        </el-table-column>
        <el-table-column prop="roleCode" label="角色编码" />
        <el-table-column label="操作" fixed="right">
          <template slot-scope="scope">
            <el-button size="mini" @click="handleEdit(scope.$index, scope.row)">编辑</el-button>
            <el-button v-permission="'btn-menuOther-role-edit'" size="mini" type="success" @click="handleAuthoriza(scope.$index, scope.row)">授权</el-button>
            <el-button
              v-show="scope.row.account!=='xytAdmin'"
              v-permission="'btn-menuOther-role-edit'"
              size="mini"
              type="danger"
              @click="handleDeleteRow(scope.$index, scope.row)"
            >删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="pagination-container" style="margin-top:20px;">
      <el-pagination
        :current-page="currentPage"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="currentSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
    <!-- 弹框 -->
    <div class="dialog-container">
      <el-dialog
        :title="form.id === '' ? '新增' : '编辑' "
        :visible.sync="showDialog"
        width="50%"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        center
      >
        <el-form
          ref="ruleForm"
          :model="form"
          :rules="rules"
          label-width="100px"
          class="demo-ruleForm"
        >
          <el-form-item label="角色名称" prop="roleName">
            <el-input v-model="form.roleName" />
          </el-form-item>
          <el-form-item label="角色编号" prop="roleCode">
            <el-input v-model="form.roleCode" />
          </el-form-item>
          <el-form-item>
            <el-button @click="showDialog = false">返回</el-button>
            <el-button v-permission="'btn-menuOther-role-edit'" type="primary" :loading="loading" @click="submitForm">保存</el-button>
          </el-form-item>
        </el-form>
      </el-dialog>
    </div>

    <div class="dialog-container">
      <el-dialog
        :title="'授权'"
        :visible.sync="showDialogResource"
        width="50%"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        center
      >
        <el-form
          label-width="100px"
          class="demo-ruleForm"
        >
          <el-form-item label="权限点" prop="roleName">
            <el-tree
              ref="tree"
              :default-checked-keys="defaultSelectData"
              :data="resTree"
              show-checkbox
              :default-expand-all="false"
              node-key="id"
              :check-strictly="true"
              highlight-current
              :props="defaultProps"
            />

          </el-form-item>
          <el-form-item>
            <el-button @click="showDialogResource = false">返回</el-button>
            <el-button v-permission="'btn-menuOther-role-edit'" type="primary" :loading="loading" @click="submitFormAuth">保存</el-button>
          </el-form-item>
        </el-form>
      </el-dialog>
    </div>
  </div>
</template>
<script>
import { fetchListByPage, saveRecord, deleteRecord, findResourcesListByRoleId, disResources } from '@/api/auth/role'
// import AliyunOSS from '@/utils/aliyunOSS'
// const oss = new AliyunOSS()
export default {
  data() {
    return {
      currentPage: 1,
      currentSize: 10,
      total: 0,
      tableList: [],
      showDialogResource: false,
      multipleSelection: [],
      // 弹框
      showDialog: false,
      // 批量
      showMuliDialog: false,
      // 仅上传文件模式
      onlyUpload: false,
      // 表单
      form: {
        id: '',
        channel: '',
        url: '',
        param: '',
        version: '',
        remark: ''
      },
      resTree: [],
      defaultSelectData: [],
      // 表单
      multiForm: {
        version: '',
        remark: ''
      },
      // 检验规则
      rules: {
        channel: [
          { required: true, message: '请输入渠道', trigger: 'blur' },
          { min: 3, max: 20, message: '长度在 3 到 20 个字符', trigger: 'blur' }
        ],
        url: [{ required: true, message: '请选择文件', trigger: 'blur' }],
        version: [
          { required: true, message: '请输入版本号如1.1.2', trigger: 'blur' }
        ]
      },
      multiFormRules: {
        version: [
          { required: true, message: '请输入版本号如1.1.2', trigger: 'blur' }
        ]
      },
      fileList: [],
      file: undefined,
      loading: false,
      showUrl: false,
      defaultProps: {
        children: 'resourceDtoList',
        label: 'resName'
      }
    }
  },

  computed: {},
  mounted() {
    this.getList()
  },
  methods: {
    async handleAuthoriza(index, row) {
      this.roleId = row.id
      const parms = {
        roleId: row.id
      }
      const res = await findResourcesListByRoleId(parms)
      if (res.head.ret === 0) {
        this.resTree = res.data.treeData
        this.defaultSelectData = res.data.selected
        this.showDialogResource = true
      }
    },
    async submitFormAuth() {
      const selelectTreeNode = this.$refs.tree.getCheckedKeys()
      const resIds = selelectTreeNode.join(',')
      const params = {
        roleId: this.roleId,
        resIds: resIds
      }
      const res = await disResources(params)
      if (res.head.ret === 0) {
        this.$message({
          type: 'success',
          message: '删除成功!'
        })
        this.getList()
        this.showDialogResource = false
      }
    },
    /** 添加 */
    addRole() {
      this.form = {}
      this.showDialog = true
      this.fileList = []
      this.file = undefined
      this.showUrl = false
      this.onlyUpload = false
    },
    /** 查询 */
    queryRole() {
      this.getList()
    },
    /** 切换每页条数 */
    handleSizeChange(val) {
      console.log(`每页 ${val} 条`)
      this.currentSize = val
      this.getList()
    },
    /** 切换当前页 */
    handleCurrentChange(val) {
      console.log(`当前页: ${val}`)
      this.currentPage = val
      this.getList()
    },
    /** 获取表数据 */
    async getList() {
      const params = {
        pageno: this.currentPage,
        pagesize: this.currentSize
      }
      const res = await fetchListByPage(params)
      if (res.head.ret === 0) {
        const data = res.data
        this.total = data.totalCount
        this.tableList = data.result
      }
    },
    clearSelection() {
      this.$refs.multipleTable.clearSelection()
    },
    /** 勾选事件 */
    handleSelectionChange(val) {
      this.multipleSelection = val
      console.log('handleSelectionChange: ' + this.multipleSelection)
    },
    /** 编辑 */
    handleEdit(index, row) {
      this.form = row
      this.showDialog = true
      this.fileList = []
      this.file = undefined
      this.showUrl = true
      this.onlyUpload = false
    },
    /** 编辑 仅上传文件 */
    handleOnlyFile(index, row) {
      this.form = row
      this.showDialog = true
      this.fileList = []
      this.file = undefined
      this.showUrl = true
      this.onlyUpload = true
    },

    /** 删除单行 */
    async handleDeleteRow(index, row) {
      const params = {
        id: row.id
      }
      this.$confirm('此操作将永久删除该记录, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          deleteRecord(params).then(res => {
            if (res.head.ret === 0) {
              this.$message({
                type: 'success',
                message: '删除成功!'
              })
              this.getList()
            }
          })
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    },
    /** 删除单行 */
    async handleMultiDel() {
      this.$confirm('此操作将永久删除记录, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          let i = 0
          const len = this.multipleSelection.length
          for (i = 0; i < len; i++) {
            // const params = {
            //   id: this.multipleSelection[i].id
            // }
          }
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    },

    // 表单编辑
    /** 保存 */
    submitForm() {
      this.$refs['ruleForm'].validate(valid => {
        if (valid) {
          this.loading = true
          console.log(this.file)
          if (!this.file) {
            this.saveConfig()
            return
          }
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    async saveConfig() {
      const res = await saveRecord(this.form)
      if (res.head.ret === 0) {
        this.$message.success('保存成功')
      } else {
        this.$message.error(res.head.msg)
        this.loading = false
        return
      }
      this.loading = false
      this.showDialog = false
      this.onlyUpload = false
      this.getList()
    },
    /** 点击批量修改 */
    handleMultiModify() {
      this.showMuliDialog = true
    },
    /** 批量保存 */
    submitMultiForm() {
      this.$refs['ruleMultiForm'].validate(valid => {
        if (valid) {
          this.loading = true
          console.log('this.multiForm: ', this.multiForm)
          this.doMultiSave()
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    async doMultiSave() {
      let i = 0
      const len = this.multipleSelection.length
      let count = 0
      for (i = 0; i < len; i++) {
        const data = { ...this.multipleSelection[i] }
        data.version = this.multiForm.version
        data.remark = this.multiForm.remark
        const res = await saveRecord(data)
        if (res.head.ret === 0) {
          count++
        }
      }
      this.$message.success('保存成功[' + count + ']条, 共' + len + '条')
      this.loading = false
      this.showMuliDialog = false
      this.getList()
    },

    /** 移除之前 */
    handleRemove(file, fileList) {
      console.log('handleRemove')
      this.showUrl = true
    },
    /** 上传前 */
    beforeUpload(file) {
      return false
    },
    changeFile(file) {
      console.log('changeFile')
      console.log(file)
      this.file = file.raw
      this.fileList.pop()
      this.fileList.push({
        name: file.name,
        url: file.name
      })
      this.showUrl = false
    }
  }
}
</script>

<style lang="scss" scoped>
.app-container {
  margin: 20px;
  .control-container {
    margin-bottom: 20px;
  }
  .choose-container {
    margin-bottom: 20px;
    background-color: #e6f7ff;
    border: 1px solid #91d5ff;

    span {
      margin-left: 20px;
      line-height: 48px;
    }
    a {
      margin-left: 20px;
      color: #1890ff;
      text-decoration: none;
      background-color: transparent;
      outline: none;
      cursor: pointer;
      transition: color 0.3s;
    }
  }
}
</style>
