<template>
  <div v-if="visiable" class="edit-dialog">
    <el-dialog
      ref="dialog"
      :show-close="false"
      :title="form.id === '' ? '新增' : '编辑' "
      :visible.sync="visiable"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      width="50%"
      center
    >
      <el-form ref="form" :model="form" :rules="rules" label-width="130px" class="edit-form">
        <el-form-item label="封面" prop="coverUrl">
          <el-upload
            class="avatar-uploader"
            action=""
            :show-file-list="false"
            :on-change="changeHead"
            :before-upload="beforeUpload"
            accept="image/png, image/jpeg"
          >
            <img v-if="form.coverUrl" :src="form.coverUrl" class="avatar">
            <i v-else class="el-icon-plus avatar-uploader-icon" />
          </el-upload>
          <img v-show="false" :src="testUrl" class="avatar">
        </el-form-item>
        <el-form-item label="商品名称" prop="name">
          <el-input v-model="form.name" />
        </el-form-item>
        <el-form-item label="排序值" prop="sortNum">
          <el-input v-model="form.sortNum" />
        </el-form-item>
        <el-row>
          <el-col :span="8">
            <el-form-item label="兑换积分" prop="costNum">
              <el-input v-model="form.costNum" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="库存" prop="stockNum">
              <el-input v-model="form.stockNum" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="是否新品" prop="isNews">
              <el-radio-group v-model="form.isNews">
                <el-radio-button label="0">否</el-radio-button>
                <el-radio-button label="1">是</el-radio-button>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="类型" prop="type">
          <el-radio-group v-model="form.type">
            <el-radio-button label="0">商品</el-radio-button>
            <!-- <el-radio-button label="1">头像</el-radio-button> -->
            <el-radio-button label="2">字体</el-radio-button>
            <el-radio-button label="3">文具</el-radio-button>
            <el-radio-button label="4">打印机</el-radio-button>
            <el-radio-button label="9">其他</el-radio-button>
          </el-radio-group>
        </el-form-item>
        <el-row>
          <el-col :span="8">
            <el-form-item label="兑换限制" prop="changeNum">
              <el-input v-model="form.changeNum" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="价钱(含单位)" prop="price">
              <el-input v-model="form.price" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="积分和现金模式" prop="canOffset">
              <el-radio-group v-model="form.canOffset">
                <el-radio-button label="0">否</el-radio-button>
                <el-radio-button label="1">是</el-radio-button>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="第三方商城链接" prop="redirectUrl">
          <el-input v-model="form.redirectUrl" />
        </el-form-item>
        <el-form-item v-if="form.type=='1'" label="挂件编号" prop="code">
          <el-input v-model="form.code" />
        </el-form-item>
        <el-form-item v-if="form.type==2" label="字体文件：">
          <el-upload
            action=""
            accept=".ttf,.eot,.otf,.woff,.svg"
            :auto-upload="false"
            :show-file-list="false"
            :on-change="onChangeFont"
          >
            <div slot="tip"><a style="color:#409EFF" :href="form.url" target="_blank">{{ form.url }}</a> </div>
            <el-button type="primary" size="small">上传字体文件</el-button>
          </el-upload>
        </el-form-item>
        <el-form-item label="列表展示缩略图" prop="fontListUrl">
          <el-upload
            class="avatar-uploader"
            action=""
            :show-file-list="false"
            :on-change="changeHeadFontListUrl"
            :before-upload="beforeUploadFontListUrl"
            accept="image/png, image/jpeg"
          >
            <img v-if="form.fontListUrl" :src="form.fontListUrl" class="avatar">
            <i v-else class="el-icon-plus avatar-uploader-icon" />
          </el-upload>
          <img v-show="false" :src="testUrlFontListUrl" class="avatar">
        </el-form-item>
        <el-form-item label="商品描述" prop="printNum">
          <el-input v-model="form.info" type="textarea" :rows="4" placeholder="商品描述" :maxlength="200" show-word-limit />
        </el-form-item>
        <el-form-item label="商品图片">
          <el-upload
            action=""
            :multiple="true"
            :http-request="myUploadImageMethod"
            list-type="picture-card"
            :on-remove="handleRemove"
            :file-list="fileList"
            :auto-upload="true"
            accept="image/png, image/jpeg"
          >
            <i class="el-icon-plus" />
          </el-upload>
        </el-form-item>
        <el-form-item>
          <el-button @click="handleClose">返回</el-button>
          <el-button v-permission="'btn-menuCoin-goods-edit'" type="primary" @click="submit">保存</el-button>
          <!-- <el-button type="primary" @click="sortByFileName">自动排序</el-button>
          <el-button type="primary" @click="showDialog=true">排序与设置</el-button> -->
        </el-form-item>
      </el-form>
    </el-dialog>

    <el-dialog
      ref="dialogxxx"
      :show-close="true"
      :title="'文件列表编辑'"
      :visible.sync="showDialog"
      width="50%"
      center
    >
      <el-table
        ref="multipleTable"
        :data="fileList"
        tooltip-effect="dark"
        style="width: 100%"
        border
      >
        <el-table-column label="缩略图">
          <template slot-scope="scope">
            <a :href="scope.row.pic" target="_blank">
              <el-avatar shape="square" :size="80" :src="scope.row.pic" />
            </a>
          </template>
        </el-table-column>
        <el-table-column label="图片名称">
          <template slot-scope="scope">
            {{ scope.row.name }}
          </template>
        </el-table-column>
        <el-table-column label="是否打印">
          <template slot-scope="scope">
            <el-radio-group v-model="scope.row.printFlag">
              <el-radio-button label="0">不打印</el-radio-button>
              <el-radio-button label="1">打印</el-radio-button>
            </el-radio-group>
          </template>
        </el-table-column>
        <el-table-column label="不分享能否阅读">
          <template slot-scope="scope">
            <el-radio-group v-model="scope.row.canRead">
              <el-radio-button label="0">不能</el-radio-button>
              <el-radio-button label="1">能</el-radio-button>
            </el-radio-group>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="160">
          <template slot-scope="scope">
            <el-button type="primary" icon="el-icon-top" circle @click="move(scope.$index, -1)" />
            <el-button type="primary" icon="el-icon-bottom" circle @click="move(scope.$index, 1)" />
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
  </div>
</template>

<script>
import { saveGoodsRecord } from '@/api/coin/goods'
import AliyunOSS from '@/utils/aliyunOSS2'
// import TinyMce from '@/components/Tinymce/index'
const oss = new AliyunOSS()

export default {
  name: 'EditDialog',
  // components: { TinyMce },
  props: {
    visiable: {
      type: Boolean,
      required: true,
      default: false
    },
    formData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      /** 表单 */
      form: {
        pics: []
      },
      /** 校验规则 */
      rules: {
        title: [
          { required: true, message: '请输入标题', trigger: 'blur' }
        ],
        headUrl: [
          { required: true, message: '请输入选择图片', trigger: 'blur' }
        ]
      },
      /** 待上传的文件 */
      upFile: undefined,
      upFile2: undefined,
      testUrl: '',
      testUrlFontListUrl: '',
      /** 时间格式 */
      timeFormat: 'yyyy-MM-dd HH:mm:ss',
      fileList: [],
      preUploadList: [],
      showDialog: false,
      subjectList: [],
      checkboxGroup1: [],
      /** 字体文件 */
      fontFile: {
        name: '',
        url: '',
        file: undefined
      }
    }
  },
  computed: {
  },
  watch: {
    formData: function(val) {
      this.upFile = undefined
      this.form = { ...val }
      this.fileList = []
      if (this.form.pics) {
        this.form.pics.forEach(element => {
          const fileObj = {
            name: element.name,
            url: element.url,
            size: element.size,
            pic: element.pic,
            printFlag: element.printFlag,
            canRead: element.canRead
          }
          this.fileList.push(fileObj)
        })
      } else {
        this.form.pics = []
        this.fileList = []
      }
      if (this.form.url) {
        this.fontFile.url = this.form.url
      }
    },
    visiable: function(val) {
      if (val) {
        oss.initClient2()
      }
    }
  },
  mounted() {
    this.show = this.visiable
  },
  methods: {
    /** 上传字体文件前 */
    onChangeFont(file) {
      oss.uploadFile(file.raw, (result, error) => {
        if (error) {
          this.$message.error('上传文件失败')
          return -1
        }
        if (result) {
          this.form.url = result.name
        }
      })
    },
    init() {
      oss.initClient2()
    },
    move(index, action) {
      if (action === 1) {
        if (index !== this.fileList.length - 1) {
          this.fileList[index] = this.fileList.splice(index + 1, 1, this.fileList[index])[0]
        }
      } else {
        if (index !== 0) {
          this.fileList[index] = this.fileList.splice(index - 1, 1, this.fileList[index])[0]
        }
      }
    },
    /** 返回 */
    handleClose(code) {
      this.$emit('closeDialog', code)
    },
    /** 选择图片*/
    changeHead(file) {
      this.upFile = file.raw
      this.form.coverUrl = URL.createObjectURL(file.raw)
      this.testUrl = URL.createObjectURL(file.raw)
      return false
    },
    changeHeadFontListUrl(file) {
      this.upFile2 = file.raw
      this.form.fontListUrl = URL.createObjectURL(file.raw)
      this.testUrlFontListUrl = URL.createObjectURL(file.raw)

      oss.uploadFile(file.raw, (result, error) => {
        if (error) {
          this.$message.error('上传文件失败')
          return -1
        }
        if (result) {
          this.form.fontListUrl = result.name
          this.testUrlFontListUrl = result.name
        }
      })

      return false
    },
    /** 上传前 */
    beforeUpload(file) {
      return false
    },
    /** 上传前 */
    beforeUploadFontListUrl(file) {
      return false
    },
    /** 上传pic时 */
    async changeMyPics(file) {
      console.log('我要上传文件：', file)
      if (file.pic) {
        return
      }
      oss.uploadFile(file.raw, (result, error) => {
        if (error) {
          this.$message.error('上传文件失败')
          return -1
        }
        if (result) {
          const fileObj = {
            name: file.name,
            url: result.name,
            size: file.size,
            pic: result.name,
            printFlag: 1,
            canRead: 0
          }
          this.form.pics.push(fileObj)
          this.fileList.push(fileObj)
        }
      })
      // console.log(this.form.pics)
      return false
    },
    async myUploadImageMethod(parms) {
      console.log(parms)
      oss.uploadFile(parms.file, (result, error) => {
        if (error) {
          this.$message.error('上传文件失败')
          return -1
        }
        if (result) {
          const fileObj = {
            name: parms.file.name,
            url: result.name,
            size: parms.file.size,
            pic: result.name,
            printFlag: 1,
            canRead: 0
          }
          console.log('我调用了upload', this.form.pics.length)
          this.form.pics.push(fileObj)
          this.fileList.push(fileObj)
        }
      })
    },
    sortByFileName() {
      this.fileList = this.fileList.sort(function(val1, val2) {
        console.log(val1.name)
        return val1.name > val2.name ? 1 : -1
      })
      this.form.pics = this.form.pics.sort(function(val1, val2) {
        return val1.name > val2.name ? 1 : -1
      })
      this.$message.success('操作成功')
    },
    handleRemove(e) {
      this.fileList = this.fileList.filter(item => {
        return item.pic !== e.pic
      })
      this.form.pics = this.form.pics.filter(item => {
        return item.pic !== e.pic
      })
    },
    /** 提交 */
    submit() {
      if (this.checkboxGroup1) {
        this.form.gradeLevel = this.checkboxGroup1.join(',')
      } else {
        this.form.gradeLevel = ''
      }
      this.$refs['form'].validate(valid => {
        if (valid) {
          if (this.upFile) {
            oss.uploadFile(this.upFile, (result, error) => {
              if (error) {
                this.$message.error('上传文件失败')
                return -1
              }
              if (result) {
                console.log('result=', result)
                this.form.coverUrl = result.name
                this.addUpdateRecord()
              }
            })
          } else {
            // 直接保存
            this.addUpdateRecord()
          }
        } else {
          console.log('submit error')
          return -1
        }
      })
    },
    async addUpdateRecord() {
      // 新增
      if (!this.form.id) {
        this.form.id = ''
      }
      // 更新
      // console.log('fileList=', this.fileList)
      // console.log('form.pics=', this.form.pics)
      this.form.pics = this.fileList
      const res = await saveGoodsRecord(this.form)
      if (res.head.ret === 0) {
        this.$message.success('保存成功')
        this.handleClose('1')
      }
    }
  }

}
</script>

<style lang='scss' scoped>
  .avatar-uploader .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }
  .avatar-uploader .el-upload:hover {
    border-color: #409EFF;
  }
  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 178px;
    height: 178px;
    line-height: 178px;
    text-align: center;
  }
  .avatar {
    width: 178px;
    height: 178px;
    display: block;
  }
</style>
