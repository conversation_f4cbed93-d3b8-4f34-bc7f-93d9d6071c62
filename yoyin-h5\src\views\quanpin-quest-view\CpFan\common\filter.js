const optionlist = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'
const tinyNumlist = '①②③④⑤⑥⑦⑧⑨⑩⑪⑫⑬⑭⑮⑯⑰⑱⑲⑳'

export function optionTag(i) {
  return optionlist.charAt(i)
}

export function smallTopicTag(i) {
  return '(' + (i + 1) + ')'
}

export function tinyTopicTag(i) {
  return tinyNumlist.charAt(i)
}

// 判断是否为空对象
export function isEmptyObject(obj, key) {
  if (obj === null || obj === undefined) {
    return '-'
  }
  const arr = Object.keys(obj)
  if (arr.length > 0) {
    return obj[key]
  } else {
    return '-'
  }
}

export default {
  install: function (Vue) {
    Vue.filter('optionTag', optionTag)
    Vue.filter('smallTopicTag', smallTopicTag)
    Vue.filter('tinyTopicTag', tinyTopicTag)
    Vue.filter('isEmptyObject', isEmptyObject)

    Vue.prototype.$cp_filter = {
      optionTag,
      smallTopicTag,
      tinyTopicTag,
      isEmptyObject
    }
  }
}
