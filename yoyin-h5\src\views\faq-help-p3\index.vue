<template>
  <div class="faq">
    <div class="faq-title2">
      连接后打印出来的是白纸，没有内容？
    </div>
    <div class="faq-content2">
      <p class="first-line">1、这可能是打印纸未安装正确导致的问题，请尝试将打印纸反面放置;</p>
      <p class="first-line">2、检查App中的打印预览页内容是否为空；</p>
      <p class="first-line">3、检查纸张类型是否选择错误，有连续纸和标签纸之分。</p>
    </div>

  </div>
</template>

<script>
export default {
  data () {
    return {
      moduleKey: 'faq'
    }
  },
  async mounted () { },
  methods: {},
  components: {}
}
</script>

<style lang="scss">
@import '../../assets/scss/faq.scss'; // 全局faq自定义的css样式
</style>
