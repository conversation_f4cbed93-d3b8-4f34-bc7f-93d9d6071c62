import request from '@/utils/request'

// 翻页查询
const LOAD_DATA_URL = '/platform/gam/community/v1/msg/findmsgpage'
// 删除记录
const DELETE_RECORD_URL = '/platform/gam/community/v1/msg/del'

/**
 * 查询列表数据
 * @param params 参数
 */
export const fetchListByPage = async params => {
  return request({
    url: LOAD_DATA_URL,
    method: 'get',
    params
  })
}

/**
 * 删除记录
 * @param {String} id 记录id
 */
export const deleteRecord = async params => {
  return request({
    url: DELETE_RECORD_URL,
    method: 'get',
    params
  })
}
/** 保存记录 */
export const saveRecord = async params => {
  return request({
    url: '/platform/gam/community/v1/msg/saveorupdate',
    method: 'post',
    params
  })
}
