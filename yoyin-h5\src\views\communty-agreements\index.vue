<template>
  <div class="content">
    <p>{{$t(`${this.moduleKey}.dear-starpany-user`)}}</p>
    <p class="first-line">打印机社区致力于为所有社区用户打造一个自由分享、真诚交流的平台。为维护良好、友善的社区氛围，打印机团队特别制定《打印机社区规范》（以下简称“规范”）。打印机社区尊重有价值的优质内容，注重保护用户权益，对违反规范的用户账号，依据程度轻重采取不同处理办法。</p>

    <p>1. 禁止发布违反中华人民共和国法律、法规的内容，主要表现为：</p>
    <p class="first-line">反对宪法所确定的基本原则的；危害国家安全、泄露国家秘密、颠覆国家政权、破坏国家统一的；损害国家荣誉和利益的；煽动民族仇恨、民族歧视，破坏民族团结的；破坏国家宗教政策，宣扬邪教和封建迷信的；散布谣言，扰乱社会秩序，破坏社会稳定的；散布淫秽、色情、赌博、暴力、凶杀、恐怖或教唆犯罪的；煽动非法集会、结社、游行、示威、聚众扰乱社会秩序的；侮辱或诽谤他人，泄露他人隐私，侵害他人合法权益的；含有法律、行政法规禁止的其他内容的信息。</p>
    <p>2.禁止恶意行为，主要表现为：</p>
    <p class="first-line">发布含有钓鱼网站、木马、病毒网站链接等对APP的运营安全有潜在威胁的内容；发布含有潜在危险、窃取用户隐私等相关内容；抄袭、模仿、冒充他人，通过头像、用户名等个人信息暗示自己与他人或机构相等同或有关联；骚扰他人，以评论、@ 他人、私信等方式对他人反复发送重复或相似诉求；其他扰乱打印机社区正常秩序及打印机社区运营安全的行为。</p>
    <p>3.禁止不友善行为，主要表现为：</p>
    <p class="first-line">臆测、羞辱、谩骂、攻击其他用户或其他不尊重用户的内容；骚扰、恐吓、威胁、诽谤其他用户，或者其他发布在打印机社区并对其他用户造成严重影响的内容；恶意煽动用户攻击、孤立或以其他手段不友善地对待其他用户的内容；因民族、种族、宗教、性取向、性别、年龄、地域、生理特征等身份或归类而歧视、侮辱、攻击他人的内容；泄露他人隐私，或其他侵犯他人合法权益的内容；其他对打印机社区用户产生不良影响的行为。</p>
    <p>4.禁止发布垃圾信息，主要表现为：</p>
    <p class="first-line">以赢利为目的、带有广告性质的内容；重复发布相似内容、恶意刷屏；拥有多个账号，或者与多个账号合作谋求不正当曝光；买卖账号、关注、赞同，发布干扰社区秩序的推广内容或进行相关交易；其他扰乱社区秩序的内容及行为。对违反“规范”用户账号的处理办法：对违反“规范”的用户账号或内容，由社区方直接处理。若对处理有异议，可通过App内“反馈”功能进行反馈。社区方一旦审核判定用户账号存在违反规范的行为，将依据程度轻重，给予以下处理：</p>
    <p class="first-line">（1）警告并删除违规内容：社区方删除相关违反规范的内容，并予以警告；</p>
    <p class="first-line">（2）禁言：社区方将给予违反规范的用户“禁言”处理（禁言期间，被禁言用户不能发表动态、发表评论、发送私信）；</p>
    <p class="first-line">（3）屏蔽：社区方将给予违反规范的用户“屏蔽”处理（被屏蔽用户账号将被删除所有内容及信息）</p>
    <p class="first-line">依据相关法律法规，以及随着社区运营、管理经验的不断成熟与完善，打印机社区有权利随时完善本“规范”。同时，打印机社区欢迎所有社区用户共同维护社区的良好氛围，让我们携手打造一个有序、有爱的健康社区！</p>

    <div style="margin-top: 2rem;text-align: right;color:black">
      打印机社区管委会
      <br />
      2019.04.17
    </div>
  </div>
</template>

<script>
export default {
  data () {
    return {
      moduleKey: 'communty-agreements'
    }
  },
  async mounted () { },
  methods: {},
  components: {}
}
</script>

<style lang="scss">
.content {
  padding: 1.2rem;
  p {
    color: black;
  }
  .first-line {
    text-indent: 2em;
  }
}
</style>
