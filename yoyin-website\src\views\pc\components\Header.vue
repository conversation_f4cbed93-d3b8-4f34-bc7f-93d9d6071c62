<template>
  <div class="contents">
    <div class="logos">
        <div class="logs-image">
            <img src="../../../assets/logo4.png" style="width:97px; height:50px;"/>
        </div>
    </div>
    <div class="menus">
        <div class="menu" :class="{'active': menuType==4}" @click="selectMenu(4)">关于</div>
        <div class="menu" :class="{'active': menuType==3}" @click="selectMenu(3)">下载</div>
        <div class="menu" :class="{'active': menuType==2}" @click="selectMenu(2)">服务</div>
        <!-- <div class="menu" :class="{'active': menuType==1}" @click="selectMenu(1)">产品</div> -->
        <div class="menu" :class="{'active': menuType==0}" @click="selectMenu(0)">首页</div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'MyHeader',
  data () {
    return {
      menuType: 0
    }
  },
  created () {
    console.log('adfadfasdfadsf')
  },
  methods: {
    selectMenu (menuType) {
      this.menuType = menuType
      this.$emit('clickMenu', menuType)
    }
  }
}
</script>

<style scoped>
.contents {
    width: 1200px;
    min-height: 76px;
    position: relative;
    margin: 0 auto;
    display: flex;
}
.logos {
    width: 300px;
    display: flex;
    min-height: 76px;
}
.logs-image {
    margin: auto 0;
}
.logs-text {
    margin: 15px 0 0px 23px;
    color: #5EBF81;
}
.logs-text-top {
    font-size: 24px;
}
.logs-text-down {
    padding-top: 5px;
    font-size: 18px;
}
.menus {
    width: 900px;
    float: right;
    font-size: 16px;
    margin: auto 0;
}
.menu {
    margin-left:auto;
    width: 33px;
    float: right;
    line-height: 32px;
    position: relative;
    /* margin: 22px 65px 22px 0; */
    margin-right: 65px;
}
.menu:hover{
    cursor: pointer;
}
.active {
    /* background-color: #5EBF81; */
    color: #63C184;
    border-bottom: 2px solid #63C184;
    font-weight: bold;
}
</style>
