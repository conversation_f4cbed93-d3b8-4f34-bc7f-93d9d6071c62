/*
 * @Description: In User Settings Edit
 * @Author: your name
 * @Date: 2019-10-16 10:58:32
 * @LastEditTime: 2019-10-17 14:09:07
 * @LastEditors: Please set LastEditors
 */
import request from '@/utils/request'

export function login(data) {
  return request({
    url: '/platform/authority/auth/v1/useraccount/login',
    method: 'post',
    // headers: { 'sadais-agent': 'HEALTHWEIGHT_PLATFORM/2.0/WEB/HEALTH///' },
    data
  })
}

export function getInfo(token) {
  return request({
    url: '/user/info',
    method: 'get',
    params: { token }
  })
}

export function getUserAuthorityInfo() {
  return request({
    url: '/platform/authority/auth/v1/useraccount/getuserauthorityinfo',
    method: 'get'
  })
}

export function logout() {
  return request({
    url: '/user/logout',
    method: 'post'
  })
}
