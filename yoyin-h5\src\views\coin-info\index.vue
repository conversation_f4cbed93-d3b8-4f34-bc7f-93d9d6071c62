<template>
  <div class="content">
    <div class="faq">
      <div class="faq-title"><span>什么是积分？</span></div>
      <div class="faq-content">
        <p class="first-line">积分是柚印APP的官方虚拟货币，你可以使用积分在积分商城兑换任意商品。<br/><br/></p>
      </div>                
    </div>
    <!-- <div class="faq">
      <div class="faq-title"><span>什么是等级？</span></div>
      <div class="faq-content">
        <p class="first-line">等级是通过累计积分后达到的用户身份。等级越高，可获得的抽奖次数越多、奖品越丰厚。使用积分不会影响等级。<br/><br/></p>
      </div>
    </div> -->

    <div class="faq">
      <div class="faq-title"><span>如何获得积分？</span></div>
      <div class="faq-content">
        <p class="first-line">完成积分商城中的各项任务即可获得相应积分奖励。具体的任务内容及奖励数量请查看任务列表。<br/><br/></p>
      </div>
    </div>

    <div class="faq">
      <div class="faq-title"><span>积分有效期是多久？</span></div>
      <div class="faq-content">
        <p class="first-line">积分余额的有效截止期分别是在每年的7月1日0点和1月1日0点。请在上述两个时间节点前兑换使用积分余额，清空后无法补回。<br/><br/></p>
      </div>
    </div>

    <div class="faq">
      <div class="faq-title"><span>更多：</span></div>
      <div class="faq-content">
        <p class="first-line">1.若出现刷积分行为，官方将收回相应积分奖励。</p>
        <p class="first-line">2.积分一旦使用不可退回，请在兑换前认真考虑。</p>
        <p class="first-line">3.使用兑换功能，系统将自动从余额中扣除相应积分。</p>
        <p class="first-line">4.积分不可兑现、不可转让。</p>
        <p class="first-line">5.若使用非正常手段获取积分，柚印APP有权追回所有奖励、平台损失，并追究责任。</p>
        <p class="first-line">6.港澳台地区、新疆、西藏、内蒙古等偏远地区，兑换奖品的邮费需自理。</p>
        <p class="first-line">7.针对限购商品，同一用户只能兑换限制次数；同一ID、手机号、收货地址、收货人视为同一用户。</p>
        <p class="first-line">8.本规则最终解释权在法律允许范围内归柚印平台所有。</p>
      </div>
    </div>
    
    <!-- <div class="cont">
      <div class="cont-all"><span class="cont-title-span" >若参考以上内容后仍存在连接问题，您可以添加官方客服 QQ2657035001，我们将全力帮您解决。</span></div>
    </div> -->
  </div>
</template>

<script>
export default {
  data () {
    return {
    }
  },
  components: {}
}
</script>

<style lang="scss">
// @import '../../assets/scss/faq.scss'; // 全局faq自定义的css样式
page {
  font-size: 16px;
}
.content {
  margin: 8px 24px;
}
.faq {
}
.faq-title {
  line-height: 40px;
  font-size: 16px;
  color: #000000;
  font-weight: bold;
}
.first-line {
  //line-height: 1.25rem;
  font-size: 16px;
  color: #000000;
  line-height: 28px;
}
</style>
