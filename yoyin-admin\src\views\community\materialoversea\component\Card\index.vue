<template>
  <div class="card">
    <div class="card-info">
      <div class="card-info-id">id:{{ item.id }}</div>
      <div class="card-info-size">
        <span>宽:{{ item.width }}px</span>
        <span> 高:{{ item.height }}px</span>
      </div>
      <div class="card-info-time">创建时间：{{ item.createTime }}</div>
      <div v-if="parseInt(item.length) > 0" class="card-info-type">纸张类型: 标签纸-{{ item.length }}</div>
      <div v-if="parseInt(item.length) === -1" class="card-info-type">纸张类型: 连续纸</div>
    </div>
    <div class="card-image">
      <el-image
        :src="item.pic"
        fit="scale-down"
        style="width:100%;height:300px;"
      >
        <div slot="placeholder" class="image-slot">
          加载中<span class="dot">...</span>
        </div>
      </el-image>
    </div>
    <div class="card-control">
      <el-tooltip content="编辑" placement="top">
        <el-button class="el-icon-edit" @click="notifyEdit" />
      </el-tooltip>
      <el-tooltip content="删除" placement="top">
        <el-button class="el-icon-delete" @click.stop="handelDelete" />
      </el-tooltip>
    </div>
  </div>
</template>
<script>
import { deleteRecord } from '@/api/community/materialoversea'
export default {
  name: 'Card',
  props: {
    item: {
      type: Object,
      required: true,
      default: () => {
        return {}
      }
    }
  },
  methods: {
    async handelDelete() {
      const _this = this
      this.$confirm('此操作将永久删除素材，不可恢复, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteRecord({
          id: this.item.id
        }).then(res => {
          if (res.head.ret === 0) {
            _this.$message.success('删除成功')
          } else {
            _this.$message.error('删除失败')
          }
          _this.notifyUpdate()
        })
      }).catch(() => {
      })
    },
    notifyUpdate() {
      this.$emit('notifyUpdate')
    },
    /** 通知父进入编辑 */
    notifyEdit() {
      this.$emit('notifyEdit', this.item)
    }

  }
}
</script>

<style lang="scss" scoped>
.card{
  font-size: 12px;
  color: #999;

  &-info{
      padding: 5px;
    div{
      margin-bottom: 5px;
    }
  }
  &-control{
    width: 100%;
    display: flex;
    justify-content: flex-start;
    button{
      margin: 0px;
      display: inline-block;
      width: 50%;
    }
  }
}
</style>
