/*
 * @Author: your name
 * @Date: 2019-10-16 11:52:25
 * @LastEditTime: 2019-10-22 15:10:15
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /d:\src\starPrinter\xstar-ui\star-printer-admin\src\api\version\list.js
 */
import request from '@/utils/request'

const LOAD_DATA_URL = '/platform/gam/community/v1/words/wordTypeList2'
// 删除记录
const DELETE_RECORD_URL = '/platform/gam/community/v1/words/deleteWordType'
// 保存记录
const SAVE_RECORD_URL = '/platform/gam/community/v1/words/saveOrUpdateWordType'

/**
 * 查询用户列表
 * @param params 参数
 */
export const fetchListByPage = async params => {
  return request({
    url: LOAD_DATA_URL,
    method: 'get',
    params
  })
}

/**
 * 删除记录
 * @param {String} id 记录id
 */
export const deleteRecord = async params => {
  return request({
    url: DELETE_RECORD_URL,
    method: 'get',
    params
  })
}

/**
 * 保存记录
 * @param ids 记录id
 */
export const saveRecord = async params => {
  return request({
    url: SAVE_RECORD_URL,
    method: 'post',
    data: params
  })
}
