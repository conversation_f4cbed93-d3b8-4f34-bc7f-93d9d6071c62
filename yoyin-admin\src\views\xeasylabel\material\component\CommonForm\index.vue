<template>
  <div class="todolist">
    <el-form class="demo-form-inline">
      <el-form-item v-if="mId==='5fb7265e5baa9f55802cf75b'" label="标签">
        <el-checkbox-group v-model="label">
          <el-checkbox-button v-for="item in decorateList" :key="item.id" :label="item.id">{{ item.name }}</el-checkbox-button>
        </el-checkbox-group>
      </el-form-item>
      <el-form-item label="预览图：">
        <SingleUpload key="listUrl" key-word="listUrl" :init-url="isEdit ? files.listUrl.pic: ''" @updateFile="updateFile" />
      </el-form-item>
      <el-form-item label="资源图：">
        <SingleUpload key="resUrl" key-word="resUrl" :init-url="isEdit ? files.resUrl.pic: '' " @updateFile="updateFile" />
      </el-form-item>
      <el-form-item label="排序" prop="sortNum">
        <el-input
          v-model="sortNum"
          type="text"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleBack">返回</el-button>
        <el-button v-permission="'btn-xeasylabel-material-edit'" type="primary" @click="handleSave">保存</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>
<script>
import { saveRecord } from '@/api/xeasylabel/material'
import SingleUpload from '../SingleUpload/index'
import AliyunOSS from '@/utils/aliyunOSS'
import { fetchDictTypeList } from '@/api/xeasylabel/dict'
const oss = new AliyunOSS()
const TIP_MESSAGES = {
  listUrl: '请选择预览图',
  resUrl: '请选择资源图'
}

export default {
  components: {
    SingleUpload
  },
  props: {
    mId: {
      type: String,
      default: ''
    },
    mName: {
      type: String,
      default: ''
    },

    formData: {
      type: Object,
      default: () => { return {} }
    }
  },
  data() {
    return {
      sortNum: 0,
      uploadFiles: { listUrl: {}, resUrl: {}, topUrl: {}, downUrl: {}, leftUrl: {}, rightUrl: {}},
      paperLabel: '0',
      label: [],
      decorateList: [{ id: 'all', name: '全部' }, { id: 'hot', name: '热门' }, { id: 'storage', name: '收纳' }, { id: 'skin', name: '护肤' }, { id: 'kitchen', name: '厨房' }, { id: 'pet', name: '动物' }, { id: 'symbol', name: '符号' }, { id: 'currency', name: '货币' }, { id: 'face', name: '表情' }, { id: 'office', name: '办公' }, { id: 'plant', name: '植物' }, { id: 'cartoon', name: '卡通' }, { id: 'travel', name: '旅游' }, { id: 'study', name: '学习' }, { id: 'fruits', name: '食物' }, { id: 'bubble', name: '气泡' }]
    }
  },
  computed: {
    isEdit() {
      if (this.formData && this.formData.id) {
        return true
      } else {
        return false
      }
    },
    /** 编辑的数据 */
    files() {
      if (this.isEdit) {
        return this.formData.resMap
      } else {
        return {}
      }
    },
    /** 编辑ID */
    addId() {
      if (this.isEdit) {
        return this.formData.id
      } else {
        return ''
      }
    }
  },
  watch: {
    formData: {
      handler(newVal, oldVal) {
        if (newVal.label) {
          this.label = newVal.label.split(',')
        }
        this.sortNum = newVal.sortNum
      },
      immediate: true
    }
  },
  mounted() {
    this.getDecorateList()
  },
  methods: {
    async getDecorateList() {
      const params = {
        type: 'material_sub_type_zs'
      }
      const res = await fetchDictTypeList(params)
      if (res.head.ret === 0) {
        const result = [{ id: '', name: '全部' }]
        res.data.forEach(item => {
          const temp = {
            id: item.value,
            name: item.label
          }
          result.push(temp)
        })
        this.decorateList = result
      }
    },
    handleBack(val) {
      this.$emit('handleBack', val)
    },
    /** 子组件上传文件 */
    updateFile(key, file) {
      this.uploadFiles[key] = {
        file,
        key
      }
    },
    /** 校验文件是否上传 */
    validateFiles() {
      let flag = true
      // 合并
      if (this.isEdit) {
        Object.keys(this.files).forEach(key => {
          const item = this.uploadFiles[key]
          if (!item || !item.hasOwnProperty('file')) {
            this.uploadFiles[key] = this.files[key]
          }
        })
      }
      // 校验
      Object.keys(this.uploadFiles).forEach(key => {
        if (!flag) {
          return
        }
        const file = this.uploadFiles[key]
        if (TIP_MESSAGES[key] && file && !file.file && (!file.pic || file.pic.indexOf('http') < 0)) {
          this.$message.error(TIP_MESSAGES[key])
          flag = false
        }
      })
      return flag
    },
    /** 保存 */
    async handleSave() {
      if (this.validateFiles()) {
        const upFiles = []
        const picDto = {}
        Object.keys(this.uploadFiles).forEach(key => {
          const item = this.uploadFiles[key]
          if (item.file) {
            item.file.key = key
            upFiles.push(item.file)
          } else {
            picDto[key] = { pic: item.pic }
          }
        })
        // 上传
        oss.uploadFiles(upFiles, (results, error) => {
          if (error) {
            this.$message.error('文件上传失败，请检查')
            return
          }
          if (results) {
            results.forEach(res => {
              picDto[res.key] = { pic: res.name }
            })
          }

          let labelTmp = ''
          if (this.label && this.label.length > 0) {
            this.label.forEach(item => {
              labelTmp += item + ','
            })
          }

          // 保存
          const params = {
            id: this.addId,
            mId: this.mId,
            length: this.paperLabel,
            label: labelTmp,
            sortNum: this.sortNum,
            picDto
          }
          saveRecord(params).then(res => {
            if (res.head.ret === 0) {
              this.$message.success('保存成功')
              this.handleBack('1')
            } else {
              this.$message.error(res.head.msg)
            }
          })
        })
      }
    }
  }
}
</script>
