export default {
  starpany: '打印机',
  loading: '加载中...',
  day: '天',
  title: {
    'communty-agreements': '打印机社区规范',
    'doc-operation-instructions': 'FAQ',
    'feed-detail': '动态详情',
    'note-detail': '打印机小纸条',
    'oauth-transfer': '授权回调',
    'reply-list': '评论回复',
    'share-about-us': '星星機',
    'share-device': '打印机互动打印',
    'user-protocol': '用户协议',
    download: '打印机下载',
    faq: '常见问题'
  },

  // 社区规范
  'communty-agreements': {
    'dear-starpany-user': '致亲爱的打印机用户'
  },
  // 安卓文档导入说明
  'doc-import-andriod': {
    'title': '如何将文档导入APP？',
    'step1': '通过电子邮件等方式，将文档发送到手机上',
    'step2': '打开打印机App，选择文档类型，即可看到该文档',
    'step3': '当文档过多的时候，可通过搜索功能搜索该文档',
    'tips': '若上述方法未能成功导入，请在手机的【文件管理】 中找到需要打印的文档，选中文档后，在打开方式中选择打印机App即可。'
  },
  // IOS文档导入说明
  'doc-import-ios': {
    'title': '如何将文档导入APP？',
    'step1': '通过电子邮件等方式，将文档发送到手机上',
    'step2': '打开文件，点击右上角"..."，选择“用其他应用程序打开”',
    'step3': '选择“打印机”App打开'
  },

  // 动态详情
  'feed-detail': {
    'add-to-friends': '加为好友',
    'follow': '+关注',
    'followed': '已关注',
    'mutual-followed': '互相关注',
    'i-want-to-say': '我要互动一下',
    release: '发布',
    'delete-tip': '确定删除该评论吗？',
    'delete-success': '删除成功',
    'data-empty': '该动态不存在或已被删除',
    'share-feed': '分享了一条动态',
    'no-comments': '还木有人评论',
    comment: '评论',
    reply: '回复',
    'view-all': '查看全部',
    comments: '条评论',
    'dear-starpany-user': '动态数据已删除，返回上一级'
  },

  // 纸条详情
  'reply-list': {
    'view-original-dynamic': '查看原动态',
    reply: '回复',
    'comment-replies': '评论回复',
    'data-empty': '该评论不存在或已被删除',
    'delete-tip': '确定删除该回复吗？',
    'delete-success': '删除成功',
    replies: '条回复'
  },

  'share-about-us': {
    'download-now': '立即下載',
    share: '分享',
    'product-manual': '星星機產品說明'
  },
  'share-device': {
    anonymous: '匿名',
    'share-theme': '互动主题',
    countdown: '倒计时间',
    organizer: '发起者',
    'sharing-has-expired': '互动已过期',
    'enter-your-content': '请输入你的内容',
    'add-image': '添加图片',
    'send-to-friends': '发送好友打印',
    'enter-the-content': '请输入内容',
    sending: '发送中',
    'image-upload-failed': '图片上传失败',
    'send-success': '发送成功'
  },
  'user-protocol': {
    'user-agreement': '用户协议'
  }
}
