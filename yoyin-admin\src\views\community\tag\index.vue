<!--
 * @Description: In User Settings Edit
 * @Author: your name
 * @Date: 2019-10-16 11:28:20
 * @LastEditTime: 2019-10-16 11:38:48
 * @LastEditors: Please set LastEditors
 -->
<template>
  <div class="app-container">
    <div class="table-query-container">
      <span class="demonstration">有效期</span>
      <el-date-picker
        v-model="timeRange"
        :value-format="timeFormat"
        :format="timeFormat"
        :unlink-panels="true"
        type="datetimerange"
        :clearable="false"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        @change="changeTimeRange"
      />
      <el-button type="primary" @click="handleQuery">查询</el-button>
      <el-button @click="handleAdd">新增</el-button>
    </div>
    <div class="dialog-container">
      <EditDialog :visiable="showDialog" :form-data="banner" @closeDialog="closeDialog" />
    </div>

    <div class="table-select-container">
      <div class="select-count">当前选择<span style="color: #409EFF"> {{ multipleSelection.length }} </span>项
        <a @click="handleClearSelection">清空</a>
        <a v-if="multipleSelection.length > 0" @click="handelMultiDel">删除</a>
      </div>
    </div>

    <div class="table-container">
      <el-table
        ref="multipleTable"
        :data="list"
        tooltip-effect="dark"
        style="width: 100%"
        border
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column label="序号" width="55">
          <template slot-scope="scope">{{ scope.row.sort }}</template>
        </el-table-column>
        <el-table-column label="键值" width="200">
          <template slot-scope="scope">{{ scope.row.name }}</template>
        </el-table-column>
        <el-table-column label="描述" width="200">
          <template slot-scope="scope">{{ scope.row.remark }}</template>
        </el-table-column>
        <el-table-column label="中文名" width="200">
          <template slot-scope="scope">{{ scope.row.titleZh }}</template>
        </el-table-column>
        <el-table-column label="英文名" width="200">
          <template slot-scope="scope">{{ scope.row.titleEn }}</template>
        </el-table-column>
        <el-table-column prop="showType" label="显示类型" :formatter="jumpTypeFormat" width="120" />
        <el-table-column label="操作">
          <template slot-scope="scope">
            <el-button size="mini" @click="handleEdit(scope.$index, scope.row)">编辑</el-button>
            <el-button
              size="mini"
              type="danger"
              @click="handleDeleteRow(scope.$index, scope.row)"
            >删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="pagination-container" style="margin-top:20px;">
      <el-pagination
        :current-page="params.pageno"
        :page-sizes="[10, 20, 30, 40]"
        :page-size="params.pagesize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>
<script>
import { fetchListByPage, deleteRecord } from '@/api/community/tag'
import EditDialog from './component/EditDialog/index'
export default {
  components: {
    EditDialog
  },
  data() {
    return {
      // 查询参数
      params: {
        startdate: '',
        enddate: '',
        pageno: 1,
        pagesize: 10
      },
      /** 列表 */
      list: [],
      /** 总条数 */
      total: 0,
      /** 消息体 */
      banner: { },
      /** 勾选数据 */
      multipleSelection: [],
      /** 时间段 */
      timeRange: '',
      /** 时间格式 */
      timeFormat: 'yyyy-MM-dd',
      /** 显示编辑框 */
      showDialog: false
    }
  },
  mounted() {
    this.getList()
  },
  methods: {
    /** 获取列表 */
    async getList() {
      const res = await fetchListByPage(this.params)
      if (res.head.ret === 0) {
        this.list = res.data.result
        this.total = res.data.totalCount
      }
    },
    /** 切换每页条数 */
    handleSizeChange(val) {
      this.params.pagesize = val
      this.getList()
    },
    /** 切换当前页 */
    handleCurrentChange(val) {
      this.params.pageno = val
      this.getList()
    },
    /** 编辑 */
    handleEdit(index, row) {
      this.banner = row
      this.showDialog = true
    },
    /** 删除单行 */
    async handleDeleteRow(index, row) {
      const params = {
        id: row.id
      }
      this.$confirm('此操作将永久删除该记录, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteRecord(params).then(res => {
          if (res.head.ret === 0) {
            this.$message({
              type: 'success',
              message: '删除成功!'
            })
            this.getList()
          }
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
    },
    /** 多选删除 */
    handelMultiDel() {
      this.$confirm('此操作将永久删除选中记录, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const rows = [...this.multipleSelection]
        rows.forEach(item => {
          const params = { id: item.id }
          deleteRecord(params).then(res => {
            if (res.head.ret === 0) {
              this.$message.success('删除成功')
              this.getList()
            }
          })
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
    },
    /** 勾选事件 */
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    /** 清空勾选 */
    handleClearSelection() {
      this.$refs.multipleTable.clearSelection()
    },
    /** 跳转类型 转换 */
    jumpTypeFormat(row, column) {
      const code = parseInt(row.showType)
      if (code === 0) {
        return '全部'
      } else if (code === 1) {
        return '素材'
      } else if (code === 2) {
        return '发帖'
      } else if (code === 99) {
        return '不显示'
      }
      return '未知类型'
    },
    /** 选择时间段 */
    changeTimeRange(val) {
      this.params.startdate = val[0]
      this.params.enddate = val[1]
    },
    /** 查询 */
    handleQuery() {
      this.getList()
    },
    /** 新增 */
    handleAdd() {
      this.banner = {}
      this.showDialog = true
    },
    /** 关闭弹框 */
    closeDialog(code) {
      this.showDialog = false
      this.banner = {}
      // 保存code
      if (code === '1') {
        this.getList()
      }
    }

  }
}
</script>

<style lang="scss" scoped>

</style>
