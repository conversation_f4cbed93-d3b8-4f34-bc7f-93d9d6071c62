<template>
  <div v-if="visiable" class="edit-dialog">
    <el-dialog
      ref="dialog"
      :show-close="false"
      :title="form.id === '' ? '新增' : '编辑' "
      :visible.sync="visiable"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      width="60%"
      center
    >
      <el-form ref="form" :model="form" :rules="rules" label-width="180px" class="edit-form">
        <el-form-item label="标题：" prop="title">
          <el-input
            v-model="form.title"
            type="text"
            maxlength="20"
            placeholder="请输入标题"
          />
        </el-form-item>
        <el-form-item label="副标题：" prop="subTitle">
          <el-input
            v-model="form.subTitle"
            type="text"
            maxlength="20"
            placeholder="请输入副标题"
          />
        </el-form-item>
        <el-form-item label="是否展示限时优惠：" prop="showLimitFlag">
          <el-radio-group v-model="form.showLimitFlag">
            <el-radio-button label="true">是</el-radio-button>
            <el-radio-button label="false">否</el-radio-button>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="数量/单位：" prop="unit">
          <el-input-number v-model="form.amount" label="数量：" />
          <el-radio-group v-model="form.unit">
            <el-radio-button label="day">天</el-radio-button>
            <el-radio-button label="month">月</el-radio-button>
            <el-radio-button label="year">年</el-radio-button>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="优惠后价格：" prop="realPrice">
          <el-input
            v-model="form.realPrice"
            type="text"
            maxlength="20"
            placeholder="优惠后价格"
          />
        </el-form-item>
        <el-form-item label="商品原价格：" prop="price">
          <el-input
            v-model="form.price"
            type="text"
            maxlength="20"
            placeholder="商品原价格"
          />
        </el-form-item>
        <el-form-item label="是否只允许购买一次：" prop="onlyOnceFlag">
          <el-radio-group v-model="form.onlyOnceFlag">
            <el-radio-button label="true">是</el-radio-button>
            <el-radio-button label="false">否</el-radio-button>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="排序字段：" prop="sortNum">
          <el-input
            v-model="form.sortNum"
            type="text"
            maxlength="20"
            placeholder="排序字段"
          />
        </el-form-item>
        <el-form-item label="对应苹果商品id：" prop="appleProcId">
          <el-input
            v-model="form.appleProcId"
            type="text"
            maxlength="60"
            placeholder="对应苹果商品id"
          />
        </el-form-item>
        <el-form-item>
          <el-button @click="handleClose">返回</el-button>
          <el-button v-permission="'btn-menuQuanpin-memgergoodsvip-edit'" type="primary" @click="submit">保存</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import { saveRecord } from '@/api/quanpin/membergoods'
export default {
  name: 'EditDialog',
  props: {
    visiable: {
      type: Boolean,
      required: true,
      default: false
    },
    formData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      /** 表单 */
      form: {
      },
      /** 校验规则 */
      rules: {
        content: [
          { required: true, message: '请输入文案', trigger: 'blur' }
        ]
      },
      /** 待上传的文件 */
      upFile: undefined,
      testUrl: '',
      /** 时间格式 */
      timeFormat: 'yyyy-MM-dd HH:mm:ss',
      showDialog: false
    }
  },
  computed: {
  },
  watch: {
    formData: function(val) {
      this.upFile = undefined
      this.form = { ...val }
    }
  },
  mounted() {
    this.show = this.visiable
  },
  methods: {
    /** 返回 */
    handleClose(code) {
      this.$emit('closeDialog', code)
    },
    /** 提交 */
    submit() {
      this.addUpdateRecord()
    },
    async addUpdateRecord() {
      // 新增
      if (!this.form.id) {
        this.form.id = ''
        // 新增
        const res = await saveRecord(this.form)
        if (res.head.ret === 0) {
          this.$message.success('保存成功')
          this.handleClose('1')
        }
      } else {
        // 更新
        const res = await saveRecord(this.form)
        if (res.head.ret === 0) {
          this.$message.success('保存成功')
          this.handleClose('1')
        }
      }
    }
  }

}
</script>

<style lang='scss' scoped>
  .avatar-uploader .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }
  .avatar-uploader .el-upload:hover {
    border-color: #409EFF;
  }
  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 178px;
    height: 178px;
    line-height: 178px;
    text-align: center;
  }
  .avatar {
    width: 178px;
    height: 178px;
    display: block;
  }
</style>
