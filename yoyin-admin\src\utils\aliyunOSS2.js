/*
 * @Author: your name
 * @Date: 2019-10-22 17:25:39
 * @LastEditTime: 2019-10-22 18:53:45
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \star-printer-admin\src\utils\aliyunOSS.js
 */
import { FILE_URL } from '@/consts/index'
// import axios from '@/utils/axios'
import { storage, uuid } from '@/utils/common'
import OSS from 'ali-oss'
import moment from 'moment'
import { getOssToken } from '@/api/oss/index'

const OSS_OPTIONS_KEY = 'OSS_OPTIONS_KEY'
export default class AliyunOSS extends Object {
   objectName = null;
   client = null;
   queue = [];
   isLoading = false;

   async uploadFile(file, callback) {
     const matches = file.name.match(/\.(.+)/i)
     const suffix = (matches && `.${matches[1].toLowerCase()}`) || '.jpeg'
     this.queue.push({ file, suffix, callback })
     if (!this.client) {
       this.initClient()
     } else {
       this.emptyQueue()
     }
   }

   async initClient2() {
     let options = storage.get(OSS_OPTIONS_KEY)
     options = null
     if (!options) {
       options = await this.getOSSOptionFromServer()
       storage.set(OSS_OPTIONS_KEY, JSON.stringify(options))
     } else {
       options = JSON.parse(options)
     }

     if (options) {
       this.objectName = options.objectName
       this.client = new OSS(options)
       this.emptyQueue()
     }
   }

   uploadFiles(fileList, callback) {
     if (fileList === null || fileList.length === 0) {
       callback(null, null)
       return
     }
     const uploadTask = []
     fileList.forEach(file => {
       const promise = new Promise((resolve, reject) => {
         this.uploadFile(file, (result, error) => {
           if (error) {
             reject(error)
           } else {
             resolve(result)
           }
         })
       })
       uploadTask.push(promise)
     })
     Promise.all(uploadTask)
       .then(async results => {
         if (callback != null) {
           callback(results, null)
         }
       })
       .catch(e => {
         if (callback != null) {
           callback(null, e)
         }
       })
   }

   async initClient() {
     if (this.isLoading) {
       return
     }
     this.isLoading = true

     let options = storage.get(OSS_OPTIONS_KEY)

     if (!options) {
       options = await this.getOSSOptionFromServer()
       storage.set(OSS_OPTIONS_KEY, JSON.stringify(options))
     } else {
       options = JSON.parse(options)
     }

     if (options) {
       this.objectName = options.objectName
       this.client = new OSS(options)
       this.emptyQueue()
     }
     this.isLoading = false
   }

   async getOSSOptionFromServer() {
     const {
       head, data
     } = await getOssToken('/api/log/event/v1/storage/getosstoken')
     if (head.ret === 0) {
       console.log(data)
       const options = {
         accessKeyId: data.accessKeyId,
         accessKeySecret: data.accessKeySecret,
         bucket: data.paramMap.OSS.bucket,
         endpoint: data.paramMap.OSS.endPoint,
         stsToken: data.securityToken,
         objectName: data.paramMap.OSS.objectName
       }
       return options
     }
     return {}
   }

   emptyQueue() {
     if (this.queue.length > 0) {
       const task = this.queue[this.queue.length - 1]
       const { file, suffix, callback = null } = task
       const currentDate = moment().format('YYYYMMDD')

       let objectName = `${this.objectName}${currentDate}/${uuid()}${suffix}`
       // apk包特殊处理
       if (suffix.indexOf('apk') > -1 || suffix.indexOf('bin') > -1) {
         objectName = `/common/star/apk/${file.name}`
       }
       this.client
         .put(objectName, file)
         .then(result => {
           if (callback !== null) {
             result.name = `${FILE_URL}/${result.name}`
             result.key = file.key
             callback(result, null)
           }
           this.queue.pop()
         })
         .catch(e => {
           if (e.code && (e.code === 'InvalidSecurityToken' || e.code === 'InvalidAccessKeyId')) {
             // key无效或token过期 重新发起请求
             storage.remove(OSS_OPTIONS_KEY)
             this.initClient()
           } else {
             if (callback !== null) {
               callback(null, e)
             }
           }
         })
     }
     //  this.queue.forEach(task => {
     //    const { file, suffix, callback = null } = task
     //    const currentDate = moment().format('YYYYMMDD')

     //    let objectName = `${this.objectName}${currentDate}/${uuid()}${suffix}`
     //    // apk包特殊处理
     //    if (suffix.indexOf('apk') > -1 || suffix.indexOf('bin') > -1) {
     //      objectName = `/common/star/apk/${file.name}`
     //    }
     //    this.client
     //      .put(objectName, file)
     //      .then(result => {
     //        if (callback !== null) {
     //          result.name = `${FILE_URL}/${result.name}`
     //          result.key = file.key
     //          callback(result, null)
     //        }
     //        this.queue = []
     //      })
     //      .catch(e => {
     //        if (e.code && (e.code === 'InvalidSecurityToken' || e.code === 'InvalidAccessKeyId')) {
     //          // key无效或token过期 重新发起请求
     //          storage.remove(OSS_OPTIONS_KEY)
     //          this.initClient()
     //        } else {
     //          if (callback !== null) {
     //            callback(null, e)
     //          }
     //        }
     //      })
     //  })
   }
}
