<template>
  <div class="content">
    <div>
      <img src="https://m.yoyin.net/h5/img/a4/en/zidingyipingtu.png" />
    </div>
  </div>
</template>

<script>
export default {
  data () {
    return {
      language: ''
    }
  },
  components: {},
  mounted() {
    this.initPage()
  },
  methods: {
    getCookie(cName) {
      // alert(document.cookie)
      if (document.cookie.length > 0) {
        let cStart = document.cookie.indexOf(cName + '=')
        if (cStart !== -1) {
          cStart = cStart + cName.length + 1
          let cEnd = document.cookie.indexOf(';', cStart)
          if (cEnd === -1) {
            cEnd = document.cookie.length
          }
          return unescape(document.cookie.substring(cStart, cEnd))
        }
      }
      return ''
    },
    initPage() {
      // 页面初始化调用
      var lang = navigator.language || navigator.browserLanguage
      let locale = 'zh_CN'
      if (window.StarPrinterJS && window.StarPrinterJS.getLanguage()) {
        locale = window.StarPrinterJS.getLanguage()
      } else if (window.webkit && window.webkit.messageHandlers) {
        locale = this.getCookie('language')
        if (locale) {
          // do nothing
        } else {
          // locale = window.webkit.messageHandlers.getLanguage.postMessage(null)
          locale = navigator.language || navigator.browserLanguage
        }
      } else {
        locale = navigator.language || navigator.browserLanguage
      }
      this.language = locale.toLowerCase()
      // this.language = 'en-US'
      document.title = 'Question'
    }
  }
}
</script>

<style lang="scss">
// .content {
//   margin: 2em;
// }
img {
  width: 100%;
  object-fit: contain;
}
</style>
