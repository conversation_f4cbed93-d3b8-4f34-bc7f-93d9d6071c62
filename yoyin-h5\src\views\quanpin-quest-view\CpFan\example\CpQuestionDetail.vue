<template>
  <div>
    <div class="question-item" v-if="ques">
      <QuestionItem
        :ques="ques"
        :showQuestionNum="true"
        :questionIndex="1"
        :showKnowledge="true"
        :showExplain="true"
        @toggleExplain="toggleExplain"
      >
        <!-- <template v-slot:quesAttribute="slotProps">
          <div class="item">
            {{ slotProps.data.questionId }}
          </div>
        </template> -->
      </QuestionItem>
    </div>
  </div>
</template>

<script>
  export default {
    data() {
      return {
        quesRes: require('../../assets/data/question.json'),
        ques: null,
      }
    },
    mounted() {
      if (this.quesRes.code == 200) {
        this.ques = this.quesRes.data
      }
    },
    methods: {
      toggleExplain(ques, index) {
        console.log(ques, index)
      },
    },
  }
</script>

<style scoped lang="scss">
  .question-item {
    margin: 20px auto;
    width: 800px;
  }
</style>
