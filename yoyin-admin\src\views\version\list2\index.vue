<!--
 * @Description: In User Settings Edit
 * @Author: your name
 * @Date: 2019-10-16 11:28:20
 * @LastEditTime: 2019-10-22 17:03:31
 * @LastEditors: Please set LastEditors
 -->
<template>
  <div class="app-container">
    <div class="control-container">
      <el-select v-model="printerModel" placeholder="选择打印机类型">
        <el-option label="全部" value="" />
        <el-option
          v-for="(value, key) in printerTypeList"
          :key="value"
          :label="key"
          :value="value"
        />
      </el-select>
      <el-button type="primary" @click="queryVersion">查询</el-button>
      <el-button type="primary" @click="showWhiteUserListDialog = true"
        >白名单</el-button
      >
      <p>
        <el-button
          v-permission="'btn-menuSystemsetting-version-edit'"
          type="success"
          @click="addVersion"
          >添加</el-button
        >
      </p>
    </div>

    <div class="table-container">
      <el-table
        ref="multipleTable"
        :data="tableList"
        tooltip-effect="dark"
        style="width: 100%"
        border
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column
          prop="followStatus"
          label="状态"
          width="80"
          :formatter="formatFollowStatus"
        />
        <el-table-column label="打印机型号" width="120">
          <template slot-scope="scope">{{ scope.row.printerModel }}</template>
        </el-table-column>
        <el-table-column label="固件名称" width="120">
          <template slot-scope="scope">{{ scope.row.versionName }}</template>
        </el-table-column>
        <el-table-column prop="versionCode" label="固件版本号" width="120" />
        <el-table-column prop="createtime" label="上传时间" width="120" />
        <el-table-column prop="updatetime" label="更新时间" width="120" />
        <el-table-column prop="needForceUpdate" label="强制升级" width="120">
          <template slot-scope="scope">{{
            scope.row.needForceUpdate == 0 ? "否" : "是"
          }}</template>
        </el-table-column>
        <el-table-column prop="isBaseVersion" label="是否重大版本" width="120">
          <template slot-scope="scope">{{
            scope.row.isBaseVersion == 0 ? "否" : "是"
          }}</template>
        </el-table-column>
        <el-table-column prop="url" label="下载地址" show-overflow-tooltip />
        <el-table-column prop="remark" label="版本说明">
          <template slot-scope="scope">
            <pre> {{ scope.row.remark }}</pre>
          </template>
        </el-table-column>
        <el-table-column label="操作" fixed="right">
          <template slot-scope="scope">
            <div v-show="scope.row.followStatus == 0">
              <el-button
                size="mini"
                v-permission="'btn-menuSystemsetting-version-edit'"
                @click="handleEdit(scope.$index, scope.row)"
                style
                >编辑</el-button
              >
              <!-- <el-button size="mini" @click="handleOnlyFile(scope.$index, scope.row)">仅上传文件</el-button> -->
              <el-button
                v-permission="'btn-menuSystemsetting-version-edit'"
                size="mini"
                type="danger"
                @click="handleDeleteRow(scope.$index, scope.row)"
                >删除</el-button
              >
            </div>

            <div v-show="scope.row.followStatus == 1">
              <el-button
                size="mini"
                type="success"
                v-permission="'btn-menuSystemsetting-version-audit'"
                @click="handleEdit(scope.$index, scope.row)"
                style
                >审核</el-button
              >
            </div>
            <div v-show="scope.row.followStatus == 99">
              <el-button
                size="mini"
                v-permission="'btn-menuSystemsetting-version-audit'"
                @click="handleEdit(scope.$index, scope.row)"
                style
                >回退</el-button
              >
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="pagination-container" style="margin-top: 20px">
      <el-pagination
        :current-page="currentPage"
        :page-sizes="[10, 20, 30, 40]"
        :page-size="currentSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
    <!-- 弹框 -->
    <div class="dialog-container">
      <el-dialog
        :title="form.id === '' ? '新增标签' : '编辑标签'"
        :visible.sync="showDialog"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        width="50%"
        center
      >
        <el-form
          ref="ruleForm"
          :model="form"
          :rules="rules"
          label-width="100px"
          class="demo-ruleForm"
        >
          <el-form-item label="打印机型号" prop="printerModel">
            <!-- <el-input v-model="form.printerModel" /> -->
            <el-select
              v-model="form.printerModel"
              placeholder="选择打印机类型"
              @change="getAllByDriverData"
            >
              <el-option
                v-for="item in printerTypeConstantList"
                :key="item.value"
                :label="item.value"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="固件名称" prop="versionName">
            <!-- <el-input v-model="form.versionName" /> -->
            <el-select v-model="form.versionName" placeholder="选择固件名称">
              <el-option
                v-for="item in versionDataList"
                :key="item.value"
                :label="item.value"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="固件版本号" prop="versionCode">
            <el-input v-model="form.versionCode" />
          </el-form-item>
          <el-form-item label="前置版本" prop="preDriverId">
            <el-row>
              <el-col :span="12">
                <el-input v-model="form.preDriverId" />
              </el-col>
              <el-col :span="10" offset="1">
                如果有多个，以英文字符分号“;”分开，<font style="color: red"
                  >优先</font
                >
              </el-col>
            </el-row>
          </el-form-item>
          <el-form-item label="是否重大版本" prop="preDriverId">
            <el-radio-group v-model="form.isBaseVersion">
              <el-radio-button label="0">否</el-radio-button>
              <el-radio-button label="1">是</el-radio-button>
            </el-radio-group>
          </el-form-item>

          <el-form-item label="是否停用">
            <el-radio-group v-model="form.stopFlag">
              <el-radio-button label="0">否</el-radio-button>
              <el-radio-button label="1">是</el-radio-button>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="是否强制升级">
            <el-radio-group v-model="form.needForceUpdate">
              <el-radio-button label="0">否</el-radio-button>
              <el-radio-button label="1">是</el-radio-button>
            </el-radio-group>
          </el-form-item>
          <el-row>
            <el-col :span="12">
              <el-form-item label="是否判断电量">
                <el-radio-group v-model="form.needCheckPower">
                  <el-radio-button label="0">否</el-radio-button>
                  <el-radio-button label="1">是</el-radio-button>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :span="12" style="display: flex">
              <el-form-item
                label="电量百分比"
                v-show="
                  form.needCheckPower === '1' || form.needCheckPower === 1
                "
              >
                <el-input
                  v-model="form.powerValue"
                  style="padding-right: 15px"
                  size="60"
                />
                <font style="color: red; font-weight: blod">填写1到99</font>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item label="MD5值">
                <el-input v-model="form.md5" />
              </el-form-item>
            </el-col>
            <el-col :span="12" style="display: flex">
              <el-form-item label="是否自动计算">
                <el-radio-group v-model="autoCale">
                  <el-radio-button label="1">是</el-radio-button>
                  <el-radio-button label="0">否</el-radio-button>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>
          <!-- <el-form-item label="是否首页展示">
            <el-radio-group v-model="form.needIndexShow">
              <el-radio-button label="0">否</el-radio-button>
              <el-radio-button label="1">是</el-radio-button>
            </el-radio-group>
          </el-form-item> -->
          <el-form-item v-if="!onlyUpload" label="版本说明">
            <el-input
              v-model="form.remark"
              type="textarea"
              :autosize="{ minRows: 2, maxRows: 20 }"
              placeholder="请输入内容"
            />
          </el-form-item>
          <el-form-item v-show="showUrl" label="下载地址">
            <a :href="form.url" style="color: blue" class="link">{{
              form.url
            }}</a>
          </el-form-item>
          <el-form-item label="更换文件">
            <el-upload
              ref="upload"
              class="upload-demo"
              action
              accept=".apk, .bin, .new"
              :auto-upload="false"
              :on-remove="handleRemove"
              :file-list="fileList"
              :on-change="changeFile"
              :before-upload="beforeUpload"
            >
              <el-button slot="trigger" size="small" type="primary"
                >选取文件</el-button
              >
            </el-upload>
          </el-form-item>
          <el-form-item
            v-show="form.followStatus != 1 && form.followStatus != 99"
          >
            <el-button @click="showDialog = false">返回</el-button>
            <el-button
              v-permission="'btn-menuSystemsetting-version-edit'"
              type="primary"
              :loading="loading"
              @click="submitForm"
              >保存</el-button
            >
            <el-button
              v-permission="'btn-menuSystemsetting-version-edit'"
              type="success"
              :loading="loading"
              @click="updateSubmit"
              v-show="form.followStatus == 0"
              >提交审核</el-button
            >
          </el-form-item>
          <el-form-item v-show="form.followStatus == 1">
            <el-button @click="showDialog = false">返回</el-button>
            <el-button
              v-permission="'btn-menuSystemsetting-version-audit'"
              type="warning"
              :loading="loading"
              @click="updateReject"
              >驳回</el-button
            >
            <el-button
              v-permission="'btn-menuSystemsetting-version-audit'"
              type="primary"
              :loading="loading"
              @click="updatePass"
              >审核通过</el-button
            >
          </el-form-item>
          <el-form-item v-show="form.followStatus == 99">
            <el-button @click="showDialog = false">返回</el-button>
            <el-button
              v-permission="'btn-menuSystemsetting-version-audit'"
              type="warning"
              :loading="loading"
              @click="updateTakeDown"
              >临时下架</el-button
            >
          </el-form-item>
        </el-form>
      </el-dialog>
    </div>
    <!-- 批量修改弹框 -->
    <div class="dialog-container">
      <el-dialog
        title="编辑标签"
        :visible.sync="showMuliDialog"
        width="50%"
        center
      >
        <el-form
          ref="ruleMultiForm"
          :model="multiForm"
          label-width="100px"
          class="demo-ruleForm"
          :rules="multiFormRules"
        >
          <el-form-item label="版本号" prop="version">
            <el-input v-model="multiForm.version" />
          </el-form-item>
          <el-form-item label="版本说明">
            <el-input
              v-model="multiForm.remark"
              type="textarea"
              :autosize="{ minRows: 2, maxRows: 20 }"
              placeholder="请输入内容"
            />
          </el-form-item>
          <el-form-item>
            <el-button @click="showMuliDialog = false">返回</el-button>
            <el-button
              v-permission="'btn-menuSystemsetting-version-edit'"
              type="primary"
              :loading="loading"
              @click="submitMultiForm"
              >保存</el-button
            >
          </el-form-item>
        </el-form>
      </el-dialog>
      <el-dialog
        :title="'白名单'"
        :visible.sync="showWhiteUserListDialog"
        :close-on-press-escape="false"
        width="50%"
        center
      >
        <while-user-list />
      </el-dialog>
    </div>
  </div>
</template>
<script>
import {
  fetchListByPage, deleteRecord, saveRecord, findPrintTypeList, getAllByDriver,
  updatePrintDriverSubmit,
  updatePrintDriverReject,
  updatePrintDriverPass
} from '@/api/version/driver.js'
import { versionDataList, printerTypeConstantList2 } from '@/consts/index'
import AliyunOSS from '@/utils/aliyunOSS'
import WhileUserList from "./component/WhiteUserList.vue";
const oss = new AliyunOSS()
export default {
  components: {
    WhileUserList
  },
  data() {
    return {
      showWhiteUserListDialog: false,
      printerModel: '',
      currentPage: 1,
      currentSize: 10,
      total: 100,
      tableList: [],
      multipleSelection: [],
      // 弹框
      showDialog: false,
      // 批量
      showMuliDialog: false,
      // 仅上传文件模式
      onlyUpload: false,
      autoCale: '1',
      // 表单
      form: {
        id: '',
        channel: '',
        url: '',
        param: '',
        version: '',
        remark: '',
        stopFlag: '0',
        autoCale: '1'
      },
      // 表单
      multiForm: {
        version: '',
        remark: ''
      },
      // 检验规则
      rules: {
        channel: [
          { required: true, message: '请输入渠道', trigger: 'blur' },
          { min: 3, max: 20, message: '长度在 3 到 20 个字符', trigger: 'blur' }
        ],
        url: [{ required: true, message: '请选择文件', trigger: 'blur' }],
        version: [
          { required: true, message: '请输入版本号如1.1.2', trigger: 'blur' }
        ]
      },
      multiFormRules: {
        version: [
          { required: true, message: '请输入版本号如1.1.2', trigger: 'blur' }
        ]
      },
      fileList: [],
      file: undefined,
      loading: false,
      showUrl: false,
      printerTypeConstantList: printerTypeConstantList2,
      versionDataList: versionDataList,
      printerTypeList: {},
      driverDataList: []
    }
  },

  computed: {},
  mounted() {
    this.getList()
    this.getTypeList()
  },
  methods: {
    formatFollowStatus(row) {
      if (row.followStatus == 0 || row.followStatus == undefined) {
        return "预发布";
      } else if (row.followStatus == 1) {
        return "提交审核";
      } else if (row.followStatus == 99) {
        return "审核通过";
      }
      return "";
    },
    getAllByDriverData() {
      // 获取当前所有打印设备，给与它的上级
      if (this.form.printerModel) {
        const parms = {
          printerType: this.form.printerModel
        }
        getAllByDriver(parms).then((res) => {
          this.driverDataList = res.data
        })
      }
    },
    getTypeList() {
      findPrintTypeList().then(res => {
        this.printerTypeList = res.data
      })
    },
    /** 添加 */
    addVersion() {
      this.form = {
        autoCale: '1'
      }
      this.showDialog = true
      this.fileList = []
      this.file = undefined
      this.showUrl = false
      this.onlyUpload = false
    },
    /** 查询 */
    queryVersion() {
      this.getList()
    },
    /** 切换每页条数 */
    handleSizeChange(val) {
      console.log(`每页 ${val} 条`)
      this.currentSize = val
      this.getList()
    },
    /** 切换当前页 */
    handleCurrentChange(val) {
      console.log(`当前页: ${val}`)
      this.currentPage = val
      this.getList()
    },
    /** 获取表数据 */
    async getList() {
      const params = {
        pageno: this.currentPage,
        pagesize: this.currentSize,
        printerModel: this.printerModel
      }
      const res = await fetchListByPage(params)
      if (res.head.ret === 0) {
        const data = res.data
        this.total = data.totalCount
        this.tableList = data.result
      }
    },
    clearSelection() {
      this.$refs.multipleTable.clearSelection()
    },
    /** 勾选事件 */
    handleSelectionChange(val) {
      this.multipleSelection = val
      console.log('handleSelectionChange: ' + this.multipleSelection)
    },
    /** 编辑 */
    handleEdit(index, row) {
      this.form = row
      this.form['autoCale'] = '1'
      this.form.autoCale = "1"
      this.showDialog = true
      this.fileList = []
      this.file = undefined
      this.showUrl = true
      this.onlyUpload = false
      this.getAllByDriverData()
    },
    /** 编辑 仅上传文件 */
    handleOnlyFile(index, row) {
      this.form = row
      this.form['autoCale'] = '1'
      this.showDialog = true
      this.fileList = []
      this.file = undefined
      this.showUrl = true
      this.onlyUpload = true
    },

    /** 删除单行 */
    async handleDeleteRow(index, row) {
      const params = {
        id: row.id
      }
      this.$confirm('此操作将永久删除该记录, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          deleteRecord(params).then(res => {
            if (res.head.ret === 0) {
              this.$message({
                type: 'success',
                message: '删除成功!'
              })
              this.getList()
            }
          })
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    },
    /** 删除单行 */
    async handleMultiDel() {
      this.$confirm('此操作将永久删除记录, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          let i = 0
          const len = this.multipleSelection.length
          for (i = 0; i < len; i++) {
            const params = {
              id: this.multipleSelection[i].id
            }
            deleteRecord(params).then(res => {
              if (res.head.ret === 0) {
                this.$message({
                  type: 'success',
                  message: '删除成功!'
                })
                this.getList()
              }
            })
          }
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    },

    // 表单编辑
    /** 保存 */
    submitForm() {
      this.$refs['ruleForm'].validate(valid => {
        if (valid) {
          this.form.autoCale = this.autoCale
          this.loading = true
          console.log(this.file)
          if (!this.file) {
            this.saveConfig()
            return
          }
          oss.uploadFile(this.file, (result, error) => {
            if (error) {
              console.log('error')
              console.log(error)
              this.$message.error('上传文件失败')
              this.loading = false
              return
            }
            if (result) {
              this.form.url = result.url
              this.saveConfig()
              this.getList()
            }
          })
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    async saveConfig() {
      console.log('this.form')
      console.log(this.form)
      this.form['type'] = 'printer'
      const res = await saveRecord(this.form)
      if (res.head.ret === 0) {
        this.$message.success('保存成功')
      }
      this.loading = false
      this.showDialog = false
      this.onlyUpload = false
      this.getList()
    },
    /** 点击批量修改 */
    handleMultiModify() {
      this.showMuliDialog = true
    },
    /** 批量保存 */
    submitMultiForm() {
      this.$refs['ruleMultiForm'].validate(valid => {
        if (valid) {
          this.loading = true
          console.log('this.multiForm: ', this.multiForm)
          this.doMultiSave()
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    async doMultiSave() {
      let i = 0
      const len = this.multipleSelection.length
      let count = 0
      for (i = 0; i < len; i++) {
        const data = { ...this.multipleSelection[i] }
        data.version = this.multiForm.version
        data.remark = this.multiForm.remark
        const res = await saveRecord(data)
        if (res.head.ret === 0) {
          count++
        }
      }
      this.$message.success('保存成功[' + count + ']条, 共' + len + '条')
      this.loading = false
      this.showMuliDialog = false
      this.getList()
    },

    /** 移除之前 */
    handleRemove(file, fileList) {
      console.log('handleRemove')
      this.showUrl = true
    },
    /** 上传前 */
    beforeUpload(file) {
      return false
    },
    changeFile(file) {
      console.log('changeFile')
      console.log(file)
      this.file = file.raw
      this.fileList.pop()
      this.fileList.push({
        name: file.name,
        url: file.name
      })
      this.showUrl = false
    },
    async updateSubmit() {
      const that = this;
      this.$confirm("是否提交审核?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          updatePrintDriverSubmit(that.form).then(res => {
            if (res.head.ret === 0) {
              that.$message.success("提交成功");
            }
            that.loading = false;
            that.showDialog = false;
            that.onlyUpload = false;
            that.getList();
          });
        })
        .catch(() => { });
    },
    async updateTakeDown() {
      const that = this;
      this.$confirm("是否确定临时下架?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          updatePrintDriverSubmit(that.form).then(res => {
            if (res.head.ret === 0) {
              that.$message.success("下架成功");
            }
            that.loading = false;
            that.showDialog = false;
            that.onlyUpload = false;
            that.getList();
          });
        })
        .catch(() => { });
    },
    async updateReject() {
      const that = this;
      this.$confirm("是否拒绝该固件版本?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          updatePrintDriverReject(that.form).then(res => {
            if (res.head.ret === 0) {
              that.$message.success("提交成功");
            }
            that.loading = false;
            that.showDialog = false;
            that.onlyUpload = false;
            that.getList();
          });
        })
        .catch(() => { });
    },
    async updatePass() {
      const that = this;
      this.$confirm("是否正式发布该固件版本?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          updatePrintDriverPass(this.form).then(res => {
            if (res.head.ret === 0) {
              this.$message.success("提交成功");
            }
            that.loading = false;
            that.showDialog = false;
            that.onlyUpload = false;
            that.getList();
          });
        })
        .catch(() => { });
    }
  }
}
</script>

<style lang="scss" scoped>
.app-container {
  margin: 20px;
  .control-container {
    margin-bottom: 20px;
  }
  .choose-container {
    margin-bottom: 20px;
    background-color: #e6f7ff;
    border: 1px solid #91d5ff;

    span {
      margin-left: 20px;
      line-height: 48px;
    }
    a {
      margin-left: 20px;
      color: #1890ff;
      text-decoration: none;
      background-color: transparent;
      outline: none;
      cursor: pointer;
      transition: color 0.3s;
    }
  }
}
</style>
