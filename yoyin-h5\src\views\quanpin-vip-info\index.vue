<template>
  <div class="app-container">
    <div v-show="hasLogin">
      <div class="table-query-container">
        <!-- <el-input v-model="params.nickName" type="text" maxlength="15" style="width:120px;padding-right: 10px" /> -->
        <span class="demonstration" style="padding-right: 10px">订单编号:</span>
        <el-input
          v-model="params.orderNo"
          type="text"
          maxlength="15"
          style="width: 120px; padding-right: 10px"
        />
        <span class="demonstration" style="padding-right: 10px">订单状态:</span>
        <el-select v-model="params.orderStatus" placeholder="请选择状态">
          <el-option label="全部" value="" />
          <el-option label="已支付" value="审核通过" />
          <el-option label="未支付" value="已删除" />
        </el-select>
        <span class="demonstration" style="padding-right: 10px">支付时间:</span>
        <el-date-picker
          v-model="timeRange"
          :value-format="timeFormat"
          :format="timeFormat"
          :unlink-panels="true"
          type="datetimerange"
          :clearable="false"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          @change="changeTimeRange"
        />
        <el-button type="primary" @click="getList">查询</el-button>
      </div>
      <div class="table-container">
        <el-table
          ref="multipleTable"
          :data="list"
          tooltip-effect="dark"
          style="width: 100%"
          border
        >
          <el-table-column label="订单编号">
            <template slot-scope="scope">{{ scope.row.orderNo }}</template>
          </el-table-column>
          <!-- <el-table-column label="用户账号">
            <template slot-scope="scope">{{ scope.row.userLoginDto.accountInfoDto
              && scope.row.userLoginDto.accountInfoDto[0] ? scope.row.userLoginDto.accountInfoDto[0].account : scope.row.createuserid + '已注销' }}</template>
          </el-table-column>
          <el-table-column label="用户id">
            <template slot-scope="scope">{{ scope.row.userLoginDto.userInfoDto ? scope.row.userLoginDto.userInfoDto.codeId : scope.row.createuserid + '已注销' }}</template>
          </el-table-column> -->
          <el-table-column label="套餐类型">
            <template slot-scope="scope">{{ scope.row.title }}</template>
          </el-table-column>
          <el-table-column label="金额(元)">
            <template slot-scope="scope">{{ scope.row.goodsPrice }}</template>
          </el-table-column>
          <el-table-column label="订单状态">
            <template slot-scope="scope">{{ scope.row.orderStatus }}</template>
          </el-table-column>
          <el-table-column label="支付时间">
            <template slot-scope="scope">{{ scope.row.createtime }}</template>
          </el-table-column>
          <!-- <el-table-column label="操作" width="230" align="center">
            <template slot-scope="scope">
              <el-button size="mini" @click="handleEdit(scope.$index, scope.row)">编辑</el-button>
              <el-button
                size="mini"
                type="danger"
                @click="handleDeleteRow(scope.$index, scope.row)"
              >删除</el-button>
            </template>
          </el-table-column> -->
        </el-table>
      </div>
      <div class="pagination-container" style="margin-top: 20px">
        <el-pagination
          :current-page="params.pageNo"
          :page-sizes="[10, 20, 30, 40]"
          :page-size="params.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
    <div v-show="!hasLogin" class="logindiv">
      <div class="logindiv-content">
        <div class="logindiv-content-login">登录</div>
        <el-form
          ref="loginForm"
          :model="loginForm"
          class="login-form"
          label-position="left"
        >
          <el-form-item prop="username">
            <el-input
              v-model="loginForm.username"
              placeholder="用户名"
              name="username"
              type="text"
            />
          </el-form-item>

          <el-form-item prop="password">
            <el-input
              v-model="loginForm.password"
              type="password"
              placeholder="密码"
              name="password"
              tabindex="2"
              auto-complete="on"
              @keyup.enter.native="handleLogin"
            />
          </el-form-item>

          <el-button
            :loading="loading"
            type="primary"
            style="width: 100%; margin-bottom: 30px"
            @click.native.prevent="handleLogin"
            >登录</el-button
          >
        </el-form>
      </div>
    </div>
  </div>
</template>
<script>
// import { fetchListByPage as fetchGoodsList } from '@/api/quanpin/membergoods'
// import { getNoteDetail } from '@/api/quanpin/quanpinorder'
import { fetchListByPage } from './assets/js/api.js'
import Cookies from 'js-cookie'
// 加个cookies的
export default {
  data() {
    return {
      timeRange: [],
      hasLogin: false,
      loginForm: {
        username: '',
        password: ''
      },
      // 查询参数
      params: {
        pageNo: 1,
        pageSize: 10,
        type: ''
      },
      /** 列表 */
      list: [],
      /** 总条数 */
      total: 0,
      userQueryList: [],
      showDialog: false,
      loading: false,
      /** 时间格式 */
      timeFormat: 'yyyy-MM-dd',
      goodsList: []
    }
  },
  mounted() {
    // this.getList()
    if (Cookies.get('quanpin-vip-info')) {
      this.hasLogin = true
      this.getList()
    } else {
      this.hasLogin = false
    }
  },
  methods: {
    /** 获取列表 */
    async getList() {
      const res = await fetchListByPage(this.params)
      if (res.head.ret === 0) {
        this.list = res.data.result
        this.total = res.data.totalCount
      }
    },
    /** 切换每页条数 */
    handleSizeChange(val) {
      this.params.pageSize = val
      this.getList()
    },
    /** 切换当前页 */
    handleCurrentChange(val) {
      this.params.pageNo = val
      this.getList()
    },
    /** 选择时间段 */
    changeTimeRange(val) {
      if (val) {
        this.params.payBeginDate = val[0]
        this.params.payEndDate = val[1]
      }
    },
    formatUnitAndAmount(row) {
      return ''
    },
    handleLogin() {
      if (
        this.loginForm.username !== 'quanpin' ||
        this.loginForm.password !== 'quanpin@api123'
      ) {
        this.$message({
          message: '用户名或密码不正确',
          type: 'error'
        })
      } else {
        this.hasLogin = true
        Cookies.set('quanpin-vip-info', '60deb78c52faff0001df3e2f', 86400)
        this.getList()
      }
    },
    showPwd() {
      if (
        this.loginForm.username !== 'quanpin' ||
        this.loginForm.password !== 'quanpin@api123'
      ) {
        this.$message({
          message: '警告哦，这是一条警告消息',
          type: 'error'
        })
      }
    }
  }
}
</script>
<style lang="scss" scoped>
html {
  font-size: 16px;
  background: #f5f5f5;
  padding-top: 2.5rem;
  padding-bottom: 100px;
}
.app-container {
  font-size: 16px;
  padding: 30px;
}

.table-query-container {
  margin-bottom: 30px;
}

.logindiv {
  display: flex;

  &-content {
    width: 500px;
    margin: 0 auto;
    padding-top: 150px;
    &-login {
      text-align: center;
      line-height: 60px;
      font-size: 20px;
      font-weight: bold;
    }
  }
}
</style>
