import MessageBox from './message'
let Message = {
  install(Vue) {
    let Msg = Vue.extend(MessageBox)

    Vue.prototype.$message = options => {
      let vm = new Msg({
        el: document.createElement('div')
      })
      document.body.appendChild(vm.$el)
      if (typeof options === 'string') {
        vm.msg = options
      } else {
        vm.msg = options.msg
      }
      setTimeout(() => {
        document.body.removeChild(vm.$el)
        vm.$destroy()
      }, 3000)
    }
  }
}
export default Message
