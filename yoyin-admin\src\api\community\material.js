import request from '@/utils/request'

// 翻页查询
const LOAD_DATA_URL = '/platform/gam/community/v1/material/getpagelist'
// 删除记录
const DELETE_RECORD_URL = '/platform/gam/community/v1/material/del'
// 添加tab分类
const Add_Tab__URL = '/platform/gam/community/v1/material/addMaterial'
// 添加tab分类
const Add_UPDATE_Tab_URL = '/platform/gam/community/v1/material/addOrUpdateMaterial'
// 删除tab分类
const Del_Tab__URL = '/platform/gam/community/v1/material//deleteMaterial'
// 查询所有tab分类
const LOAD_DATA_URL_TAB = '/platform/gam/community/v1/material/getAllMaterialList'

// 修改tab分类
const Update_Tab__URL = '/platform/gam/community/v1/material/updateMaterial'
// 加载素材类型
const LOAD_TYPES_URL = '/platform/gam/community/v1/material/getmateriallist'
const SAVE_RECORD_URL = '/platform/gam/community/v1/material/saveorupdate'
const LOAD_LABEL_URL = '/platform/gam/community/v1/label/findlabelpage'
// 素材详情
const DETAIL_URL = '/platform/gam/community/v1/material/getmaterialresource'

const LOAD_ROOT_TYPES_URL = '/platform/gam/community/v1/material/getmaterialrootlist'

/**
 * 获取素材类型
 */
export const getTypes = async() => {
  return request({
    url: LOAD_TYPES_URL,
    method: 'get'
  })
}

export const getRootTypes = async() => {
  return request({
    url: LOAD_ROOT_TYPES_URL,
    method: 'get'
  })
}

/**
 * 查询列表数据
 * @param params 参数
 */
export const fetchListByPage = async params => {
  return request({
    url: LOAD_DATA_URL,
    method: 'get',
    params
  })
}

/**
 * 删除记录
 * @param ids 记录id
 */
export const deleteRecord = async(params) => {
  return request({
    url: DELETE_RECORD_URL,
    method: 'get',
    params
  })
}
/**
 * 标签翻页查询
 * @param {Number} page 页码 从1开始
 */
export const findLabelPage = async(params) => {
  return request({
    url: LOAD_LABEL_URL,
    method: 'get',
    params
  })
}
/**
 * 获取素材详情
 */
export const getMaterialDetail = async(params) => {
  return request({
    url: DETAIL_URL,
    method: 'get',
    params
  })
}

/**
 * 保存记录
 * @param ids 记录id
 */
export const saveRecord = async params => {
  return request({
    url: SAVE_RECORD_URL,
    method: 'post',
    data: params
  })
}

/**
 * 添加tab分类
 * @param ids 记录id
 */
export const addMaterialType = async params => {
  return request({
    url: Add_Tab__URL,
    method: 'post',
    data: params
  })
}

/**
 * 添加tab分类
 * @param ids 记录id
 */
export const addOrUpdateMaterialType = async params => {
  return request({
    url: Add_UPDATE_Tab_URL,
    method: 'post',
    data: params
  })
}

/**
 * 修改tab分类
 * @param
 */
export const updateMaterialType = async params => {
  return request({
    url: Update_Tab__URL,
    method: 'post',
    data: params
  })
}
/**
 * 删除类型记录
 * @param ids 记录id
 */
export const deleteType = async params => {
  return request({
    url: Del_Tab__URL,
    method: 'post',
    data: params
  })
}

export const findListTab = async params => {
  return request({
    url: LOAD_DATA_URL_TAB,
    method: 'post',
    data: params
  })
}
