import request from '@/utils/request'

// 翻页查询
const LIST_URL = '/api/xeasylabel/xeasylabel/v1/dict/findPage'
const LIST_TYPE_URL = '/api/xeasylabel/xeasylabel/v1/dict/findListByType'
const DELETE_RECORD_URL = '/api/xeasylabel/xeasylabel/v1/dict/del'
const SAVE_URL = '/api/xeasylabel/xeasylabel/v1/dict/addOrUpdate'
const DISTINCT_TYPE_URL = '/api/xeasylabel/xeasylabel/v1/dict/findDistinctTypeList'
// 删除记录

/**
 * 查询列表数据
 * @param params 参数
 */
export const fetchListByPage = async params => {
  return request({
    url: LIST_URL,
    method: 'get',
    params: params
  })
}

export const fetchDictTypeList = async params => {
  return request({
    url: LIST_TYPE_URL,
    method: 'get',
    params: params
  })
}

/**
 * 删除记录
 * @param {String} id 记录id
 */
export const deleteRecord = async params => {
  return request({
    url: DELETE_RECORD_URL,
    method: 'get',
    params
  })
}

/** 保存记录 */
export const saveRecord = async params => {
  return request({
    url: SAVE_URL,
    method: 'post',
    params
  })
}

export const fetchDistinctTypeList = async() => {
  return request({
    url: DISTINCT_TYPE_URL,
    method: 'get'
  })
}

