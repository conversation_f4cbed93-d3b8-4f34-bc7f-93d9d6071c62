var gulp = require('gulp')
var zip = require('gulp-zip')
var sftp = require('gulp-sftp')
var sequence = require('gulp-sequence')
var del = require('del')
var fs = require('fs')
var path = require('path')
var request = require('request')

/**
 * 清理release目录和zip目录
 */
gulp.task('clean', function() {
  del.sync(['release/**', 'zip/**'])
})

/**
 * 将dist的文件复制到release/activity/V6中
 */
gulp.task('release', function() {
  return gulp.src('dist/**').pipe(gulp.dest('release/activity/V6'))
})

/**
 * 将release的子文件压缩成zip
 */
gulp.task('zip', function() {
  return gulp
    .src('release/**')
    .pipe(zip('activity.zip'))
    .pipe(gulp.dest('zip'))
})

/**
 * 使用sftp上传release到local
 */
gulp.task('upload:local', function() {
  return gulp.src('release/**').pipe(
    sftp({
      // host: '************',
      // user: 'admin',
      // pass: '1b88ab6d',
      // remotePath: '/home/<USER>/html/html/'
    })
  )
})

/**
 * 使用sftp上传release到test
 */
gulp.task('upload:test', function() {
  return gulp.src('release/**').pipe(
    sftp({
      // host: '*************',
      // user: 'ui',
      // pass: 'sytui8680938',
      // remotePath: '/syt/html/html/'
    })
  )
})

/**
 * 将zip中的activity.zip上传到oss
 */
gulp.task('upload:oss', function() {
  var formData = {
    uploadFile: fs.createReadStream(path.join(__dirname) + '/zip/activity.zip')
  }
  request.post(
    {
      url:
        'http://qmp.sythealth.com/qmp/setting/uploadfiletooss?key=a7xIWPfprKnnuc5',
      formData: formData
    },
    function(err, httpResponse, body) {
      if (err) {
        return console.error('upload failed:', err)
      }
      console.log('upload successful!')
    }
  )
})

/**
 * 默认任务，运行gulp默认执行当前任务
 * sequence：同步按照顺序执行task
 */
gulp.task(
  'default',
  sequence('clean', 'release', 'zip', 'upload:local', 'upload:test')
)
