{"remainingRequest": "D:\\workspace\\xplife\\yoyin-ui\\yoyin-admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\workspace\\xplife\\yoyin-ui\\yoyin-admin\\src\\views\\office\\material\\index.vue?vue&type=style&index=0&id=22b158ed&prod&lang=scss&scoped=true", "dependencies": [{"path": "D:\\workspace\\xplife\\yoyin-ui\\yoyin-admin\\src\\views\\office\\material\\index.vue", "mtime": 1751274032261}, {"path": "D:\\workspace\\xplife\\yoyin-ui\\yoyin-admin\\node_modules\\css-loader\\index.js", "mtime": 499162500000}, {"path": "D:\\workspace\\xplife\\yoyin-ui\\yoyin-admin\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\workspace\\xplife\\yoyin-ui\\yoyin-admin\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\workspace\\xplife\\yoyin-ui\\yoyin-admin\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\workspace\\xplife\\yoyin-ui\\yoyin-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\workspace\\xplife\\yoyin-ui\\yoyin-admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\r\n.app-container {\r\n  .list-contatiner {\r\n    .list-container-data {\r\n      &-cards {\r\n        margin-top: 20px;\r\n        margin-bottom: 20px;\r\n        display: flex;\r\n        flex-flow: row wrap;\r\n        justify-content: flex-start;\r\n        &-item {\r\n          width: 15%;\r\n          margin-left: 1%;\r\n          border: 1px solid grey;\r\n          margin-bottom: 10px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n", null]}