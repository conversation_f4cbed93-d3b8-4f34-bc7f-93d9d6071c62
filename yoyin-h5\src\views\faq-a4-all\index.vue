<template>
  <div class="content">
    <div
      v-if="
        language == 'zh-cn' ||
        language == 'zh-tw' ||
        language == 'zh_cn' ||
        language == 'zh_tw'
      "
    >
      <img v-if="index == 1" src="https://m.yoyin.net/h5/img/a4/1.png" />
      <img v-if="index == 2" src="https://m.yoyin.net/h5/img/a4/2.png" />
      <img v-if="index == 3" src="https://m.yoyin.net/h5/img/a4/3-1.png" />
      <img v-if="index == 3" src="https://m.yoyin.net/h5/img/a4/3-2.png" />
      <img v-if="index == 4" src="https://m.yoyin.net/h5/img/a4/4.png" />
      <img v-if="index == 5" src="https://m.yoyin.net/h5/img/a4/5.png" />
    </div>
    <div
      v-if="
        language != 'zh-cn' &&
        language != 'zh-tw' &&
        language != 'zh_cn' &&
        language != 'zh_tw'
      "
    >
      <img v-if="index == 1" src="https://m.yoyin.net/h5/img/a4/en/1.png" />
      <img v-if="index == 2" src="https://m.yoyin.net/h5/img/a4/en/2.png" />
      <img v-if="index == 3" src="https://m.yoyin.net/h5/img/a4/en/3.png" />
      <img v-if="index == 4" src="https://m.yoyin.net/h5/img/a4/en/4.png" />
      <img v-if="index == 5" src="https://m.yoyin.net/h5/img/a4/en/5.png" />
    </div>
  </div>
</template>

<script>
import { getUrlParameter } from '@/utils/common'
export default {
  data() {
    return {
      index: 1,
      language: ''
    }
  },
  components: {},
  async mounted() {
    // 判断url
    const query = getUrlParameter()
    this.index = query.index

    this.initPage()
  },
  methods: {
    getCookie(cName) {
      // alert(document.cookie)
      if (document.cookie.length > 0) {
        let cStart = document.cookie.indexOf(cName + '=')
        if (cStart !== -1) {
          cStart = cStart + cName.length + 1
          let cEnd = document.cookie.indexOf(';', cStart)
          if (cEnd === -1) {
            cEnd = document.cookie.length
          }
          return unescape(document.cookie.substring(cStart, cEnd))
        }
      }
      return ''
    },
    initPage() {
      // 页面初始化调用
      var lang = navigator.language || navigator.browserLanguage
      let locale = 'zh_CN'
      if (window.StarPrinterJS && window.StarPrinterJS.getLanguage()) {
        locale = window.StarPrinterJS.getLanguage()
      } else if (window.webkit && window.webkit.messageHandlers) {
        locale = this.getCookie('language')
        if (locale) {
          // do nothing
        } else {
          // locale = window.webkit.messageHandlers.getLanguage.postMessage(null)
          locale = navigator.language || navigator.browserLanguage
        }
      } else {
        locale = navigator.language || navigator.browserLanguage
      }
      this.language = locale.toLowerCase()
      // this.language = 'en-US'
      if (this.language === 'zh_cn' || this.language === 'zh_tw' || this.language === 'zh-cn' || this.language === 'zh-tw') {
        document.title = '常见问题'
      } else {
        document.title = 'Question'
      }
    }
  }
}
</script>

<style lang="scss">
// .content {
//   margin: 2em;
// }
img {
  width: 100%;
  object-fit: contain;
}
</style>
