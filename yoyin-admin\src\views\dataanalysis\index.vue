<!--
 * @Description: In User Settings Edit
 * @Author: your name
 * @Date: 2019-10-16 11:28:20
 * @LastEditTime: 2019-10-18 14:10:13
 * @LastEditors: Please set LastEditors
 -->
<template>
  <div class="app-container">
    <div class="tabs-container">
      <el-tabs v-model="activeName" @tab-click="handleClick">
        <el-tab-pane label="用户统计" name="user" :lazy="true">
          <div class="date-container">
            <el-date-picker
              v-model="userDate"
              type="daterange"
              align="right"
              unlink-panels
              range-separator="-"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="yyyy-MM-dd"
              :picker-options="pickerOptions"
              @change="handleChangeUserDate"
            />
          </div>
          <el-row style="background:#fff;padding:16px 16px 0;margin-bottom:32px;">
            <UserChart :chart-data="UserChartData" />
          </el-row>
          <div class="table-container">
            <el-table
              :data="userTable"
              border
              style="width: 100%"
            >
              <el-table-column
                prop="date"
                label="日期"
              />
              <el-table-column
                prop="num"
                label="数量"
              />
            </el-table>
          </div>

        </el-tab-pane>

        <!-- 动态统计 -->
        <el-tab-pane label="第三方接口调用" name="activity" :lazy="true">

          <div class="condition-wrapper">
            <div class="date-container">
              <el-date-picker
                v-model="activityDate"
                type="daterange"
                align="right"
                unlink-panels
                range-separator="-"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                value-format="yyyy-MM-dd"
                :picker-options="pickerOptions"
                @change="handleChangeAtivityDate"
              />
            </div>
            <el-radio-group v-model="actType" style="margin-bottom: 30px;" @change="changeActType">
              <el-radio-button label="">全部</el-radio-button>
              <el-radio-button label="baiduOrc">百度orc</el-radio-button>
              <el-radio-button label="XueKeCond">试题推送</el-radio-button>
              <el-radio-button label="XueKeKeyword">关键词搜题</el-radio-button>
              <el-radio-button label="XueKeSimilar">相似题搜题</el-radio-button>
              <el-radio-button label="XueKeText">拍搜（精品）</el-radio-button>
              <el-radio-button label="XueKeTiku">拍搜（海量）</el-radio-button>
            </el-radio-group>
          </div>

          <el-row style="background:#fff;padding:16px 16px 0;margin-bottom:32px;">
            <FeedChart :chart-data="FeedChartData" />
          </el-row>
          <div class="table-container">
            <el-table
              :data="actTable"
              border
              style="width: 100%"
            >
              <el-table-column
                prop="userId"
                label="用户"
              />
              <el-table-column
                prop="count"
                label="数量"
              />
              <el-table-column label="操作" width="120" header-align="center" align="center">
                <template slot-scope="scope">
                  <el-button size="mini" type="error" @click="showDetailsPageByUserId(scope.row)">详情</el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
    <el-dialog title="调用详情" :visible.sync="userCallDetailsWin" :append-to-body="true" @opened="initDetailsPageByUserId" width="80%">
      <user-call-logs ref="userCallDetailsDialog"></user-call-logs>
    </el-dialog>
  </div>
</template>
<script>
import UserChart from './component/UserChart'
import FeedChart from './component/FeedChart'
import UserCallLogs from './component/UserCallLogs'
import { parseTime } from '@/utils/index'
import { getUserList, getActivityData, getApiCallCountTop } from '@/api/dataanalysis'
const lineChartData = {
  newVisitis: {
    actualData: [120, 8, 9, 15, 16, 14, 14, 100],
    xData: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun', 'test'],
    xinterval: 1
  }
}
export default {
  name: 'Analysis',
  components: { UserChart, FeedChart, UserCallLogs },
  data() {
    return {
      userCallDetailsWin: false,
      actType: '',
      activeName: 'activity',
      pickerOptions: {
        shortcuts: [{
          text: '最近一周',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近一个月',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近三个月',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
            picker.$emit('pick', [start, end])
          }
        }]
      },
      userDate: [],
      activityDate: [],
      // x轴数据
      xData: [],
      // 曲线图数据
      sData: [],
      // 动态X轴数据
      actXData: [],
      // 动态曲线数据
      actSData: [],
      UserChartData: lineChartData.newVisitis,
      FeedChartData: lineChartData.newVisitis,
      actTable: [],
      selectUserId: ''
    }
  },
  computed: {
    userTable() {
      if (this.xData && this.sData) {
        const cols = []
        for (var i = 0; i < this.xData.length; i++) {
          const col = {
            date: this.xData[i],
            num: this.sData[i]
          }
          cols.push(col)
        }
        return cols
      }
      return []
    }
  },

  created() {
    this.initDate()
    this.pickerOptions.disabledDate = function(time) {
      return time.getTime() > Date.now() - 8.64e7
    }
  },
  mounted() {
    this.getUser()
    this.getActList()
    this.getActTable()
  },
  methods: {
    showDetailsPageByUserId(row) {
      this.userCallDetailsWin = true
      this.selectUserId = row.userId
      
    },
    initDetailsPageByUserId() {
      this.$refs["userCallDetailsDialog"].initData(this.activityDate[0], this.activityDate[1], this.selectUserId, this.actType)
    },
    getActTable(){
      const params = {
        startdate: this.activityDate[0],
        enddate: this.activityDate[1],
        businessType: this.actType
      }
      getApiCallCountTop(params).then((response) => {
        if (response.data) {
          console.log('getActTable', response.data)
          this.actTable = response.data
        }
      })
    },
    /** 切换tab */
    handleClick(tab, event) {
    },
    /**
     * 选择用户时间
     */
    handleChangeUserDate(val) {
      this.userDate = val
      this.getUser()
    },
    /**
     * 选择动态时间
     */
    handleChangeAtivityDate(val) {
      this.activityDate = val
      this.getActList()
      this.getActTable()
    },
    /** 初始化选择器时间 默认一个星期 */
    initDate() {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
      this.userDate = [parseTime(start, '{y}-{m}-{d}'), parseTime(end, '{y}-{m}-{d}')]
      this.activityDate = [parseTime(start, '{y}-{m}-{d}'), parseTime(end, '{y}-{m}-{d}')]
    },
    /**
     * 获取用户
     */
    getUser() {
      const params = {
        startdate: this.userDate[0],
        enddate: this.userDate[1]
      }
      getUserList(params).then((response) => {
        if (response.data) {
          console.log(response.data)
          const catagory = response.data.categories
          const items = response.data.items
          this.xData = catagory
          this.sData = items[0].data
          // 更新曲线图
          let interval = 0
          if (catagory.length > 7) {
            interval = Math.floor(catagory.length / 7)
          }
          this.UserChartData = {
            actualData: this.sData,
            xData: this.xData,
            xinterval: interval
          }
        }
      })
    },
    /** 修改动态类型 */
    changeActType(label) {
      this.actType = label
      this.getActList()
      this.getActTable()
    },
    /** 获取动态数据 */
    getActList() {
      const params = {
        startdate: this.activityDate[0],
        enddate: this.activityDate[1],
        businessType: this.actType
      }
      getActivityData(params).then(response => {
        console.log(response.data)
        const catagory = response.data.categories
        const items = response.data.items
        this.actXData = catagory
        this.actSData = items[0].data
        // 更新曲线图
        let interval = 0
        if (catagory.length > 7) {
          interval = Math.floor(catagory.length / 7)
        }
        this.FeedChartData = {
          actualData: this.actSData,
          xData: this.actXData,
          xinterval: interval
        }
      })
    }

  }

}
</script>

<style lang="scss" scoped>

.date-container{
  margin-bottom: 20px;
}
.condition-wrapper{
  display: flex;
  flex-direction: row;
  justify-content:space-between;
}
</style>
