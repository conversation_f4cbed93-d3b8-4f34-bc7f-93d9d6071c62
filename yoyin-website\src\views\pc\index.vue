<template>
  <div style="height: 100%">
    <my-header @clickMenu="clickMenu"></my-header>
    <div class="lines"/>
    <div class="contents">
      <div class="contents-index" v-if="type===0"><home></home></div>
      <div class="contents-products" v-if="type===1"><product></product></div>
      <div class="contents-service" v-if="type===2"><service></service></div>
      <div class="contents-download" v-if="type===3"><download></download></div>
      <div class="contents-introduce" v-if="type===4"><about-us></about-us></div>
    </div>
    <!-- <div class="lines2"/> -->
    <index-footer></index-footer>
  </div>
</template>

<script>
import IndexFooter from './components/Footer.vue'
import MyHeader from './components/Header.vue'
import Home from './components/home'
import Product from './components/product'
import Service from './components/service'
import Download from './components/download'
import AboutUs from './components/about'

export default {
  name: 'MainIndex',
  components: {IndexFooter, MyHeader, Home, Product, Service, Download, AboutUs},
  data () {
    return {
      msg: 'Welcome to Your Vue.js App',
      type: 0
    }
  },
  methods: {
    clickMenu (type) {
      this.type = type
    }
  }
}
</script>

<style scoped>
.contents {
  width: 100%;
  margin: 0 auto;
}
.index-footer {
  width: 1200px;
  margin: 0 auto;
  min-height: 150px;
  bottom: 0px;
  background-color: azure;
}
.lines {
  min-height: 1px;
  border-top: 1px solid #CCCCCC;
}
.lines2 {
  min-height: 1px;
  border-top: 1px solid #F1F1F1;
}
</style>
