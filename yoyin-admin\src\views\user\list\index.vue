<!--
 * @Description: In User Settings Edit
 * @Author: your name
 * @Date: 2019-10-16 11:28:20
 * @LastEditTime: 2019-10-16 11:38:48
 * @LastEditors: Please set LastEditors
 -->
<template>
  <div class="app-container">
    <div class="table-query-container">
      <el-row>
        <span class="demonstration" style="padding-right: 10px">注册时间 </span>
        <el-date-picker
          v-model="timeRange"
          :value-format="timeFormat"
          :format="timeFormat"
          :unlink-panels="true"
          type="datetimerange"
          :clearable="false"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          @change="changeTimeRange"
        />
        <el-button
          v-permission="'btn-menuUsermanage-user-edit'"
          type="primary"
          @click="exportHandleQuery"
          >导出明细</el-button
        >
        <el-button type="primary" @click="handleJyWhiteListQuery"
          >查询菁优接口白名单</el-button
        >
      </el-row>
      <el-row v-if="downloadUrl" style="line-height: 50px; color: red">
        因数据大，晚10分钟后，自行从以下地址下载：
        <a :href="downloadUrl">{{ downloadUrl }}</a>
      </el-row>
      <el-row style="display: flex; line-height: 40px; padding-top: 15px">
        <span class="demonstration" style="padding-right: 10px">CODE-ID</span>
        <el-input
          v-model="codeId"
          type="text"
          maxlength="10"
          style="width: 120px; padding-right: 10px"
        />
        <span class="demonstration" style="padding-right: 10px">昵称</span>
        <el-input
          v-model="nickName"
          type="text"
          maxlength="10"
          style="width: 120px; padding-right: 10px"
        />
        <span class="demonstration" style="padding-right: 10px">头衔</span>
        <el-select v-model="userTitleType" placeholder="请选择头衔">
          <el-option
            v-for="item in titleTypeList"
            :key="item.id"
            :label="item.name"
            :value="item.code"
          />
        </el-select>
        <span class="demonstration" style="padding-right: 10px">用户身份</span>
        <el-cascader v-model="userRole" :options="userRoleData" />
        <el-button type="primary" @click="handleQuery">查询</el-button>
      </el-row>
      <el-row style="display: flex; line-height: 40px; padding-top: 15px">
        <div
          v-permission="'btn-menuUsermanage-user-edit'"
          class="table-query-container"
        >
          <span class="demonstration" style="padding-right: 10px"
            >临时账号：</span
          >
          <el-input
            v-model="defaultAccount"
            type="text"
            maxlength="100"
            style="width: 200px; padding-right: 10px"
          />
          <span class="demonstration" style="padding-right: 10px"
            >临时密码：</span
          >
          <el-input
            v-model="defaultPassword"
            type="text"
            maxlength="100"
            style="width: 200px; padding-right: 10px"
          />
          <el-button type="primary" @click="settingDefaultMgrInfo"
            >设置</el-button
          >
        </div>
      </el-row>
    </div>
    <div class="table-container">
      <el-table
        ref="multipleTable"
        :data="list"
        tooltip-effect="dark"
        style="width: 100%"
        border
        max-height="700px"
      >
        <el-table-column label="头像" width="60">
          <template slot-scope="scope">
            <el-avatar :size="30" :src="scope.row.userPic" />
          </template>
        </el-table-column>
        <el-table-column prop="nickName" label="用户昵称" />
        <el-table-column prop="codeId" label="CodeId" />
        <el-table-column label="禁用操作">
          <template slot-scope="scope">
            <el-button
              v-if="scope.row.forbidden === 0"
              v-permission="'btn-menuUsermanage-user-edit'"
              type="primary"
              @click="enableUser(scope.row.userId, true)"
              >禁用</el-button
            >
            <el-button
              v-if="scope.row.forbidden === 1"
              v-permission="'btn-menuUsermanage-user-edit'"
              type="primary"
              @click="enableUser(scope.row.userId, false)"
              >恢复</el-button
            >
          </template>
        </el-table-column>
        <!-- <el-table-column prop="userTitleObj.name" label="头衔" /> -->
        <el-table-column prop="userRole" label="用户身份">
          <template slot-scope="scope">
            {{ scope.row.roleObj ? scope.row.roleObj.name : "" }} |
            {{ scope.row.gradeLevelObj ? scope.row.gradeLevelObj.name : "" }}
          </template>
        </el-table-column>
        <!-- <el-table-column label="积分情况">
          <template slot-scope="scope">
            {{ scope.row.curCoinNum + '/' + scope.row.totalCoinNum }}
          </template>
        </el-table-column>
        <el-table-column prop="mileInfo" label="里程及排名" />
        <el-table-column prop="machineInfo" label="使用情况" /> -->
        <el-table-column prop="createtime" label="注册时间" />
        <el-table-column
          prop="userLoginHistoryDto.latestLoginTime"
          label="最近登录时间"
        />
        <el-table-column
          prop="userLoginHistoryDto"
          label="使用情况"
          :formatter="useInfoFormatter"
          width="250"
        >
          <template slot-scope="scope">
            app版本：
            {{
              scope.row.userLoginHistoryDto != null
                ? scope.row.userLoginHistoryDto.appVersion
                : ""
            }}
            <br />
            手机系统/型号：{{
              scope.row.userLoginHistoryDto != null
                ? scope.row.userLoginHistoryDto.phoneType
                : ""
            }}
            <br />
            连接过设备：{{
              scope.row.userLoginHistoryDto != null
                ? scope.row.userLoginHistoryDto.usedDevices
                : ""
            }}
          </template>
        </el-table-column>
        <!-- <el-table-column prop="mileInfo" label="里程及排名" /> -->
        <el-table-column label="操作">
          <template slot-scope="scope">
            <a
              style="
                color: #1890ff;
                text-decoration: none;
                cursor: pointer;
                transition: color 0.3s;
              "
              @click="handleActivity(scope.$index, scope.row)"
              >查看动态</a
            >
            <!-- <a v-permission="'btn-menuUsermanage-user-edit'" style="color: rgb(255, 24, 24);  text-decoration: none;  cursor: pointer; transition: color 0.3s;" @click="handleSetting(scope.$index, scope.row)">设置头衔</a> -->
            <a
              v-permission="'btn-menuUsermanage-user-edit'"
              style="
                color: #1890ff;
                text-decoration: none;
                cursor: pointer;
                transition: color 0.3s;
              "
              @click="binding(scope.row.userId)"
              >临时绑定</a
            >
            <a
              style="
                color: #1890ff;
                text-decoration: none;
                cursor: pointer;
                transition: color 0.3s;
              "
              @click="showJyApiCount(scope.row.userId)"
              >菁优api次数</a
            >
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="pagination-container" style="margin-top: 20px">
      <el-pagination
        :current-page="params.pageno"
        :page-sizes="[10, 20, 30, 40]"
        :page-size="params.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
    <el-dialog :visible.sync="showDialog" width="70%" title="选择头衔">
      <title-type-select
        :user-id="selectUserId"
        :title-type="selectTitleType"
        @handleSelectTitleType="updateTitleType"
      />
    </el-dialog>

    <el-dialog
      :visible.sync="whiteListDialog"
      width="70%"
      title="菁优接口白名单"
    >
      <el-table
        ref="multipleTable"
        :data="whiteListData"
        tooltip-effect="dark"
        style="width: 100%"
        border
        max-height="700px"
      >
        <el-table-column label="用户codeId" width="60">
          <template slot-scope="scope">
            {{
              scope.row.userObj.userInfoDto !== null
                ? scope.row.userObj.userInfoDto.codeId
                : ""
            }}
          </template>
        </el-table-column>
        <el-table-column prop="nickName" label="用户昵称">
          <template slot-scope="scope">
            {{
              scope.row.userObj.userInfoDto !== null
                ? scope.row.userObj.userInfoDto.nickName
                : ""
            }}
          </template>
        </el-table-column>
        <el-table-column prop="account" label="账号">
          <template slot-scope="scope">
            {{
              scope.row.userObj.accountInfoDto !== null
                ? scope.row.userObj.accountInfoDto[0].account
                : ""
            }}
          </template>
        </el-table-column>
        <el-table-column prop="expressTime" label="有效期">
          <template slot-scope="scope">
            {{ formatExpressTime(scope.row.expressTime) }}
          </template>
        </el-table-column>
        <el-table-column label="删除">
          <template slot-scope="scope">
            <a
              style="
                color: #1890ff;
                text-decoration: none;
                cursor: pointer;
                transition: color 0.3s;
              "
              @click="removeWhiteList(scope.row.userObj.userInfoDto.userId)"
              >删除</a
            >
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>

    <el-dialog
      :visible.sync="jyRequestCountDialog"
      width="70%"
      title="菁优接口当日调用次数"
    >
      <el-form label-width="160px">
        <el-form-item label="按关键字搜索试题">
          <el-input v-model="jyRequestCount.keyword" />
          每日限制：100
        </el-form-item>
        <el-form-item label="按条件精选题库">
          <el-input v-model="jyRequestCount.cond" />
          每日限制：250
        </el-form-item>
        <el-form-item label="查看试题">
          <el-input v-model="jyRequestCount.viewques" />
          每日限制：150
        </el-form-item>
        <el-form-item label="推荐试题">
          <el-input v-model="jyRequestCount.nominateques" />
          每日限制：50
        </el-form-item>

        <el-form-item>
          <el-button @click="jyRequestCountDialog = false">返回</el-button>
          <el-button
            v-permission="'btn-menuUsermanage-user-jyapi'"
            type="primary"
            @click="submitTodayCount"
            >保存当日调用次数</el-button
          >
        </el-form-item>

        <el-form-item label="加入白名单">
          <el-radio-group v-model="time">
            <el-radio-button
              :label="item.key"
              v-for="item in timesData"
              :key="'time' + item.key"
              >{{ item.value }}</el-radio-button
            >
          </el-radio-group>

          <el-button
            v-permission="'btn-menuUsermanage-user-jyapi'"
            type="success"
            size="middle"
            @click="submitAddWileList"
            >点击加入白名单</el-button
          >
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>
<script>
import { fetchListByPage, saveUser, exportUserList, saveUserInfo, getDefaultManagerUserInfo, settingDefaultManagerUserInfo, bindingDefaultManagerUserId } from '@/api/user/list'
import { fetchTitleTypeList } from '@/api/user/titleType'
import TitleTypeSelect from './TitleTypeSelect'
import { queryJqApiWhiteList, addJqApiUserId, removeJqApiUserId, queryJqApiCountByUserId, settingJqApiCountByUserId } from '@/api/user/usercache'
export default {
  components: {
    TitleTypeSelect
  },
  data() {
    return {
      defaultAccount: '',
      defaultPassword: '',
      // 查询参数
      params: {
        startDate: '',
        endDate: '',
        pageNo: 1,
        pageSize: 10,
        sort: 'desc',
        sortType: 'time'
      },
      time: '86400',
      whiteListDialog: false,
      jyRequestCountDialog: false,
      timesData: [{
        key: '86400',
        value: '1天'
      }, {
        key: '2592000',
        value: '1个月'
      }, {
        key: '7776000',
        value: '3个月'
      }, {
        key: '********',
        value: '1年'
      }, {
        key: '********0',
        value: '10年'
      }],
      /** 列表 */
      list: [],
      /** 总条数 */
      total: 0,
      downloadUrl: '',
      /** 时间段 */
      timeRange: '',
      codeId: '',
      nickName: '',
      /** 时间格式 */
      timeFormat: 'yyyy-MM-dd',
      showDialog: false,
      selectUserId: '',
      selectTitleType: 0,
      titleTypeList: [],
      userTitleType: null,
      whiteListData: [],
      jyRequestCount: {},
      userRole: '',
      userRoleData: [{
        value: '1',
        label: '学生',
        children: [{
          value: 'xx',
          label: '小学',
          children: [{
            value: '1',
            label: '一年级'
          }, {
            value: '2',
            label: '二年级'
          }, {
            value: '3',
            label: '三年级'
          }, {
            value: '4',
            label: '四年级'
          }, {
            value: '5',
            label: '五年级'
          }, {
            value: '6',
            label: '六年级'
          }]
        }, {
          value: 'cz',
          label: '初中',
          children: [{
            value: '7',
            label: '初一'
          }, {
            value: '8',
            label: '初二'
          }, {
            value: '9',
            label: '初三'
          }]
        }, {
          value: 'gz',
          label: '高中',
          children: [{
            value: '10',
            label: '高一'
          }, {
            value: '11',
            label: '高二'
          }, {
            value: '12',
            label: '高三'
          }]
        }]
      }, {
        value: '2',
        label: '老师',
        children: [{
          value: 'xx',
          label: '小学',
          children: [{
            value: '1',
            label: '一年级'
          }, {
            value: '2',
            label: '二年级'
          }, {
            value: '3',
            label: '三年级'
          }, {
            value: '4',
            label: '四年级'
          }, {
            value: '5',
            label: '五年级'
          }, {
            value: '6',
            label: '六年级'
          }]
        }, {
          value: 'cz',
          label: '初中',
          children: [{
            value: '7',
            label: '初一'
          }, {
            value: '8',
            label: '初二'
          }, {
            value: '9',
            label: '初三'
          }]
        }, {
          value: 'gz',
          label: '高中',
          children: [{
            value: '10',
            label: '高一'
          }, {
            value: '11',
            label: '高二'
          }, {
            value: '12',
            label: '高三'
          }]
        }]
      }, {
        value: '3',
        label: '家长',
        children: [{
          value: 'xx',
          label: '小学',
          children: [{
            value: '1',
            label: '一年级'
          }, {
            value: '2',
            label: '二年级'
          }, {
            value: '3',
            label: '三年级'
          }, {
            value: '4',
            label: '四年级'
          }, {
            value: '5',
            label: '五年级'
          }, {
            value: '6',
            label: '六年级'
          }]
        }, {
          value: 'cz',
          label: '初中',
          children: [{
            value: '7',
            label: '初一'
          }, {
            value: '8',
            label: '初二'
          }, {
            value: '9',
            label: '初三'
          }]
        }, {
          value: 'gz',
          label: '高中',
          children: [{
            value: '10',
            label: '高一'
          }, {
            value: '11',
            label: '高二'
          }, {
            value: '12',
            label: '高三'
          }]
        }]
      }, {
        value: '4',
        label: '其他'
      }]
    }
  },
  mounted() {
    this.getList()
    this.getTitleTypeList()
    this.getDefaultMgrInfo()
  },
  methods: {
    useInfoFormatter(row) {
      let result = ''
      if (row.userLoginHistoryDto !== null) {
        const userLoginHistoryDto = row.userLoginHistoryDto
        result += 'app版本：' + userLoginHistoryDto.appVersion + '<br>'
        result += '手机系统/型号：' + userLoginHistoryDto.phoneType + '\n'
        result += '连接过设备：' + userLoginHistoryDto.usedDevices
      }
      return result
    },
    /** 获取列表 */
    async getTitleTypeList() {
      fetchTitleTypeList().then(res => {
        this.titleTypeList = res.data
      })
    },
    handleJyWhiteListQuery() {
      queryJqApiWhiteList().then(res => {
        console.log(res)
        this.whiteListData = res.data
      })
      this.whiteListDialog = true
    },
    showJyApiCount(userId) {
      const parms = {
        userId
      }
      queryJqApiCountByUserId(parms).then(res => {
        console.log('queryJqApiCountByUserId', res)
        this.jyRequestCount = res.data
        this.jyRequestCount["userId"] = userId
      })
      this.jyRequestCountDialog = true
    },
    submitAddWileList() {
      const params = {
        'userId': this.jyRequestCount.userId,
        'time': this.time
      }
      addJqApiUserId(params).then(res => {
        this.$message.success('设置成功')
        this.jyRequestCountDialog = false
      })
    },
    removeWhiteList(userId) {
      const params = {
        'userId': userId
      }
      removeJqApiUserId(params).then(res => {
        this.$message.success('删除成功')
        this.whiteListDialog = false
      })
    },
    formatExpressTime(time) {
      const res = Number.parseInt(time)
      if (res < 86400) {
        return time + "秒"
      } else if (res < ********) {
        return Math.round(res / 86400) + "天"
      } else if (res < ********0) {
        return Math.round(res / ********) + "年"
      }
    },
    submitTodayCount() {
      const parms = {
        'userId': this.jyRequestCount.userId,
        'keyword': this.jyRequestCount.keyword !== null ? this.jyRequestCount.keyword : 0,
        'cond': this.jyRequestCount.cond !== null ? this.jyRequestCount.cond : 0,
        'viewques': this.jyRequestCount.viewques !== null ? this.jyRequestCount.viewques : 0,
        'nominateques': this.jyRequestCount.nominateques !== null ? this.jyRequestCount.nominateques : 0
      }
      settingJqApiCountByUserId(parms).then(res => {
        this.$message.success('设置成功')
        this.jyRequestCountDialog = false
      })
    },
    async exportHandleQuery() {
      const loading = this.$loading({
        lock: true,
        text: 'Loading',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      if (this.codeId) {
        this.params['codeId'] = this.codeId
      } else {
        delete this.params['codeId']
      }
      if (this.nickName) {
        this.params['nickName'] = this.nickName
      } else {
        delete this.params['nickName']
      }
      if (this.userTitleType || this.userTitleType === 0) {
        this.params['userTitleType'] = this.userTitleType
      } else {
        delete this.params['userTitleType']
      }
      if (this.userRole) {
        this.params['userRole'] = this.userRole[0]
      } else {
        delete this.params['userRole']
      }
      const res = await exportUserList(this.params)
      if (res.head.ret === 0) {
        // const a = document.createElement('a')
        // a.href = res.data // 这里的请求方式为get，如果需要认证，接口上需要带上token
        // a.click()
        this.downloadUrl = res.data
      }
      loading.close()
    },
    async getList() {
      if (this.codeId) {
        this.params['codeId'] = this.codeId
      } else {
        delete this.params['codeId']
      }
      if (this.nickName) {
        this.params['nickName'] = this.nickName
      } else {
        delete this.params['nickName']
      }
      if (this.userTitleType || this.userTitleType === 0) {
        this.params['userTitleType'] = this.userTitleType
      } else {
        delete this.params['userTitleType']
      }
      if (this.userRole) {
        console.log("------- this.urerrole", this.userRole)
        this.params['userRole'] = this.userRole
        // 数组第一个是 role 身份 0：其他；1：学生；2：老师；3：家长
        // 数组第三个是 gradeLevel 所在年级1--12对应小学一年级到高中三年级
        if (Array.isArray(this.userRole)) {
          this.params['role'] = this.userRole[0];
          if (!!this.userRole[2]) {
            this.params['gradeLevel'] = this.userRole[2]
          }
          delete this.params['userRole']
        }
      } else {
        delete this.params['userRole']
      }
      console.log('params', this.params)
      const res = await fetchListByPage(this.params)
      if (res.head.ret === 0) {
        this.list = res.data.result
        this.total = res.data.totalCount
      }
    },
    /** 切换每页条数 */
    handleSizeChange(val) {
      this.params.pageSize = val
      this.getList()
    },
    /** 切换当前页 */
    handleCurrentChange(val) {
      this.params.pageNo = val
      this.getList()
    },
    /** 跳转类型 转换 */
    jumpTypeFormat(row, column) {
      if (row.msgSubType === 100) {
        return '无跳转'
      } else if (row.msgSubType === 101) {
        return 'H5'
      } else if (row.msgSubType === 102) {
        return '消息中心'
      } else if (row.msgSubType === 103) {
        return '纸条消息'
      } else if (row.msgSubType === 104) {
        return '共享打印'
      }
      return '未知类型'
    },
    /** 选择时间段 */
    changeTimeRange(val) {
      this.params.startDate = val[0]
      this.params.endDate = val[1]
    },
    /** 查询 */
    handleQuery() {
      this.params = {
        startDate: '',
        endDate: '',
        pageNo: 1,
        pageSize: 10,
        sort: 'desc',
        sortType: 'time'
      }
      this.getList()
    },
    /** 查看动态 */
    handleActivity(index, row) {
      this.$router.push({
        path: '/user/userdraft',
        query: {
          userId: row.codeId
        }
      })
    },
    updateTitleType(parms) {
      const paramm = {
        id: parms.userId,
        userTitleType: parms.titleType
      }
      saveUser(paramm).then(res => {
        if (res.head.ret === 0) {
          this.$message.success('设置成功')
          this.getList()
        }
        this.showDialog = false
      })
    },
    enableUser(userId, enable) {
      const parms = {
        userId: userId,
        forbidden: enable ? '1' : '0'
      }
      this.handleSaveUserInfo(parms)
    },
    handleSaveUserInfo(parms) {
      saveUserInfo(parms).then(res => {
        if (res.head.ret === 0) {
          this.$message.success('设置成功')
          this.getList()
        }
        this.showDialog = false
      })
    },
    handleSetting(index, row) {
      this.showDialog = true
      this.selectUserId = row.userId
      this.selectTitleType = row.userTitleObj ? row.userTitleObj.id : 0
    },
    binding(userId) {
      const parms = {
        id: userId
      }
      bindingDefaultManagerUserId(parms).then(res => {
        this.$message.success('设置成功')
      })
    },
    getDefaultMgrInfo() {
      getDefaultManagerUserInfo().then(res => {
        this.defaultAccount = res.data.defaultAccount
        this.defaultPassword = res.data.defaultPassword
      })
    },
    settingDefaultMgrInfo() {
      const parms = {
        defaultAccount: this.defaultAccount,
        defaultPassword: this.defaultPassword
      }
      settingDefaultManagerUserInfo(parms).then(res => {
        this.$message.success('设置成功')
      })
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
