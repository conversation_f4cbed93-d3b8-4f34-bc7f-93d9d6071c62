<template>
  <div class="container-side">
    <div class="container-side-top">
      <img src="https://m.yoyin.net/h5/img/mobile/<EMAIL>">
<!--      柚印-->
    </div>
    <div class="container-side-nav">
      <div class="container-side-nav-li" @click="changeNav(4, 'HomePage', '首页')">
<!--        <img src="https://m.yoyin.net/h5/img/mobile/home_home_on.gif" v-show="sideBarNavIndex===4">-->
<!--        <img src="https://m.yoyin.net/h5/img/mobile/home_home_off.png" v-show="sideBarNavIndex!==4">-->
        <transition name="navshow">
          <img src="https://m.yoyin.net/h5/img/mobile/home_home_on.png" v-show="sideBarNavIndex===4">
        </transition>
        <transition name="navdis">
          <img src="https://m.yoyin.net/h5/img/mobile/home_home_off.png" v-show="sideBarNavIndex!==4">
        </transition>
      </div>
      <div class="container-side-nav-li" @click="changeNav(3, 'AboutUs', '关于我们')">
        <transition name="navshow">
          <img src="https://m.yoyin.net/h5/img/mobile/home_about_on.png" v-show="sideBarNavIndex===3">
        </transition>
        <transition name="navdis">
          <img src="https://m.yoyin.net/h5/img/mobile/home_about_off.png" v-show="sideBarNavIndex!==3">
        </transition>
      </div>
      <div class="container-side-nav-li" @click="changeNav(2, 'ServiceInfo', '服务')">
        <transition name="navshow" mode="out-in">
          <img src="https://m.yoyin.net/h5/img/mobile/home_service_on.png" v-show="sideBarNavIndex===2">
        </transition>
        <transition name="navdis">
          <img src="https://m.yoyin.net/h5/img/mobile/home_service_off.png" v-show="sideBarNavIndex!==2">
        </transition>
      </div>
      <div class="container-side-nav-li" @click="changeNav(1, 'DriveDownload', '电脑驱动下载')">
        <transition name="navshow">
          <img src="https://m.yoyin.net/h5/img/mobile/home_drive_on.png" v-show="sideBarNavIndex===1">
        </transition>
        <transition name="navdis">
          <img src="https://m.yoyin.net/h5/img/mobile/home_drive_off.png" v-show="sideBarNavIndex!==1">
        </transition>
      </div>
    </div>
  </div>
</template>

<script>

export default {
  name: 'SideBar',
  data () {
    return {
      sideBarNavIndex: 4
    }
  },
  methods: {
    changeNav (index, componentName, titleName) {
      this.sideBarNavIndex = index
      const parms = {
        index, componentName, titleName
      }
      this.$emit('changeNav', parms)
    }
  }
}
</script>

<style scoped>
  .container-side {
    position: fixed;
    background-color: #222222;
    width: 5.7rem;
    height: 100%;
    left: 0;
  }
  .container-side-top {
    padding: 1.67rem 1.28rem 2.77rem 1.28rem;
    color: #ffffff;
    font-size: 1.2rem;
    text-align: center;
  }
  .container-side-top img{
    width: 3.1rem;
    height: 6.1rem;
  }

  .container-side-nav {
    position: fixed;
    bottom: 0rem;
    left: 1.433rem;
  }
  .container-side-nav-li {
    min-height: 8.2rem;
  }
  .container-side-nav-li img{
    width: 2.6rem;
    height: 2.6rem;
    position: absolute;
    vertical-align: middle;
  }

  .navshow-enter{
    opacity:0;
  }
  .navshow-enter-active{
    animation: pulse 1s linear infinite alternate;
  }
  .navshow-leave-active{
    transition:transform 0s;
  }
  .navshow-leave-to{
    /*transform: translateX(0);*/
  }

  .navdis-enter{
    opacity:0;
  }
  .navdis-enter-to{
    transform: none;
  }
  .navdis-enter-active{
    /*transition:opacity 2s;*/
  }
  .navdis-leave-active{
    /*transition:transform 0s;*/
  }
  .navdis-leave-to{
    transform: none;
  }

  @keyframes pulse {
    0%, 50%, 80%, 100% {
      -webkit-transition-timing-function: cubic-bezier(0.3, 0.6, 0.6, 1.000);
      transition-timing-function: cubic-bezier(0.2135, 0.6, 0.6, 1.000);
    }

    0% {
      opacity: 1;
      -webkit-transform: scale3d(0.1, 0.1, 0.1);
      transform: scale3d(0.1, 0.1, 0.1);
    }

    50% {
      -webkit-transform: scale3d(1, 1, 1);
      transform: scale3d(1, 1, 1);
    }

    /*40% {*/
    /*  -webkit-transform: scale3d(1.23, 1.23, 1.23);*/
    /*  transform: scale3d(1.23, 1.23, 1.23);*/
    /*}*/

    80% {
      -webkit-transform: scale3d(1.5, 1.5, 1.5);
      transform: scale3d(1.5, 1.5, 1.5);
    }

    /*80% {*/
    /*  -webkit-transform: scale3d(1.2, 1.2, 1.2);*/
    /*  transform: scale3d(1.2, 1.2, 1.2);*/
    /*}*/

    100% {
      opacity: 1;
      -webkit-transform: scale3d(1, 1, 1);
      transform: scale3d(1, 1, 1);
    }
  }
</style>
