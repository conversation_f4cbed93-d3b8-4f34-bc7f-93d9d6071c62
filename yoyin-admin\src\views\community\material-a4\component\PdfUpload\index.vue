<template>
  <div class="singleupload">
    <el-upload
      class="avatar-uploader"
      accept=".pdf"
      action="https://jsonplaceholder.typicode.com/posts/"
      :show-file-list="true"
      :auto-upload="false"
      :on-change="onChnagePic"
      :file-list="fileList"
      :limit="1"
    >
      <el-button type="primary">选取文件</el-button>
    </el-upload>
  </div>
</template>
<script>
export default {
  name: 'SingleUpload',
  props: {
    keyWord: {
      type: String,
      default: ''
    },
    initUrl: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      fileList: [],
      dialogVisible: false,
      currentFile: undefined

    }
  },
  computed: {
    getUrl() {
      console.log('this.initUrl = ', this.initUrl)
      const url = this.imageUrl || this.initUrl
      const urls = []
      urls.push(url)
      return urls
    }
  },
  watch: {
    initUrl(val) {
      this.fileList = []
      this.fileList.push({ name: val, url: val })
    }
  },
  mounted() {
    if (this.initUrl) {
      this.fileList = []
      this.fileList.push({ name: this.initUrl, url: this.initUrl })
    }
  },
  methods: {
    handleClick() {
      this.dialogVisible = true
    },
    onChnagePic(file, fileList) {
      this.currentFile = file.raw
      this.imageUrl = URL.createObjectURL(file.raw)
      // 通知父 文件对象
      this.$emit('updateFile', this.keyWord, this.currentFile)
    }

  }
}
</script>
