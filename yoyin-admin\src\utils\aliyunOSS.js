/*
 * @Author: your name
 * @Date: 2019-10-22 17:25:39
 * @LastEditTime: 2019-10-22 18:53:45
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \star-printer-admin\src\utils\aliyunOSS.js
 */
import { FILE_URL } from '@/consts/index'
// import axios from '@/utils/axios'
import { storage, uuid } from '@/utils/common'
import OSS from 'ali-oss'
import moment from 'moment'
import { getOssToken } from '@/api/oss/index'

const OSS_OPTIONS_KEY = 'OSS_OPTIONS_KEY'

export default class AliyunOSS extends Object {
  objectName = null;
  client = null;
  queue = []; // Stores tasks: { file, suffix, callback }
  isLoading = false; // Flag for initClient process
  isProcessingQueue = false; // Flag for emptyQueue process

  /**
   * Adds a file upload task to the queue and initiates processing if possible.
   * @param {File} file - The file object to upload.
   * @param {Function} callback - Callback function (result, error).
   */
  uploadFile(file, callback) {
    // Basic validation
    if (!file || !file.name) {
      console.error("Invalid file object provided.");
      if (callback) callback(null, new Error("Invalid file object provided."));
      return;
    }
    const matches = file.name.match(/\.(.+)/i);
    // Provide a default or handle cases with no extension more robustly if needed
    const suffix = (matches && `.${matches[1].toLowerCase()}`) || '';

    const task = { file, suffix, callback }; // Create task object
    this.queue.push(task);
    console.log(`Task added for ${file.name}. Queue size: ${this.queue.length}`);

    // Trigger processing or initialization
    if (this.client) {
      // If client is ready, start processing the queue if not already doing so
      this.emptyQueue();
    } else if (!this.isLoading) {
      // If client is not ready and not being initialized, start initialization
      console.log('Client not ready, initiating initialization...');
      this.initClient(); // initClient will call emptyQueue on success
    } else {
      // If client is initializing, queue will be processed upon completion
      console.log('Client initialization in progress...');
    }
  }

  // uploadFiles remains largely the same conceptually, relying on the corrected uploadFile
  uploadFiles(fileList, callback) {
    if (fileList === null || fileList.length === 0) {
      if (callback) callback([], null); // Return empty array for success
      return;
    }
    const uploadPromises = fileList.map(file => {
      return new Promise((resolve, reject) => {
        // Pass resolve/reject as the callback handlers
        this.uploadFile(file, (result, error) => {
          if (error) {
            console.error(`Error uploading ${file.name} in batch:`, error);
            // Reject with an object containing error and filename for better context
            reject({ error, fileName: file.name });
          } else {
            console.log('------》》》》upload result', result)
            resolve(result);
          }
        });
      });
    });

    Promise.allSettled(uploadPromises) // Use allSettled to get results even if some fail
      .then(results => {
        console.log('------》》》》 allSettled results', results)
        if (callback) {
          // Separate successful uploads and errors
          const successfulUploads = results
            .filter(r => r.status === 'fulfilled')
            .map(r => r.value);
          const errors = results
            .filter(r => r.status === 'rejected')
            .map(r => r.reason); // reason contains { error, fileName }

          if (errors.length > 0) {
            console.error("Some files failed to upload in batch:", errors);
            // Decide how to report partial success/failure.
            // Option 1: Report overall failure if any file fails
            // callback(null, errors);
            // Option 2: Report successful ones and pass errors separately (more info)
            callback(successfulUploads, errors);
          } else {
            callback(successfulUploads, null); // All succeeded
          }
        }
      });
    // Note: Original Promise.all would reject immediately on first error.
    // allSettled is often better for batch uploads.
  }

  /**
   * Initializes the OSS client, fetching options if necessary.
   */
  async initClient() {
    if (this.isLoading) {
      console.log('Initialization already in progress.');
      return;
    }
    this.isLoading = true;
    console.log('Initializing OSS Client...');
    this.client = null; // Ensure client is null during init

    try {
      let options = storage.get(OSS_OPTIONS_KEY);
      let optionsValid = false;
      if (options) {
        try {
          options = JSON.parse(options);
          // Basic check for necessary properties and potentially token expiry if stored
          if (options.accessKeyId && options.bucket && options.endpoint /* && !isExpired(options.stsTokenExpiry) */) {
            optionsValid = true;
            console.log('Using stored OSS options.');
          } else {
            console.warn('Stored OSS options are invalid or incomplete.');
          }
        } catch (e) {
          console.error('Failed to parse stored OSS options:', e);
          storage.remove(OSS_OPTIONS_KEY); // Clear potentially corrupted data
        }
      }


      if (!optionsValid) {
        console.log('Fetching new OSS options from server...');
        options = await this.getOSSOptionFromServer();
        if (options && Object.keys(options).length > 0 && options.accessKeyId) {
          // Store the new options (consider storing expiry time too)
          storage.set(OSS_OPTIONS_KEY, JSON.stringify(options));
          console.log('Fetched and stored new OSS options.');
        } else {
          // Throw error if fetching failed to get valid options
          throw new Error("Failed to fetch valid OSS options from server.");
        }
      }

      // Final check before creating client
      if (options && options.accessKeyId && options.bucket && options.endpoint) {
        this.objectName = options.objectName; // Set objectName prefix
        this.client = new OSS(options);
        console.log('OSS Client initialized successfully.');
        // Process queue now that client is ready
        this.emptyQueue();
      } else {
        throw new Error("OSS options are invalid after init attempt.");
      }

    } catch (error) {
      console.error("Failed to initialize OSS client:", error);
      // Handle error: maybe notify application, clear queue with error?
      // For now, just log it. Subsequent calls might retry init.
      // Clear queue with error?
      // this.queue.forEach(task => task.callback && task.callback(null, new Error("OSS Initialization failed")));
      // this.queue = [];
    } finally {
      this.isLoading = false;
      console.log('Initialization process finished.');
    }
  }

  /**
   * Clears stored OSS options.
   */
  clearStorage() {
    storage.remove(OSS_OPTIONS_KEY);
    this.client = null; // Invalidate client as options are gone
    console.log('Cleared stored OSS options and invalidated client.');
  }

  /**
   * Fetches OSS credentials from the backend.
   * @returns {Promise<object|null>} OSS options or null on failure.
   */
  async getOSSOptionFromServer() {
    try {
      console.log('======= getOSSOptionFromServer start=========')
      // Ensure the API endpoint is correct
      const { head, data } = await getOssToken('/api/log/event/v1/storage/getosstoken');
      if (head.ret === 0 && data) {
        console.log('Received OSS Token Data:', data);
        // Validate received data structure
        if (data.paramMap && data.paramMap.OSS && data.accessKeyId && data.accessKeySecret && data.securityToken) {
          const options = {
            accessKeyId: data.accessKeyId,
            accessKeySecret: data.accessKeySecret,
            bucket: data.paramMap.OSS.bucket,
            endpoint: data.paramMap.OSS.endPoint,
            stsToken: data.securityToken,
            // region: data.paramMap.OSS.region, // Add region if needed by your config
            objectName: data.paramMap.OSS.objectName || 'default-prefix/', // Provide a default prefix
            // Consider adding expiry time if available in 'data' to check later
            // stsTokenExpiry: data.expiration // Example field name
          };
          return options;
        } else {
          console.error("Received incomplete OSS token data structure:", data);
          return null;
        }
      } else {
        console.error("API request for OSS token failed:", head);
        return null;
      }
    } catch (error) {
      console.error("Error fetching OSS token from server:", error);
      return null;
    }
  }

  /**
   * Generates the object name (path) for the uploaded file in OSS.
   * @param {File} file - The file object.
   * @param {string} suffix - The file extension.
   * @returns {string} The full object name path.
   */
  generateObjectName(file, suffix) {
    const currentDate = moment().format('YYYYMMDD');
    const baseObjectName = this.objectName || 'unknown-prefix/'; // Use default if not set
    let objectName = `${baseObjectName}${currentDate}/${uuid()}${suffix}`;

    // Handle special cases like apk/bin if needed
    if (suffix === '.apk' || suffix === '.bin') {
      // Using file.name directly might be insecure or cause collisions. Consider prefixing.
      objectName = `common/star/apk/${uuid()}-${file.name}`; // Example: add uuid
      console.log(`Using special object name for ${suffix}: ${objectName}`);
    }
    return objectName;
  }


  /**
   * Processes the upload queue task by task.
   */
  async emptyQueue() {
    if (this.isProcessingQueue) {
      console.log('Queue processing already in progress.');
      return;
    }
    if (this.queue.length === 0) {
      // console.log('Queue is empty.');
      return;
    }
    if (!this.client) {
      console.warn('Client not ready, cannot process queue. Attempting re-init.');
      // Don't clear queue, try re-init. If successful, emptyQueue will be called again.
      if (!this.isLoading) {
        this.initClient();
      }
      return;
    }

    this.isProcessingQueue = true;
    console.log(`Starting queue processing. Queue size: ${this.queue.length}`);

    // Process one task at a time from the front of the queue
    while (this.queue.length > 0) {
      const task = this.queue.shift(); // Get and remove task
      const { file, suffix, callback } = task;

      // Double check client validity before each upload attempt in the loop
      if (!this.client) {
        console.error("Client became invalid during queue processing. Stopping.");
        // Re-add task to the front? Or fail it? Failing seems safer.
        // this.queue.unshift(task);
        if (callback) callback(null, new Error("OSS client became invalid during upload"));
        break; // Stop processing the rest of the queue
      }


      const objectName = this.generateObjectName(file, suffix);

      try {
        console.log(`Uploading ${file.name} as ${objectName}...`);
        // Use await with the promise returned by client.put
        const result = await this.client.put(objectName, file);
        console.log(`Upload successful for ${file.name}: ${result.name}`);

        // Call the task-specific callback on success
        if (callback) {
          // Format the result URL if needed
          result.name = `${FILE_URL}/${result.name}`;
          // Add back any other relevant info if the callback expects it
          result.key = file.key;
          callback(result, null);
        }
      } catch (e) {
        console.error(`Upload failed for ${file.name} (${objectName}):`, e);

        // Check for token expiration error specifically
        if (e.code && (e.code === 'InvalidSecurityToken' || e.code === 'InvalidAccessKeyId')) {
          console.warn('OSS Token expired or invalid during upload. Clearing client and stored options.');
          this.clearStorage(); // Clears options and invalidates client

          // Report the error back via callback
          if (callback) {
            callback(null, e); // Pass the specific error
          }
          // Stop processing the rest of the queue as the client is now invalid
          // Any subsequent uploadFile calls will trigger initClient again.
          break;
        } else {
          // Handle other types of upload errors
          if (callback) {
            callback(null, e); // Report the error
          }
          // Continue processing the rest of the queue for other errors?
          // Or stop on first error? Stopping might be safer. Let's stop.
          // break; // Uncomment to stop queue processing on any error
        }
      }
    } // End while loop

    this.isProcessingQueue = false;
    console.log(`Finished queue processing. Remaining tasks: ${this.queue.length}`);

    // If queue still has items, it means processing stopped (e.g., token error)
    // You could add logic here if needed, e.g., notify app state.
  }
}
