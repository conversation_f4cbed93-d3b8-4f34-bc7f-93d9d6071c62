<!--
试题内容渲染 根据 不同的类型 显示不同的内容
eg:
 -->
<template>
  <div class="ques-origin">
    <!-- 常规题干展示区 -->
    <div v-if="level === 1 || quesStructPm != '23'">
      <div v-if="rule.s && checkStem">
        <div class="ques-topic clearfix">
          <span class="ques-type" v-html="getStem(ques)"></span>
        </div>
      </div>
      <div
        class="ques-option"
        v-if="rule.o && checkStem && getOptions(ques)"
        v-autos
      >
        <span v-for="(suboption, index) in getOptions(ques)" :key="index">
          <span
            class="ques-content"
            v-html="combinedOption(suboption, index)"
          ></span>
        </span>
        <span v-if="fillchoice" class="ques-content" v-html="longfillHtml">
        </span>
      </div>
    </div>
    <div v-else>
      <div class="nostem-ques-topic">
        <div class="nostem-no">
          <span class="ques-type" v-html="getStem()"></span>
        </div>
        <div
          class="nostem-ques-option"
          v-if="rule.o && getOptions(ques)"
          v-autos
        >
          <span v-for="(suboption, index) in getOptions(ques)" :key="index">
            <span
              class="ques-content"
              v-html="combinedOption(suboption, index)"
            ></span>
          </span>
          <span v-if="fillchoice" class="ques-content"> </span>
        </div>
      </div>
    </div>

    <!-- 有子集题干展示区 -->
    <div v-if="rule.c && ques.children && ques.children.length > 0">
      <!-- <slot name="child" :list="ques.children"></slot> -->
      <ti-question-cell
        v-for="(child, index) in ques.children"
        :key="index"
        :childIndex="index"
        :showExplain="showExplain"
        :showAttr="showAttr"
        :level="level + 1"
        :quesStructPm="quesStructPm"
        :ques="child"
      ></ti-question-cell>
    </div>
  </div>
</template>

<script>
  import CTS from '../common/constant'
  import { getQuestionSuffixNO } from '../common/util'
  export default {
    name: 'ti-question-cell',
    props: {
      ques: {
        type: Object,
      },
      level: {
        type: Number,
        default: 1,
      },
      childIndex: {
        type: Number,
        default: 0,
      },
      quesStructPm: {
        type: String,
        default: '-1',
      },
      questionNum: {
        type: Number,
        default: 1,
      },
      showQuestionNum: {
        // 是否显示题号
        type: Boolean,
        default: false,
      },
      showExplain: {
        type: Boolean,
        default: false,
      },
      showAttr: {
        type: Boolean,
        default: false,
      },
      questionWidth: {
        type: Number,
        default: 832,
      },
    },

    watch: {
      ques() {
        this.renderRule()
      },
    },
    data() {
      return {
        longfillHtml: '<longfill>&nbsp;</longfill>',
        ourQuesType: '1',
        questionShowPart: CTS.cfgDict.questionShowPart,
        rule: {
          h: false, // 听力
          s: true, // 题干
          o: true, // 选项
          c: false, // 子集
          a: true, // 答案
          ay: true, // 解析
          at: true, // 属性
        },
        ant: 1,
        fillchoice: false,
        renderAS: false,
      }
    },
    computed: {
      // 校验是否有题干
      checkStem() {
        // 是否应该显示 小题号
        if (this.level === 1) return true
        if (this.quesStructPm) {
          if ('24,25'.indexOf(this.quesStructPm) != -1) {
            return false
          } else {
            return true
          }
        } else {
          return true
        }
      },
      /* loginStatus() {
      return isLogin
    } */
    },
    created() {
      // console.log(this.showQuestionNum)
      // console.log(this.ques, this.quesTypePm)
    },
    mounted() {
      if (this.ques) {
        this.renderRule()
      }
    },
    methods: {
      combinedOption(suboption, index) {
        return this.$cp_filter.optionTag(index) + '.&nbsp;' + suboption
      },
      // 渲染 显示规则 rule
      renderRule() {
        if (this.ques.quesStruct) {
          let qstruct = parseInt(this.ques.quesStruct.code)
          let item = this.questionShowPart[qstruct]
          this.fillchoice = qstruct == 27
          if (item) {
            this.rule.h = this.getRuleAttribute('h', item)
            this.rule.s = this.getRuleAttribute('s', item)
            this.rule.o = this.getRuleAttribute('o', item)
            this.rule.c = this.getRuleAttribute('c', item)
            this.rule.a = this.getRuleAttribute('a', item)
            this.rule.ay = this.getRuleAttribute('ay', item)
            this.rule.at = this.getRuleAttribute('at', item)
            if (item.ant) {
              this.ant = item.ant
              // console.log(this.ant, 'ant')
            }

            if (
              qstruct == 19 &&
              this.ques.quesType &&
              this.ques.quesType.code == 15
            ) {
              this.rule.c = 0
            }
          }
        }
        // { name: '单选题', h: 0, s: 1, o: 1, c: 0, a: 1, ay: 1, at: 1 }
      },
      getRuleAttribute(ckey, c) {
        let state = c[ckey]
        switch (state) {
          case 0:
            return false
          case 1:
            return true
          case 2:
            return this.level === 1
          case 3:
            return this.level !== 1
        }
        return false
      },
      // 获取听力题干
      getHearing(ques) {
        let original_text = ''
        if (ques && ques.context) {
          original_text = ques.context.original_text || ''
        } else {
          original_text = ''
        }
        return original_text
      },

      // 获取题干
      getStem(ques) {
        let stem = ''
        if (ques && ques.context) {
          stem = ques.context.stem || ''
        } else {
          stem = ''
        }

        if (this.level === 1) {
          // if (this.showQuestionNum && this.quesStructPm != '19') {
          if (this.showQuestionNum) {
            return `${this.questionNum}. ${stem}`
          } else {
            return stem
          }
        } else {
          return (
            getQuestionSuffixNO(this.level, this.childIndex + 1) + ' ' + stem
          )
        }
      },
      getSuffixNo() {
        return getQuestionSuffixNO(this.level, this.childIndex + 1) + ' '
      },
      // onRenderJax () {
      //   // console.log(this.$refs['renderQuestionRef'])
      //   var el = this.$refs['renderQuestionRef']
      //   if (el) {
      //     this.$nextTick(() => {
      //       window.MathJax.Hub.Queue(['Typeset', window.MathJax.Hub, el])
      //     })
      //   }
      // },
      // 获取选项
      getOptions(ques) {
        if (ques && ques.context) {
          return ques.context.options
        } else {
          return null
        }
      },
      // 显示登录弹框
      showLogin() {
        this.Bus.$emit('showLogin')
      },
    },
  }
</script>

<style lang="scss">
  img {
    vertical-align: middle;
  }
  .ques-type {
    .center {
      text-align: center;
    }
  }
  .MathJax img {
    transform: scale(0.85);
  }
  /*新加的*/
  blank,
  .blank {
    width: auto !important;
  }
</style>

<style lang="scss" scoped>
  .ques-origin {
    .ques-option {
      span {
        line-height: 34px;
        color: #333;
        display: inline-block;
        font-size: 14px;
      }
      & > span {
        padding-right: 50px;
      }
    }
    .ques-topic {
      /*width: 800px;*/
      padding-bottom: 5px;
      color: #333;
      line-height: 24px;
      .sub-index {
        padding-left: 20px;
      }
    }
    .ques-answer {
      line-height: 24px;
      padding: 6px 0;
      color: #666;
      font-size: 14px;
    }
    .ques-analysis {
      line-height: 24px;
      padding: 6px 0;
      color: #666;
      font-size: 14px;
    }
    .ques-type {
      margin-bottom: 10px;
      color: #333;
      font-size: 14px;
      /*font-weight: 700;*/
    }
    .q-l.ques-type {
      margin-left: 20px;
    }
  }
  .ques-title {
    margin-left: -8px;
  }
  //flex 布局
  .ques-crow {
    display: flex;
    .q-l {
      width: 70px;
    }
    .q-c {
      flex: 1;
    }
    .b-l {
      width: 120px;
    }
  }
  .small-topic {
    margin-left: 25px;
  }
  .nostem-ques-topic {
    display: flex;
    line-height: 34px;
    .nostem-no {
      width: 35px;
    }
    .nostem-ques-option {
      flex: 1;
    }
  }
</style>
