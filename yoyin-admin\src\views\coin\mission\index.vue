<template>
  <div class="app-container">
    <div class="table-query-container">
      <div class="query-code-btn">
        <el-button v-permission="'btn-menuCoin-mission-edit'" size="medium" style="margin-left:20px;" @click="handleAdd">新增</el-button>
      </div>
    </div>
    <div class="table-container">
      <el-table
        ref="multipleTable"
        :data="list"
        tooltip-effect="dark"
        style="width: 100%"
        border
      >
        <el-table-column type="selection" width="55" />
        <el-table-column label="任务类别" prop="category" />
        <el-table-column label="任务名称" prop="name" />
        <el-table-column label="任务编号" prop="code" />
        <el-table-column label="详情描述" prop="info" />
        <el-table-column label="积分数量" prop="costCoin" />
        <el-table-column label="每天次数" prop="dayNum" />
        <el-table-column label="类型" :formatter="typeFormat" prop="type" />
        <el-table-column label="发放方式" :formatter="autoPayFormat" prop="autoPay" />
        <el-table-column label="排序编号" prop="sortNum" />
        <el-table-column label="操作">
          <template slot-scope="scope">
            <el-button size="mini" @click="handleEdit(scope.$index, scope.row)">编辑</el-button>
            <el-button
              v-permission="'btn-menuCoin-mission-edit'"
              size="mini"
              type="danger"
              @click="handleDeleteRow(scope.$index, scope.row)"
            >删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <!-- <div class="pagination-container" style="margin-top:20px;">
        <el-pagination
          :current-page="params.pageno"
          :page-sizes="[10, 20, 30, 40]"
          :page-size="params.pagesize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div> -->
    </div>
    <div class="dialog-container">
      <EditDialog :visiable="showDialog" :form-data="missionObj" @closeDialog="closeDialog" />
    </div>
  </div>
</template>
<script>
import { fetchMissionListByPage, deleteMissionRecord } from '@/api/coin/mission'
import EditDialog from './component/EditDialog'
export default {
  name: 'MissionList',
  components: {
    EditDialog
  },
  data() {
    return {
      // 查询参数
      params: {
        pageno: 1,
        pagesize: 10
      },
      /** 列表 */
      list: [],
      /** 总条数 */
      total: 0,
      showDialog: false,
      missionObj: {}
    }
  },
  mounted() {
    this.getList()
  },
  methods: {
    /** 获取列表 */
    async getList() {
      const res = await fetchMissionListByPage(this.params)
      if (res.head.ret === 0) {
        this.list = res.data
      }
    },
    /** 切换每页条数 */
    handleSizeChange(val) {
      this.params.pagesize = val
      this.getList()
    },
    /** 切换当前页 */
    handleCurrentChange(val) {
      this.params.pageno = val
      this.getList()
    },
    /** 删除单行 */
    async handleDeleteRow(index, row) {
      const params = {
        id: row.id
      }
      this.$confirm('此操作将永久删除该记录, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteMissionRecord(params).then(res => {
          if (res.head.ret === 0) {
            this.$message({
              type: 'success',
              message: '删除成功!'
            })
            this.getList()
          }
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
    },
    handleEdit(index, row) {
      this.missionObj = row
      this.showDialog = true
    },
    handleAdd() {
      this.missionObj = {}
      this.showDialog = true
    },
    closeDialog() {
      this.missionObj = {}
      this.showDialog = false
      this.getList()
    },
    /** 跳转类型 转换 */
    typeFormat(row, column) {
      const code = parseInt(row.type)
      if (code === 0) {
        return '每日'
      } else if (code === 1) {
        return '终生一次'
      } else if (code === 2) {
        return '特殊'
      }
      return '未知类型'
    },
    autoPayFormat(row, column) {
      const code = parseInt(row.autoPay)
      if (code === 0) {
        return '手动'
      } else if (code === 1) {
        return '自动'
      }
      return '未知类型'
    }

  }
}
</script>
<style lang="scss" scoped>

</style>
