<!--
 * @Description: In User Settings Edit
 * @Author: your name
 * @Date: 2019-10-16 11:28:20
 * @LastEditTime: 2019-10-22 17:03:31
 * @LastEditors: Please set LastEditors
 -->
<template>
  <div class="app-container">
    <div class="control-container">
      <el-button v-permission="'btn-menuOther-role-edit'" type="success" @click="addResourceTable">新增</el-button>
    </div>

    <el-table
      :data="treeData"
      style="width: 100%;margin-bottom: 20px;"
      row-key="id"
      border
      :default-expand-all="false"
      :tree-props="{children: 'resourceDtoList', hasChildren: 'hasChildren'}"
    >
      <el-table-column type="index" width="55" />
      <el-table-column
        prop="resName"
        label="资源名称"
      />
      <el-table-column
        prop="resCode"
        label="资源code"
      />
      <el-table-column prop="type" label="类型">
        <template slot-scope="scope">
          {{ scope.row.type===0 ? '菜单':'按钮' }}
        </template>
      </el-table-column>

      <el-table-column label="操作" fixed="right">
        <template slot-scope="scope">
          <el-button size="mini" type="success" @click="handleEdit(scope.row)">编辑</el-button>
          <el-button
            v-show="scope.row.type===0"
            v-permission="'btn-menuOther-role-edit'"
            size="mini"
            type="primary"
            @click="handleAdd(scope.row)"
          >新增下级</el-button>
          <el-button
            v-permission="'btn-menuOther-role-edit'"
            size="mini"
            type="danger"
            @click="handleDeleteRow(scope.$index, scope.row)"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 弹框 -->
    <div class="dialog-container">
      <el-dialog
        :title="form.id === '' ? '新增' : '编辑' "
        :visible.sync="showDialog"
        width="50%"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        center
      >
        <el-form
          :model="form"
          label-width="100px"
          class="demo-ruleForm"
        >
          <el-form-item label="上级" prop="pId">
            <el-input v-model="form.pId" />
          </el-form-item>
          <el-form-item label="资源名称" prop="resName">
            <el-input v-model="form.resName" />
          </el-form-item>
          <el-form-item label="资源编号" prop="resCode">
            <el-input v-model="form.resCode" />
          </el-form-item>
          <el-form-item label="资源类型" prop="type">
            <el-radio-group v-model="form.type">
              <el-radio-button label="0">菜单</el-radio-button>
              <el-radio-button label="1">按钮</el-radio-button>
            </el-radio-group>
          </el-form-item>
          <el-form-item v-show="form.type==='0'" label="路径" prop="path">
            <el-input v-model="form.path" />
          </el-form-item>
          <el-form-item v-show="form.type==='0'" label="层级" prop="level">
            <el-input-number v-model="form.level" />
          </el-form-item>
          <el-form-item label="排序" prop="sort">
            <el-input-number v-model="form.sort" />
          </el-form-item>
          <el-form-item>
            <el-button @click="showDialog = false">返回</el-button>
            <el-button v-permission="'btn-menuOther-role-edit'" type="primary" :loading="loading" @click="submitForm">保存</el-button>
          </el-form-item>
        </el-form>
      </el-dialog>
    </div>
  </div>
</template>
<script>
import { fetchResourceTree, saveRecord, deleteRecord } from '@/api/auth/resources'
export default {
  data() {
    return {
      loading: false,
      form: {
        pId: '0', // 上级id，第一次为 “”
        resName: '', // 资源名称
        resCode: '', // 资源编号
        path: '', // 访问路径
        level: 0, // 层等级
        sort: 0, // 排序
        type: '0' // 资源类型：0 菜单 1 按钮
      },
      // 弹框
      showDialog: false,
      treeData: []
    }
  },

  computed: {},
  mounted() {
    this.getResourcesTree()
  },
  methods: {
    /** 获取表数据 */
    async getResourcesTree() {
      const params = {
        pageno: this.currentPage,
        pagesize: this.currentSize
      }
      const res = await fetchResourceTree(params)
      if (res.head.ret === 0) {
        this.treeData = res.data
        // this.total = data.totalCount
        // this.tableList = data.result
      }
    },
    addResourceTable() {
      this.form = {
        pId: '0', // 上级id，第一次为 “”
        resName: '', // 资源名称
        resCode: '', // 资源编号
        path: '', // 访问路径
        level: 1, // 层等级
        sort: 0, // 排序
        type: '0' // 资源类型：0 菜单 1 按钮
      }
      this.showDialog = true
    },
    handleAdd(row) {
      this.form = {
        pId: row.id, // 上级id
        resName: '', // 资源名称
        resCode: '', // 资源编号
        path: '', // 访问路径
        level: row.level + 1, // 层等级
        sort: 0, // 排序
        type: '1' // 资源类型：0 菜单 1 按钮
      }
      this.showDialog = true
    },
    handleEdit(row) {
      this.form = Object.assign({}, row)
      this.form.type = this.form.type + ''
      this.showDialog = true
    },
    async submitForm() {
      const res = await saveRecord(this.form)
      if (res.head.ret === 0) {
        this.$message.success('保存成功')
      } else {
        this.$message.error(res.head.msg)
        this.loading = false
        return
      }
      this.loading = false
      this.showDialog = false
      this.initForm()
      this.getResourcesTree()
    },
    initForm() {
      this.form = {
        pId: '', // 上级id
        resName: '', // 资源名称
        resCode: '', // 资源编号
        path: '', // 访问路径
        level: 1, // 层等级
        sort: 0, // 排序
        type: '1' // 资源类型：0 菜单 1 按钮
      }
    },
    /** 删除单行 */
    async handleDeleteRow(index, row) {
      const params = {
        id: row.id
      }
      this.$confirm('此操作将永久删除该记录, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          deleteRecord(params).then(res => {
            if (res.head.ret === 0) {
              this.$message({
                type: 'success',
                message: '删除成功!'
              })
              this.getResourcesTree()
            }
          })
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    }

  }
}
</script>

<style lang="scss" scoped>
.app-container {
  margin: 20px;
  .control-container {
    margin-bottom: 20px;
  }
  .choose-container {
    margin-bottom: 20px;
    background-color: #e6f7ff;
    border: 1px solid #91d5ff;

    span {
      margin-left: 20px;
      line-height: 48px;
    }
    a {
      margin-left: 20px;
      color: #1890ff;
      text-decoration: none;
      background-color: transparent;
      outline: none;
      cursor: pointer;
      transition: color 0.3s;
    }
  }
}
</style>
