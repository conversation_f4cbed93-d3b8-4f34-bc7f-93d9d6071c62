<!--
 * @Description: In User Settings Edit
 * @Author: your name
 * @Date: 2019-10-16 11:28:20
 * @LastEditTime: 2019-10-16 11:38:48
 * @LastEditors: Please set LastEditors
 -->
<template>
  <div class="app-container">
    <div class="table-query-container">
      <el-row style="display:flex;line-height: 40px;padding:15px">
        <span class="demonstration" style="padding-right: 10px">账号/ID</span>
        <el-input v-model="otherid" type="text" maxlength="100" style="width:200px;padding-right: 10px" />
        <span class="demonstration" style="padding-right: 10px">SN号</span>
        <el-input v-model="deviceSn" type="text" maxlength="100" style="width:200px;padding-right: 10px" />

        <span class="demonstration" style="padding-right: 10px">MAC地址</span>
        <el-input v-model="macAddress" type="text" maxlength="100" style="width:200px;padding-right: 10px" />
        <span class="demonstration" style="padding-right: 10px">设备名称</span>
        <el-select v-model="deviceName" placeholder="请选择设备">
          <el-option lablel="全部" value="">全部</el-option>
          <el-option
            v-for="item in deviceNameTypeList"
            :key="item.key"
            :label="item.value"
            :value="item.key"
          />
        </el-select>
      </el-row>
      <el-row>
        <span class="demonstration" style="padding-right: 10px">最后连接时间 </span>
        <el-date-picker
          v-model="timeRange"
          :value-format="timeFormat"
          :format="timeFormat"
          :unlink-panels="true"
          type="datetimerange"
          :clearable="false"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          @change="changeTimeRange"
        />
        <el-button type="primary" @click="handleQuery">查询</el-button>
      </el-row>
    </div>
    <div class="table-container">
      <el-table
        ref="multipleTable"
        :data="list"
        tooltip-effect="dark"
        style="width: 100%"
        border
        max-height="700px"
      >
        <el-table-column prop="codeId" label="用户ID" header-align="center" align="center">
          <template slot-scope="scope">
            {{ scope.row.userInfoDto && scope.row.userInfoDto.userInfoDto ? scope.row.userInfoDto.userInfoDto.codeId : '' }}
          </template>
        </el-table-column>
        <el-table-column label="用户账号" header-align="center" align="center">
          <template slot-scope="scope">
            {{ scope.row.userInfoDto && scope.row.userInfoDto.accountInfoDto && scope.row.userInfoDto.accountInfoDto[0] ? scope.row.userInfoDto.accountInfoDto[0].account : '' }}
          </template>
        </el-table-column>
        <el-table-column prop="deviceName" label="设备名称" header-align="center" align="center" />
        <el-table-column prop="deviceSn" label="设备SN号" header-align="center" align="center" />
        <el-table-column prop="macAddress" label="MAC地址" header-align="center" align="center" />
        <el-table-column prop="deviceVersion" label="固件版本号" header-align="center" align="center" />
        <el-table-column prop="lastestTime" label="最后连接时间" header-align="center" align="center" />
        <el-table-column label="操作" header-align="center" align="center">
          <template slot-scope="scope">
            <!-- <a style="color: #1890FF;  text-decoration: none;  cursor: pointer; transition: color 0.3s;" @click="handleActivity(scope.$index, scope.row)">查看动态</a> -->
            <!-- <a v-permission="'btn-menuUsermanage-user-edit'" style="color: rgb(255, 24, 24);  text-decoration: none;  cursor: pointer; transition: color 0.3s;" @click="handleSetting(scope.$index, scope.row)">设置头衔</a> -->
            <a v-permission="'btn-menuUsermanage-user-edit'" style="color: #1890FF;  text-decoration: none;  cursor: pointer; transition: color 0.3s;" @click="handleActivity(scope.row.userId)">用户动态</a>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="pagination-container" style="margin-top:20px;">
      <el-pagination
        :current-page="params.pageno"
        :page-sizes="[10, 20, 30, 40]"
        :page-size="params.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>
<script>
import { fetchListByPage } from '@/api/user/device'
import { deviceNameTypeList } from '@/consts'
export default {
  data() {
    return {
      // 查询参数
      params: {
        startDate: '',
        endDate: '',
        pageno: 1,
        pagesize: 10,
        sort: 'desc',
        sortType: 'time'
      },
      /** 列表 */
      list: [],
      /** 总条数 */
      total: 0,
      downloadUrl: '',
      /** 时间段 */
      timeRange: '',
      otherid: '',
      deviceSn: '',
      deviceName: '',
      macAddress: '',
      /** 时间格式 */
      timeFormat: 'yyyy-MM-dd',
      showDialog: false,
      deviceNameTypeList: deviceNameTypeList
    }
  },
  mounted() {
    this.getList()
  },
  methods: {
    /** 获取列表 */
    async getList() {
      if (this.otherid) {
        this.params['otherid'] = this.otherid
      } else {
        delete this.params['otherid']
      }
      if (this.deviceSn) {
        this.params['deviceSn'] = this.deviceSn
      } else {
        delete this.params['deviceSn']
      }
      if (this.deviceName || this.deviceName === 0) {
        this.params['deviceName'] = this.deviceName
      } else {
        delete this.params['deviceName']
      }
      if (this.macAddress) {
        this.params['macAddress'] = this.macAddress
      } else {
        delete this.params['macAddress']
      }
      console.log('params', this.params)
      const res = await fetchListByPage(this.params)
      if (res.head.ret === 0) {
        this.list = res.data.result
        this.total = res.data.totalCount
      }
    },
    /** 切换每页条数 */
    handleSizeChange(val) {
      this.params.pagesize = val
      this.getList()
    },
    /** 切换当前页 */
    handleCurrentChange(val) {
      this.params.pageno = val
      this.getList()
    },
    /** 选择时间段 */
    changeTimeRange(val) {
      this.params.startdate = val[0]
      this.params.enddate = val[1]
    },
    /** 查询 */
    handleQuery() {
      this.getList()
    },
    /** 查看动态 */
    handleActivity(row) {
      this.$router.push({
        path: '/user/userdraft',
        query: {
          userId: row.codeId,
          subType: row.deviceName
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>

</style>
