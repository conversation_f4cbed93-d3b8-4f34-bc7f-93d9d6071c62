<!--
 * @Description: In User Settings Edit
 * @Author: your name
 * @Date: 2019-10-16 11:28:20
 * @LastEditTime: 2019-10-16 11:38:48
 * @LastEditors: Please set LastEditors
 -->
<template>
  <div class="app-container">
    <div class="table-query-container">
      <div class="query-date-hot">
        <span class="demonstration">日期范围： </span>
        <el-date-picker
          v-model="timeRange"
          :value-format="timeFormat"
          :format="timeFormat"
          :unlink-panels="true"
          type="datetimerange"
          :clearable="false"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          @change="changeTimeRange"
        />
      </div>
      <div class="query-code-btn">
        <span>用户codeId： </span>
        <el-input
          v-model="params.codeid"
          placeholder="请输入用户codeId"
          style="display: inline-block;width:200px"
        />
        <span style="margin-left: 20px;">动态Id： </span>
        <el-input
          v-model="params.feedid"
          placeholder="请输入动态Id"
          style="display: inline-block;width:200px"
        />
        <el-button type="primary" size="medium" style="margin-left: 20px;" @click="handleQuery">查询</el-button>
        <el-button size="medium" style="margin-left:20px;" @click="handleAdd">新增</el-button>
      </div>
    </div>

    <div class="dialog-container">
      <el-dialog
        title="新增动态"
        :visible.sync="showEdit"
        width="50%"
        center
        :destroy-on-close="true"
      >
        <EditDialog :feed-id="params.feedid" @closeDialog="handleClose" />
      </el-dialog>
    </div>

    <div class="table-select-container">
      <div class="select-count">当前选择<span style="color: #409EFF"> {{ multipleSelection.length }} </span>项
        <a @click="handleClearSelection">清空</a>
        <a v-if="multipleSelection.length > 0" @click="handelMultiDel">删除</a>
      </div>
    </div>

    <div class="table-container">
      <el-table
        ref="multipleTable"
        :data="list"
        tooltip-effect="dark"
        style="width: 100%"
        border
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column label="动态ID" width="250">
          <template slot-scope="scope">{{ scope.row.feedId }}</template>
        </el-table-column>
        <el-table-column label="头像" width="60">
          <template slot-scope="scope">
            <el-avatar shape="circle" :size="30" :src="scope.row.commentUserPic" />
          </template>
        </el-table-column>

        <el-table-column label="评论人" width="200">
          <template slot-scope="scope">{{ scope.row.commentUserName }}</template>
        </el-table-column>
        <el-table-column label="内容" width="200">
          <template slot-scope="scope" show-overflow-tooltip>{{ scope.row.content }}</template>
        </el-table-column>
        <el-table-column label="回复数" width="200">
          <template slot-scope="scope">{{ scope.row.replyNum }}</template>
        </el-table-column>
        <el-table-column prop="commentDate" label="时间" show-overflow-tooltip />
        <el-table-column label="操作">
          <template slot-scope="scope">
            <el-button type="text" @click="handleGo(scope.$index, scope.row)">查看动态</el-button>
            <el-button
              type="text"
              @click="handleDeleteRow(scope.$index, scope.row)"
            >删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <div class="pagination-container" style="margin-top:20px;">
      <el-pagination
        :current-page="params.pageno"
        :page-sizes="[10, 20, 30, 40]"
        :page-size="params.pagesize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

  </div>
</template>
<script>
import { fetchListByPage, deleteRecord } from '@/api/community/comment'
import EditDialog from './component/EditDialog/index'
export default {
  components: {
    EditDialog
  },
  data() {
    return {
      // 查询参数
      params: {
        startdate: '',
        enddate: '',
        pageno: 1,
        pagesize: 10,
        codeid: '',
        feedid: ''
      },
      /** 列表 */
      list: [],
      /** 总条数 */
      total: 0,
      /** 消息体 */
      banner: { },
      /** 勾选数据 */
      multipleSelection: [],
      /** 时间段 */
      timeRange: '',
      /** 时间格式 */
      timeFormat: 'yyyy-MM-dd',
      /** 显示编辑框 */
      showEdit: false,
      /** 标签列表 */
      labelList: [],
      /** 显示详情 */
      showDetail: false,
      /** 详情数据 */
      detailUrl: ''
    }
  },
  mounted() {
    const feedId = this.$route.query.feedId
    this.params.feedid = feedId
    this.getList()
  },
  methods: {
    /** 获取列表 */
    async getList() {
      const res = await fetchListByPage(this.params)
      if (res.head.ret === 0) {
        this.list = res.data.result
        this.total = res.data.totalCount
      }
    },
    /** 切换每页条数 */
    handleSizeChange(val) {
      this.params.pagesize = val
      this.getList()
    },
    /** 切换当前页 */
    handleCurrentChange(val) {
      this.params.pageno = val
      this.getList()
    },
    /** 选择时间段 */
    changeTimeRange(val) {
      this.params.startdate = val[0]
      this.params.enddate = val[1]
    },
    /** 查询 */
    handleQuery() {
      this.getList()
    },
    /** 新增 */
    handleAdd() {
      this.showEdit = true
    },
    /** 关闭弹框 */
    closeDialog(code) {
      this.showEdit = false
      this.banner = {}
      // 保存code
      if (code === '1') {
        this.getList()
      }
    },
    /** 保存后关闭刷新 */
    handleClose(val) {
      this.showEdit = false
      this.getList()
    },
    /** 删除单行 */
    async handleDeleteRow(index, row) {
      const params = {
        id: row.commentId
      }
      this.$confirm('此操作将永久删除该记录, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteRecord(params).then(res => {
          if (res.head.ret === 0) {
            this.$message({
              type: 'success',
              message: '删除成功!'
            })
            this.getList()
          }
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
    },
    /** 多选删除 */
    handelMultiDel() {
      this.$confirm('此操作将永久删除选中记录, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const rows = [...this.multipleSelection]
        rows.forEach(item => {
          const params = { id: item.commentId }
          deleteRecord(params).then(res => {
            if (res.head.ret === 0) {
              this.$message.success('删除成功')
              this.getList()
            }
          })
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
    },
    /** 勾选事件 */
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    /** 清空勾选 */
    handleClearSelection() {
      this.$refs.multipleTable.clearSelection()
    },
    /** 查看动态 */
    handleGo(index, row) {
      this.$router.push({
        path: '/community/activity',
        query: {
          feedId: row.feedId
        }
      })
    }

  }
}
</script>

<style lang="scss" scoped>
.app-container {
  .table-query-container {
    .query-date-hot{
      margin-bottom: 10px;
    }
    .query-code-btn{
      button{
        margin-right: 20px;
      }
    }
  }

}
</style>
