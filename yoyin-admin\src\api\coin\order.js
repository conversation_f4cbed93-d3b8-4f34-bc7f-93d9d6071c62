import request from '@/utils/request'

// 翻页查询
const LIST_URL = '/platform/gam/community/v1/coin/getorderpage'
const SAVE_URL = '/platform/gam/community/v1/coin/updateorderdeliverystatus'
const DELETE_URL = '/platform/gam/community/v1/coin/deleteorderbyid'
// 删除记录

/**
 * 查询列表数据
 * @param params 参数
 */
export const fetchOrdersListByPage = async params => {
  return request({
    url: LIST_URL,
    method: 'get',
    params: params
  })
}

/** 保存记录 */
export const saveOrdersRecord = async params => {
  return request({
    url: SAVE_URL,
    method: 'post',
    data: params
  })
}

/** 保存记录 */
export const deleteOrdersRecord = async params => {
  return request({
    url: DELETE_URL,
    method: 'get',
    params
  })
}
