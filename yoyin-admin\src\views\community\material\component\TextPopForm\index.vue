<template>
  <div class="todolist">
    <el-form class="demo-form-inline" :inline="true">
      <el-form-item label="预览图：">
        <SingleUpload key="listUrl" key-word="listUrl" :init-url="isEdit ? files.listUrl.pic: ''" @updateFile="updateFile" />
      </el-form-item>
      <el-form-item label="资源图：">
        <SingleUpload key="resUrl" key-word="resUrl" :init-url="isEdit ? files.resUrl.pic: '' " @updateFile="updateFile" />
      </el-form-item>
      <el-row v-if="mName==='tz_namecard'">
        <el-form-item label="顶部图：">
          <SingleUpload key-word="topUrl" :init-url="isEdit && files.topUrl ? files.topUrl.pic: '' " @updateFile="updateFile" />
        </el-form-item>
        <el-form-item label="底部图：">
          <SingleUpload key-word="downUrl" :init-url="isEdit && files.downUrl ? files.downUrl.pic: '' " @updateFile="updateFile" />

        </el-form-item>
        <el-form-item label="左侧图：">
          <SingleUpload key-word="leftUrl" :init-url="isEdit && files.leftUrl ? files.leftUrl.pic: '' " @updateFile="updateFile" />

        </el-form-item>
        <el-form-item label="右侧图：">
          <SingleUpload key-word="rightUrl" :init-url="isEdit && files.rightUrl ? files.rightUrl.pic: '' " @updateFile="updateFile" />
        </el-form-item>
        <br>
        <el-form-item label="type值：">
          <el-input v-model="formData.type" size="small" controls-position="right" />
        </el-form-item>
        <!-- <el-form-item label="版本号:">
          <el-input v-model="formData.version" type="text" size="small" />
        </el-form-item> -->
      </el-row>
      <el-form-item label="文本区域(px):">
        <el-input-number v-model="textArea.top" controls-position="right" size="small" :min="0" placeholder="顶部距离" />
        <el-input-number v-model="textArea.bottom" controls-position="right" size="small" :min="0" placeholder="底部距离" />
        <el-input-number v-model="textArea.left" controls-position="right" size="small" :min="0" placeholder="左侧距离" />
        <el-input-number v-model="textArea.right" controls-position="right" size="small" :min="0" placeholder="右侧距离" />
      </el-form-item>
      <el-form-item label="是否展示new" prop="shareFlag">
        <el-radio-group v-model="formData.isNew">
          <el-radio-button label="0">不展示</el-radio-button>
          <el-radio-button label="1">展示</el-radio-button>
        </el-radio-group>
        <span style="padding-left:20px;padding-right:15px">期限:</span><el-date-picker
          v-model="formData.newFlagBeforeDate"
          value-format="yyyy-MM-dd"
          type="date"
          placeholder="选择展示期限"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleBack">返回</el-button>
        <el-button v-permission="'btn-menu2cunkuan-material-edit'" type="primary" @click="handleSave">保存</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>
<script>
import { saveRecord } from '@/api/community/material'
import SingleUpload from '../SingleUpload/index'
import AliyunOSS from '@/utils/aliyunOSS'
import { getImageSize } from '@/utils/index'
const oss = new AliyunOSS()
const TIP_MESSAGES = {
  listUrl: '请选择预览图',
  resUrl: '请选择资源图'
}

export default {
  components: {
    SingleUpload
  },
  props: {
    mId: {
      type: String,
      default: ''
    },
    mName: {
      type: String,
      default: ''
    },

    formData: {
      type: Object,
      default: () => { return {} }
    }
  },
  data() {
    return {
      uploadFiles: { listUrl: {}, resUrl: {}, topUrl: {}, downUrl: {}, leftUrl: {}, rightUrl: {}},
      paperLabel: '',
      num: 1,
      textArea: {
        top: 0,
        bottom: 0,
        left: 0,
        right: 0
      }
    }
  },
  computed: {
    isEdit() {
      // console.log('isEdit', this.formData)
      if (this.formData && this.formData.id) {
        return true
      } else {
        return false
      }
    },
    /** 编辑的数据 */
    files() {
      if (this.isEdit) {
        return this.formData.resMap
      } else {
        return {}
      }
    },
    /** 编辑ID */
    addId() {
      if (this.isEdit) {
        return this.formData.id
      } else {
        return ''
      }
    },
    /** 文本区域数据 */
    initTextArea: {
      get: function() {
        if (this.isEdit && this.files.resUrl) {
          let top = 0
          let bottom = 0
          let left = 0
          let right = 0
          const { startPoint, endPoint, width, height } = this.files.resUrl

          if (startPoint && startPoint.split(',').length > 0) {
            left = (Number(startPoint.split(',')[0]) * width * 0.01).toFixed(1)
            top = (Number(startPoint.split(',')[1]) * height * 0.01).toFixed(1)
          }

          if (endPoint && endPoint.split(',').length > 0) {
            right = (Number(endPoint.split(',')[0]) * width * 0.01).toFixed(1)
            bottom = (Number(endPoint.split(',')[1]) * height * 0.01).toFixed(1)
          }

          return { top, bottom, left, right }
        }
        return this.textArea
        // return {
        //   top: 2,
        //   bottom: 1,
        //   left: 1,
        //   right: 1
        // }
      },
      set: function(newValue) {
        this.textArea = newValue
      }
    }
  },
  watch: {
    formData: function(newVal, oldValue) {
      console.log('get new value', newVal)
    }
  },
  mounted() {
    console.log('is edit:', this.isEdit)
    console.log('do mounted')
    this.initializeTextArea()
  },
  created() {
    console.log('do created')
  },
  methods: {
    /** 初始化文本区域*/
    initializeTextArea() {
      if (this.isEdit && this.files.resUrl) {
        let top = 0
        let bottom = 0
        let left = 0
        let right = 0
        const { startPoint, endPoint, width, height } = this.files.resUrl
        console.log(' this.files.resUrl', this.files.resUrl)
        if (startPoint && startPoint.split(',').length > 0) {
          left = (Number(startPoint.split(',')[0]) * width * 0.01).toFixed(1)
          top = (Number(startPoint.split(',')[1]) * height * 0.01).toFixed(1)
        }

        if (endPoint && endPoint.split(',').length > 0) {
          right = (Number(endPoint.split(',')[0]) * width * 0.01).toFixed(1)
          bottom = (Number(endPoint.split(',')[1]) * height * 0.01).toFixed(1)
        }
        this.textArea = { top, bottom, left, right }
        console.log(' this.textArea', this.textArea)
      }
    },
    handleBack(val) {
      this.$emit('handleBack', val)
    },
    /** 子组件上传文件 */
    updateFile(key, file) {
      console.log('receive updateFile')
      this.uploadFiles[key] = {
        file,
        key
      }
      console.log(this.uploadFiles)
    },
    /** 校验文件是否上传 */
    validateFiles() {
      let flag = true
      // 合并
      if (this.isEdit) {
        Object.keys(this.files).forEach(key => {
          const item = this.uploadFiles[key]
          console.log('item', item)
          if (!item || !item.hasOwnProperty('file')) {
            this.uploadFiles[key] = this.files[key]
          }
        })
      }
      // 校验
      Object.keys(this.uploadFiles).forEach(key => {
        if (!flag) {
          return
        }
        const file = this.uploadFiles[key]
        if (TIP_MESSAGES[key] && file && !file.file && (!file.pic || file.pic.indexOf('http') < 0)) {
          this.$message.error(TIP_MESSAGES[key])
          flag = false
        }
      })
      return flag
    },
    /**
   * 起点终点转为百分比
   */
    async getPoint(url, textArea) {
      let startPoint = ''
      let endPoint = ''
      let width = ''
      let height = ''
      const res = await getImageSize(url)
      if (res) {
        width = res.width
        height = res.height
        startPoint =
        parseFloat(textArea.left * 100.0 / width).toFixed(2) +
        ',' +
        parseFloat(textArea.top * 100.0 / height).toFixed(2)
        endPoint =
        parseFloat(textArea.right * 100.0 / width).toFixed(2) +
        ',' +
        parseFloat(textArea.bottom * 100.0 / height).toFixed(2)
      }
      console.log('getPoint', startPoint, endPoint, width, height)
      return { startPoint, endPoint, width, height }
    },
    /** 保存 */
    async handleSave() {
      if (this.validateFiles()) {
        const upFiles = []
        const picDto = {}
        Object.keys(this.uploadFiles).forEach(key => {
          const item = this.uploadFiles[key]
          if (item.file) {
            item.file.key = key
            upFiles.push(item.file)
          } else {
            picDto[key] = { pic: item.pic }
          }
        })
        // 上传
        oss.uploadFiles(upFiles, (results, error) => {
          console.log(results, error)
          if (error) {
            this.$message.error('文件上传失败，请检查')
            return
          }
          if (results) {
            results.forEach(res => {
              picDto[res.key] = { pic: res.name }
            })
          }
          /** 计算区域 */
          this.getPoint(picDto.resUrl.pic, this.textArea).then(point => {
            picDto.resUrl = { ...picDto.resUrl, ...point }
            // 保存
            const params = {
              id: this.addId,
              mId: this.mId,
              length: this.paperLabel,
              isNew: this.formData.isNew,
              type: this.formData.type,
              newFlagBeforeDate: this.formData.newFlagBeforeDate,
              picDto
            }
            console.log('params:', params)
            // return
            saveRecord(params).then(res => {
              if (res.head.ret === 0) {
                this.$message.success('保存成功')
                this.handleBack('1')
              } else {
                this.$message.error(res.head.msg)
              }
            })
          })
        })
      }
    }
  }
}
</script>
