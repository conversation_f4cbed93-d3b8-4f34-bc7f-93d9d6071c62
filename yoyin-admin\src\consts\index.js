/*
 * @Author: your name
 * @Date: 2019-10-10 13:55:00
 * @LastEditTime: 2019-10-22 17:27:02
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /d:\src\starPrinter\xstar-ui\star-printer-console-2.0\src\consts\index.ts
 */
// 'https://api.yoyin.net' // test  api  http://test.yoyin.net   http://************:20010
// export const CURR_ENV = process.env.NODE_ENV // test    proc
export const CURR_ENV = process.env.VUE_APP_ENV
console.log('CURR_ENV', CURR_ENV)
export const BASE_URL = CURR_ENV === 'development' ? 'http://localhost:20010' : CURR_ENV === 'test' ? 'https://test.yoyin.net' : 'https://api.yoyin.net'
console.log('BASE_URL', BASE_URL)
export const FILE_URL = 'https://m.yoyin.net'
export const TOKEN_KEY = 'jwttoken'
export const UserInfoKey = 'USER_INFO'
export const AuthListKey = 'AUTH_LIST'
export const LoginInfoKey = 'LoginInfo'
export const LoginAccountKey = 'Account'
export const DEFAULT_INDEX_PATH = '/data-analysis'

// header头信息
export const USER_AGENT = 'sadais-agent'
export const VERSION = '2.0'
export const APPID = 'HEALTH'
export const APPNAME = 'HEALTHWEIGHT_PLATFORM'
export const SYSTEAM = 'WEB'

const constsIndex = {}

export const getUserAgent = () => {
  // 应用英文名称（全大写）/当前版本/系统（全大写）/渠道信息/系统信息/网络信息/运营商(小程序取不到运营商，此项为空)，
  return `${APPNAME}/${VERSION}/${SYSTEAM}/${APPID}///`
}

export default constsIndex

export const deviceNameTypeList = [
  { 'value': 'TP1', 'key': 'TP1' },
  { 'key': 'starpany200', 'value': 'starpany200(TP1-Y)' },
  { 'key': 'starpany', 'value': 'starpany(TP1)' },
  { 'key': 'TP2-Y', 'value': 'TP2-Y' },
  { 'key': 'starpanyTP2', 'value': 'starpanyTP2(TP2)' },
  { 'key': 'starpanyTP2-SZ', 'value': 'starpanyTP2-SZ' },
  { 'key': 'TP2-S', 'value': 'TP2-S' },
  { 'key': 'TP6', 'value': 'TP6' },
  { 'key': 'CP1', 'value': 'CP1' },
  { 'key': 'CP1-L', 'value': 'CP1-L' },
  { 'key': 'CP2', 'value': 'CP2' },
  { 'key': 'CP3', 'value': 'CP3' },
  { 'key': 'CP4', 'value': 'CP4' },
  { 'key': 'TP6-S', 'value': 'TP6-S' },
  { 'key': 'MP2', 'value': 'MP2' },
  { 'key': 'MP3', 'value': 'MP3' },
  { 'key': 'MP3-C', 'value': 'MP3-C' },
  { 'key': 'CP03', 'value': 'CP03' },
  { 'key': 'CP04', 'value': 'CP04' },
  { 'key': 'NT2', 'value': 'NT2' },
  { 'key': 'TP3', 'value': 'TP3' },
  { 'key': 'TP3-S', 'value': 'TP3-S' },
  { 'key': 'TP4', 'value': 'TP4' },
  { 'key': 'starpanyTP8', 'value': 'starpanyTP8' },
  { 'key': '4B-2081PA', 'value': '4B-2081PA(中性型号)' },
  { 'key': 'XP-P8101B', 'value': 'XP-P8101B' },
  { 'key': 'P81', 'value': 'P81' },
  { 'key': 'P82', 'value': 'P82' },
  { 'key': 'P83', 'value': 'P83' },
  { 'key': 'P83C', 'value': 'P83C' },
  { 'key': 'P800', 'value': 'P800' },
  { 'key': 'PB821', 'value': 'PB821' },
  { 'key': 'XP-P81XP-T81', 'value': 'XP-P81XP-T81' },
  { 'key': '4B-2081PTA', 'value': '4B-2081PTA' },
  { 'key': 'XP-D81', 'value': 'XP-D81' },
  { 'key': 'XP-D82', 'value': 'XP-D82' },
  { 'key': 'A01', 'value': 'A01' }
]

export const printerTypeConstantList = [
  { 'type': '1', 'value': 'tp1,tp1-y', 'name': 'TP1/TP1-Y' },
  { 'type': '1', 'value': 'tp2,tp2-y', 'name': 'TP2/TP2-Y' },
  { 'type': '1', 'value': 'tp2-s', 'name': 'TP2-S' },
  { 'type': '1', 'value': 'tp3', 'name': 'TP3' },
  { 'type': '1', 'value': 'tp3-s', 'name': 'TP3-S' },
  { 'type': '1', 'value': 'tp6', 'name': 'TP6' },
  { 'type': '1', 'value': 'tp6-s', 'name': 'TP6-S' },
  { 'type': '1', 'value': 'cp1-l', 'name': 'CP1-L' },
  { 'type': '1', 'value': 'cp3', 'name': 'CP3' },
  { 'type': '1', 'value': 'cp03', 'name': 'CP03' },
  { 'type': '1', 'value': 'cp04', 'name': 'CP04' },
  { 'type': '1', 'value': 'cp4', 'name': 'CP4' },
  { 'type': '1', 'value': 'nt2', 'name': 'NT2' },
  { 'type': '1', 'value': 'tp4', 'name': 'TP4' },
  { 'type': '1', 'value': 'mp2', 'name': 'mp2' },
  { 'type': '1', 'value': 'mp3', 'name': 'mp3' },
  { 'type': '1', 'value': 'mp3-c', 'name': 'mp3-c' },
  { 'type': '2', 'value': 'p81', 'name': 'P81' },
  { 'type': '2', 'value': 't81', 'name': 'T81' },
  { 'type': '2', 'value': 'd81', 'name': 'D81' },
  { 'type': '2', 'value': 'd81_tm', 'name': 'D81-天猫精灵' },
  { 'type': '2', 'value': 'd82', 'name': 'D82' },
  { 'type': '2', 'value': 'p82', 'name': 'P82' },
  { 'type': '2', 'value': 'p83', 'name': 'P83' },
  { 'type': '2', 'value': 'p83C', 'name': 'P83C' },
  { 'type': '2', 'value': 'p800', 'name': 'P800' },
  { 'type': '2', 'value': 'pb821', 'name': 'PB821' },
  { 'type': '2', 'value': 'a01', 'name': 'A01' }
]

export const printerTypeConstantList2 = [
  { 'value': 'TP1', 'key': 'tp1' },
  { 'value': 'TP1-Y', 'key': 'tp1-y' },
  { 'value': 'TP2', 'key': 'tp2' },
  { 'value': 'TP2-S', 'key': 'tp2-s' },
  { 'value': 'TP2-Y', 'key': 'tp2-y' },
  { 'value': 'TP3', 'key': 'tp3' },
  { 'value': 'TP3-S', 'key': 'tp3-s' },
  { 'value': 'TP4', 'key': 'tp4' },
  { 'value': 'TP6', 'key': 'tp6' },
  { 'value': 'TP6-S', 'key': 'tp6-s' },
  { 'value': 'CP1-L', 'key': 'cp1-l' },
  { 'value': 'CP3', 'key': 'cp3' },
  { 'value': 'CP03', 'key': 'cp03' },
  { 'value': 'CP04', 'key': 'cp04' },
  { 'value': 'CP4', 'key': 'cp4' },
  { 'value': 'NT2', 'key': 'nt2' },
  { 'value': 'MP2', 'key': 'mp2' },
  { 'value': 'MP3', 'key': 'mp3' },
  { 'value': 'MP3-C', 'key': 'mp3-c' },
  { 'value': 'P81', 'key': 'p81' },
  { 'value': 'T81', 'key': 't81' },
  { 'value': 'D81_TM', 'key': 'd81_tm' },
  { 'value': 'D81', 'key': 'd81' },
  { 'value': 'D82', 'key': 'd82' },
  { 'value': 'P82', 'key': 'p82' },
  { 'value': 'P83', 'key': 'p83' },
  { 'value': 'P83C', 'key': 'p83c' },
  { 'value': 'P800', 'key': 'p800' },
  { 'value': 'PB821', 'key': 'pb821' },
  { 'value': 'A01', 'key': 'a01' }
]

export const versionDataList = [
  { 'value': 'firmware', 'key': 'firmware' },
  { 'value': 'firmware_200', 'key': 'firmware_200' },
  { 'value': 'firmwareTP2', 'key': 'firmwareTP2' },
  { 'value': 'firmware_TP2_SZ_200', 'key': 'firmware_TP2_SZ_200' },
  { 'value': 'firmware_TP2_Y', 'key': 'firmware_TP2_Y' },
  { 'value': 'firmwareTP3', 'key': 'firmwareTP3' },
  { 'value': 'firmwareTP3-S', 'key': 'firmwareTP3_S' },
  { 'value': 'firmwareTP4', 'key': 'firmwareTP4' },
  { 'value': 'firmwareTP6', 'key': 'firmwareTP6' },
  { 'value': 'firmwareCP1-L', 'key': 'firmwareCP1_L' },
  { 'value': 'firmwareCP3', 'key': 'firmwareCP3' },
  { 'value': 'firmwareCP4', 'key': 'firmwareCP4' },
  { 'value': 'firmwareNT2', 'key': 'firmwareNT2' },
  { 'value': 'firmwareMP2', 'key': 'firmwareMP2' },
  { 'value': 'firmwareMP3', 'key': 'firmwareMP3' },
  { 'value': 'firmwareCP03', 'key': 'firmwareCP03' },
  { 'value': 'firmwareCP04', 'key': 'firmwareCP04' },
  { 'value': 'firmwareMP3-C', 'key': 'firmwareMP3_C' },
  { 'value': 'firmwareP81', 'key': 'firmwareP81' },
  { 'value': 'firmware_XP_T81', 'key': 'firmware_XP_T81' },
  { 'value': 'firmware_XP_D81_TM', 'key': 'firmware_XP_D81_TM' },
  { 'value': 'firmware_XP_D81', 'key': 'firmware_XP_D81' },
  { 'value': 'firmwareD82', 'key': 'firmwareD82' },
  { 'value': 'firmwareP82', 'key': 'firmwareP82' },
  { 'value': 'firmwareP83', 'key': 'firmwareP83' },
  { 'value': 'firmwareP83C', 'key': 'firmwareP83C' },
  { 'value': 'firmwareP800', 'key': 'firmwareP800' },
  { 'value': 'firmwarePB821', 'key': 'firmwarePB821' },
  //A01
  { 'value': 'firmwareA01', 'key': 'firmwareA01' },
]