<template>
  <div>
    <transition-group name="dis">
      <div class="header-title" v-show="showFlag.help" :key="'showFlagHelpTitle'">
        <div class="header-title-text">视频教程</div>
      </div>
      <div class="prod-content" v-show="showFlag.help" :key="'showFlagHelpContent'">
        <div class="prod-content-help">
          <div class="prod-content-help-item" v-for="item in helpItems" :key="item.info" @click="showVideo(item.videoUrl)">
            <img :src="item.url">
            <div class="p">{{item.info}}</div>
          </div>
        </div>
      </div>
      <div class="header-title" v-show="showFlag.question" :key="'showFlagQuestionTitle'">
        <div class="header-title-text">常见问题</div>
      </div>
      <div class="prod-content" v-show="showFlag.question" :key="'showFlagQuestionContent'">
        <div class="prod-content-problem" v-for="(item, index) in answerItems" :key="item.title">
          <div class="prod-content-problem-title">
            <div class="title">
              <img src="https://m.yoyin.net/h5/img/mobile/service/ask.png">
              <div class="text">{{item.title}}</div>
            </div>
            <transition name="slide-fade">
              <img src="https://m.yoyin.net/h5/img/mobile/service/collect.png" v-show="item.isActive" @click="answerItems[index].isActive=false">
            </transition>
            <transition name="slide-fade">
              <img src="https://m.yoyin.net/h5/img/mobile/service/open.png" v-show="!item.isActive" @click="answerItems[index].isActive=true">
            </transition>
          </div>
          <collapse>
            <div v-show="item.isActive" class="prod-content-problem-container">
              <img src="https://m.yoyin.net/h5/img/mobile/service/answer.png">
              <div class="text" v-html="item.answer"></div>
              <div class="clearline">&nbsp;</div>
            </div>
          </collapse>
        </div>
      </div>
      <div class="header-title" v-show="showFlag.service" :key="'showFlagServiceTitle'">
        <div class="header-title-text">官方客服</div>
      </div>
      <div class="prod-content" v-show="showFlag.service" :key="'showFlagServiceContent'">
        <div class="prod-content-service">
          <div class="title">
            如以上回答未能解决您的问题，请联系客服：
          </div>
          <div class="item">
            <img src="https://m.yoyin.net/h5/img/mobile/service/phone.png">
            <div class="text">0756-2897617</div>
          </div>
          <div class="item">
            <img src="https://m.yoyin.net/h5/img/mobile/service/weixin.png">
            <div class="text">youyin0514</div>
          </div>
        </div>
      </div>
    </transition-group>
    <transition name="dis">
      <div v-show="showFlag.foot" :key="'showFlagFoot'">
        <foot-info></foot-info>
      </div>
    </transition>
    <div class="zhezh" v-show="showZY" :key="'showZY111'" @click="showZY=!showZY; showIndex=0; videoUrl=''">
    </div>
    <div class="videoDialogClass" v-show="showZY">
      <video :src="videoUrl" controls autoplay class="video" width="375rem" height="500rem"></video>
    </div>
  </div>
</template>

<script>
import collapse from '../../../assets/mobile/js/collapse'

import FootInfo from './FootInfo'
// import info
export default {
  components: {FootInfo, collapse},
  name: 'ServiceInfo',
  data () {
    return {
      showFlag: {
        help: false,
        question: false,
        service: false,
        foot: false
      },
      isActive: false,
      helpItems: [{
        url: 'https://m.yoyin.net/h5/img/mobile/service/1.png',
        info: '2寸打印机如何开机、关机？',
        videoUrl: 'https://m.yoyin.net/h5/mp4/03.mp4'
      }, {
        url: 'https://m.yoyin.net/h5/img/mobile/service/2.png',
        info: '2寸打印机如何连接柚印APP？',
        videoUrl: 'https://m.yoyin.net/h5/mp4/02.mp4'
      }, {
        url: 'https://m.yoyin.net/h5/img/mobile/service/3.png',
        info: '2寸打印机如何安装打印纸？',
        videoUrl: 'https://m.yoyin.net/h5/mp4/01.mp4'
      }, {
        url: 'https://m.yoyin.net/h5/img/mobile/service/4.png',
        info: 'A4打印机如何安装打印纸？',
        videoUrl: 'https://m.yoyin.net/h5/mp4/A4/04.mp4'
      }, {
        url: 'https://m.yoyin.net/h5/img/mobile/service/5.png',
        info: '如何安装A5打印纸？',
        videoUrl: 'https://m.yoyin.net/h5/mp4/A4/02.mp4'
      }, {
        url: 'https://m.yoyin.net/h5/img/mobile/service/6.png',
        info: 'A4打印机如何开机、关机？',
        videoUrl: 'https://m.yoyin.net/h5/mp4/A4/03.mp4'
      }, {
        url: 'https://m.yoyin.net/h5/img/mobile/service/7.png',
        info: 'A4打印机如何连接APP？',
        videoUrl: 'https://m.yoyin.net/h5/mp4/A4/01.mp4'
      }],
      answerItems: [{
        title: '打印内容可以保存多久呢？',
        answer: '柚印错题打印机使用的是热敏打印纸，如妥善保管，字迹可留存5年或10年左右。（仅限通过官方渠道购买的打印纸，具体保留时长视打印纸而定）',
        isActive: false
      }, {
        title: '如何妥善保管打印内容？',
        answer: '<b>粘贴时需注意：</b><br>' +
          '①一切带有胶质的东西都不能沾到打印内容上，否则字迹会消失；<br>' +
          '②可使用点点胶，纸胶带等；<br>' +
          '③可使用固体胶粘贴无字迹的一面，或者使用透明胶粘贴没有字迹的四角。<br>' +
          '<b>保管时需注意：</b><br>' +
          '①避免阳光直射或高温环境；<br>' +
          '②需保存在阴凉干燥处，最好是贴在或夹在书本中。',
        isActive: false
      }, {
        title: '如何调整打印浓度？',
        answer: '1.打印时，在预览界面点击【打印设置】，调整浓度，浓度有3个选项，依次为偏淡、适中、偏浓；<br>' +
          '2.打印出来的图片浓度较浅，还可能与打印机电量相关，可选择充满电或者电量保持在50%以上进行打印。',
        isActive: false
      }, {
        title: '2寸打印机指示灯一直闪是什么情况？',
        answer: '1.红灯常亮--正常使用状态；<br>' +
          '2.红灯快闪-缺纸、开盖、过热、电量过低；<br>' +
          '3.红灯慢闪--需关机充电。',
        isActive: false
      }],
      videoUrl: '',
      dialogPlay: false,
      showZY: false
    }
  },
  mounted () {
    const that = this
    setTimeout(function () {
      that.showFlag.help = true
    }, 300)
    setTimeout(function () {
      that.showFlag.question = true
    }, 500)
    setTimeout(function () {
      that.showFlag.service = true
    }, 700)
    setTimeout(function () {
      that.showFlag.foot = true
    }, 900)
  },
  methods: {
    closeDialog () {
      this.videoUrl = ''
      this.showZY = false
    },
    showVideo (url) {
      this.videoUrl = url
      this.showZY = true
    }
  }

}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
html, body {
  overflow-x: hidden;
  touch-action:none;
  touch-action:pan-y;
}
.header-title {
  min-height: 4.3rem;
  background-image: url("https://m.yoyin.net/h5/img/mobile/home/<USER>");
  background-position: left center;
  background-repeat: no-repeat;
  background-size:4.3rem 100%;
  position: relative;
  margin-bottom: 1.2rem;
  &-text {
    float: left;
    padding-left: 2.2rem;
    padding-top: 1.6rem;
    font-size: 1.7rem;
  }
}
.prod-content {
  display: flex;
  flex-wrap: wrap;
  width: 30rem;
  padding-bottom: 4.3rem;
  text-align: left;

  &-help {
    display: flex;
    flex-wrap: nowrap;
    overflow: auto;
    height: 17.1rem;
    &-item {
      text-align: center;
      width: 14.7rem;
      height: 15.1rem;
      background: #FFFFFF;
      box-shadow: 0px 10px 13px rgba(0, 0, 0, 0.03);
      opacity: 1;
      border-radius: 10px;
      margin-right: 1.5rem;
      img {
        width: 14.7rem;
        height: 8.4rem;
        font-size: 1.4rem;
        color: #222222;
      }
      .p {
        padding: 0 1.1rem;
        text-align: left;
        font-size: 1.4rem;
      }
    }
  }
  &-problem {
    width: 27.8rem;
    background: #FFFFFF;
    box-shadow: 0px 10px 13px rgba(0, 0, 0, 0.03);
    opacity: 1;
    border-radius: 10px;
    margin-bottom: 1.1rem;

    &-title {
      position: relative;
      height: 5.5rem;
      width: 100%;
      .title {
        display: flex;
        width: 24.7rem;
        img {
          position: relative;
          width: 2.2rem;
          height: 2.3rem;
          padding: 1.6rem 1.1rem 1.6rem 1.5rem;
          top:0;
          left: 0;
        }
        .text {
          font-size: 1.6rem;
          //line-height: 5.5rem;
          display: flex;
          justify-content: left;
          align-items: center;
        }
      }

      img {
        position: absolute;
        width: 1.6rem;
        height: 1.6rem;
        top: 1.8rem;
        right: 1.5rem;
      }

    }
    &-container {
      display: flex;
      width: 27.8rem;
      border-top: 1px solid #EEEEEE;
      opacity: 1;
      flex-wrap: wrap;
      .text {
        width: 21rem;
        padding-top: 1.6rem;
        font-size: 1.5rem;
        line-height: 2.7rem;
        color: #666666;
      }
      img {
        width: 2.2rem;
        height: 2.3rem;
        margin: 1.6rem 1.1rem 0rem 1.5rem;
      }
      .clearline {
        width: 100%;
        min-height: 2.5rem;
      }
    }
  }
  &-service {
    //padding: 2.7rem 0rem 2.7rem 1.6rem;
    margin-right: 1.6rem;
    width: 27.8rem;
    height: 22.1rem;
    background: #FFFFFF;
    box-shadow: 0px 10px 13px rgba(0, 0, 0, 0.03);
    opacity: 1;
    border-radius: 10px;
    .title{
      padding: 2.7rem 1.6rem 0 1.6rem;
      font-size: 1.6rem;
      color: #222222;
      line-height: 2.7rem;
    }
    .item {
      padding: 0 1.6rem;
      height: 5.3rem;
      border-bottom: 1px solid #EEEEEE;
      display: flex;
      img {
        padding: 1.6rem 1.3rem 0 0;
        width: 2.2rem;
        height: 2.2rem;
      }
      .text {
        font-size: 1.6rem;
        line-height: 5.3rem;
      }
    }
  }
}

.slide-fade-enter-active {transition: all .2s ease;}
.slide-fade-leave-active {transition: all .2s;}
.slide-fade-enter, .slide-fade-leave-to{transform: rotate(180deg);opacity: 0;}

.dis-enter{
  opacity:0;
}
.dis-enter-active{
  transition:opacity 1.5s;
}
.dis-leave-active{
  transition:transform 1s;
}
.dis-leave-to{
  transform: translateX(0);
}
.zhezh{
  position: absolute;
  top: 0%;
  left: 0%;
  right: 0;
  bottom: 0%;
  width: 100%;
  height: 100%;
  background-color: black;
  z-index: 999;
  /*-moz-opacity: 0.1;*/
  opacity: 0.8;
}

.videoDialogClass {
  position: fixed;
  top: 10%;
  left: 0;
  width: 37.5rem;
  z-index: 9999;
}
</style>
