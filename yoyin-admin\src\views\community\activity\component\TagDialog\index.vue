<template>
  <el-form ref="form" :model="form" label-width="120px" class="edit-form">
    <el-form-item v-if=" labelList !== null && !isStudyLabel" label="标签：" prop="labelIds">
      <el-checkbox-group v-model="form.labelIds">
        <el-checkbox v-for="(item, index) in labelList" :key="index" :label="item.id" @change="onChangeLabel(item)">{{ item.titleZh }}</el-checkbox>
      </el-checkbox-group>
    </el-form-item>
    <el-form-item v-if="!isStudyLabel" label="展示在标签列表：" prop="isLabel">
      <el-radio-group v-model="isLabel">
        <el-radio-button label="1">是</el-radio-button>
        <el-radio-button label="0">否</el-radio-button>
      </el-radio-group>
    </el-form-item>

    <el-form-item v-if="isStudyLabel" label="学年：" prop="gradeLevel">
      <el-radio-group v-model="form.gradeLevel">
        <el-radio-button label="xx">小学</el-radio-button>
        <el-radio-button label="cz">初中</el-radio-button>
        <el-radio-button label="gz">高中</el-radio-button>
      </el-radio-group>
    </el-form-item>
    <el-form-item v-if="isStudyLabel" label="学级：" prop="gradeNum">
      <el-radio-group v-model="form.gradeNum">
        <el-radio-button v-for="item in gradeNumList" :key="item.key" :label="item.key">{{ item.label }}</el-radio-button>
      </el-radio-group>
    </el-form-item>
    <el-form-item v-if="isStudyLabel" label="学科：" prop="gradeNum">
      <el-radio-group v-model="form.subject">
        <el-radio-button v-for="item in subjectList" :key="item.id" :label="item.id">{{ item.name }}</el-radio-button>
      </el-radio-group>
    </el-form-item>

    <el-form-item>
      <el-button @click="handleClose">返回</el-button>
      <el-button type="primary" @click="submit">保存</el-button>
    </el-form-item>
  </el-form>
</template>

<script>
import { getById, findLabelPage, getSubjectList, updateFeedNoteTag } from '@/api/community/activity'
export default {
  name: 'EditDialog',
  props: {
    feedId: {
      type: String,
      default: ''
    },
    isLabelObject: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      /** 表单 */
      form: {
        codeId: '',
        content: '',
        isLabel: '0',
        labelIds: []
      },
      isLabel: this.isLabelObject,
      /** 待上传的文件 */
      upFile: undefined,
      testUrl: '',
      /** 时间格式 */
      timeFormat: 'yyyy-MM-dd HH:mm:ss',
      labelList: [],
      /** 上传文件的列表 */
      fileList: [],
      levelOne: [{ 'key': 1, 'label': '一年级' },
        { 'key': 2, 'label': '二年级' },
        { 'key': 3, 'label': '三年级' },
        { 'key': 4, 'label': '四年级' },
        { 'key': 5, 'label': '五年级' },
        { 'key': 6, 'label': '六年级' }],
      levelTwo: [{ 'key': 7, 'label': '初一' },
        { 'key': 8, 'label': '初二' },
        { 'key': 9, 'label': '初三' }],
      levelThree: [{ 'key': 10, 'label': '高一' },
        { 'key': 11, 'label': '高二' },
        { 'key': 12, 'label': '高三' }],
      isStudyLabel: false,
      subjectList: []
    }
  },
  computed: {
    gradeNumList() {
      let arr = []
      if (this.form.gradeLevel === 'xx') {
        arr = this.levelOne
      } else if (this.form.gradeLevel === 'cz') {
        arr = this.levelTwo
      } else if (this.form.gradeLevel === 'gz') {
        arr = this.levelThree
      }
      return arr
    }
  },
  watch: {
    feedId(val) {
      this.getFeedNoteById()
    }
  },
  mounted() {
    this.getFeedNoteById()
    this.getLabelList()
    this.getMySubjectList()
  },
  methods: {
    async getMySubjectList() {
      const res = await getSubjectList()
      if (res.head.ret === 0) {
        this.subjectList = res.data
      }
    },
    /** 获取标签列表 */
    async getLabelList() {
      const res = await findLabelPage({
        pageno: 1,
        pagesize: 10
      })
      if (res.head.ret === 0) {
        this.labelList = res.data
      }
    },
    /** 保存 */
    async getFeedNoteById() {
      // 如果id为空，则不搜索
      if (!this.feedId) {
        return
      }
      const param = {
        feedid: this.feedId
      }
      // 获取当前feedNote实体
      const res = await getById(param)
      if (res.head.ret === 0) {
        console.log(res)
        this.form = res.data.feedNote
        this.isStudyLabel = res.data.isStudyLabel
      } else {
        this.$message.error(res.head.msg)
      }
    },
    /** 返回 */
    handleClose(code) {
      this.$emit('closeDialog', code)
    },
    /** 移除图片 */
    handelRemove(file, fileList) {
      if (this.fileList.length > 0) {
        this.fileList.splice(this.fileList.findIndex(item => item.file.uid === file.uid), 1)
      }
    },
    /** 自定义上传  覆盖action */
    customUpload(arg) {
      console.log('customUpload', arg)
      const file = arg.file
      this.fileList.push({
        name: file.name,
        url: URL.createObjectURL(file),
        file
      })
      this.form.pic = [...this.fileList]
      console.log(this.fileList)
    },

    /** 提交 */
    submit() {
      const parms = {
        id: this.form.id,
        labelIds: this.form.labelIds,
        gradeLevel: this.form.gradeLevel,
        subject: this.form.subject,
        gradeNum: this.form.gradeNum,
        isLabel: this.isLabel
      }
      updateFeedNoteTag(parms).then(res => {
        console.log(res)
        this.handleClose()
      })
    },
    /** 保存 */
    async addUpdateRecord() {
      // // 新增
      // if (!this.form.id) {
      //   this.form.id = ''
      // }
      // // 更新
      // const res = await saveRecord(this.form)
      // if (res.head.ret === 0) {
      //   this.handleClose('1')
      //   this.$message.success('保存成功')
      // } else {
      //   this.$message.error(res.head.msg)
      // }
    },
    /** 勾选标签 */
    onChangeLabel(item) {
      const str = '#' + item.titleZh + '#'
      // 偶数次加标签
      if (item.count % 2 === 0) {
        this.form.content += str
      } else {
        if (this.form.content.indexOf(str) > -1) {
          this.form.content = this.form.content.replace(str, '')
        }
      }
      // 奇数查找删除 对应标签
      item.count++
    }
  }

}
</script>

<style lang='scss' scoped>
.label-span{
  line-height: 30px;
  height: 30px;
  border-radius: 5px;
  margin-right: 10px;
  cursor: pointer;
}
.check-label-span{
  background: #409EFF;
  padding: 5px;
}
 .avatar-uploader .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }
  .avatar-uploader .el-upload:hover {
    border-color: #409EFF;
  }
  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 178px;
    height: 178px;
    line-height: 178px;
    text-align: center;
    border: 1px solid gray;
  }
  .avatar {
    width: 178px;
    height: 178px;
    display: block;
  }
</style>
