<template>
  <div class="content">
  </div>
</template>

<script>
export default {
  data () {
    return {
      moduleKey: 'download'
    }
  },
  async mounted () {
    let ua = navigator.userAgent
    let isiOS =
      !!ua.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/) ||
      ua.indexOf('iPhone') > -1 ||
      ua.indexOf('iPad') > -1
    if (isiOS) {
      window.location.href = 'https://itunes.apple.com/cn/app/id1440940435'
    } else {
      window.location.href =
        'https://a.app.qq.com/o/simple.jsp?pkgname=com.starprinter.app&channel=0002160650432d595942&fromcase=60001'
    }
  },
  methods: {},
  components: {}
}
</script>

<style lang="scss">
.content {
  padding: 1.2rem;
}
</style>
