<!--
 * @Description: In User Settings Edit
 * @Author: your name
 * @Date: 2019-10-16 11:28:20
 * @LastEditTime: 2019-10-22 17:03:31
 * @LastEditors: Please set LastEditors
 -->
<template>
  <div class="app-container">
    <div class="control-container">
      <el-button type="primary" @click="queryVersion">查询</el-button>
      <el-button v-permission="'btn-menuOther-account-edit'" @click="addVersion">添加</el-button>
    </div>
    <div class="table-container">
      <el-table
        ref="multipleTable"
        :data="tableList"
        tooltip-effect="dark"
        style="width: 100%"
        border
      >
        <el-table-column label="姓名">
          <template slot-scope="scope">{{ scope.row.name }}</template>
        </el-table-column>
        <el-table-column prop="account" label="账号" />
        <el-table-column label="操作" fixed="right">
          <template slot-scope="scope">
            <el-button size="mini" @click="handleEdit(scope.$index, scope.row)">编辑</el-button>
            <el-button v-permission="'btn-menuOther-account-edit'" size="mini" type="success" @click="handleAuthoriza(scope.$index, scope.row)">授权</el-button>
            <el-button
              v-show="scope.row.account!=='xplifetest' && scope.row.account!=='xplife'"
              v-permission="'btn-menuOther-account-edit'"
              size="mini"
              type="danger"
              @click="handleDeleteRow(scope.$index, scope.row)"
            >删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="pagination-container" style="margin-top:20px;">
      <el-pagination
        :current-page="currentPage"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="currentSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
    <!-- 弹框 -->
    <div class="dialog-container">
      <el-dialog
        :title="form.id === '' ? '新增' : '编辑' "
        :visible.sync="showDialog"
        width="50%"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        center
      >
        <el-form
          ref="ruleForm"
          :model="form"
          :rules="rules"
          label-width="100px"
          class="demo-ruleForm"
        >
          <el-form-item label="姓名" prop="name">
            <el-input v-model="form.name" />
          </el-form-item>
          <el-form-item label="账号" prop="account">
            <el-input v-model="form.account" />
          </el-form-item>
          <el-form-item label="密码" prop="password">
            <el-input v-model="form.password" />
          </el-form-item>
          <el-form-item>
            <el-button @click="showDialog = false">返回</el-button>
            <el-button v-permission="'btn-menuOther-account-edit'" type="primary" :loading="loading" @click="submitForm">保存</el-button>
          </el-form-item>
        </el-form>
      </el-dialog>
    </div>

    <!-- 弹框 -->
    <div class="dialog-container">
      <el-dialog
        :title="'授权'"
        :visible.sync="showDialogAuth"
        width="50%"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        center
      >
        <el-form
          label-width="100px"
          class="demo-ruleForm"
        >
          <el-form-item label="选择角色" prop="name">
            <el-checkbox-group v-model="roleList">
              <el-checkbox-button v-for="item in rolesData" :key="item.id" :label="item.id">{{ item.roleName }}</el-checkbox-button>
            </el-checkbox-group>
          </el-form-item>
          <el-form-item>
            <el-button @click="showDialogAuth = false">返回</el-button>
            <el-button v-permission="'btn-menuOther-account-edit'" type="primary" :loading="loading" @click="submitFormAuth">保存</el-button>
          </el-form-item>
        </el-form>
      </el-dialog>
    </div>
  </div>
</template>
<script>
import { fetchListByPage, saveRecord, deleteRecord, fetchAccountRoleList, disrole } from '@/api/auth/account'
import { fetchRoleAllList } from '@/api/auth/role'
export default {
  data() {
    return {
      currentPage: 1,
      currentSize: 10,
      total: 0,
      tableList: [],
      roleList: [],
      showDialogAuth: false,
      multipleSelection: [],
      // 弹框
      showDialog: false,
      // 批量
      showMuliDialog: false,
      // 仅上传文件模式
      onlyUpload: false,
      // 表单
      form: {
        id: '',
        name: '',
        password: '',
        account: ''
      },
      // 表单
      multiForm: {
        version: '',
        remark: ''
      },
      // 检验规则
      rules: {
        channel: [
          { required: true, message: '请输入渠道', trigger: 'blur' },
          { min: 3, max: 20, message: '长度在 3 到 20 个字符', trigger: 'blur' }
        ],
        url: [{ required: true, message: '请选择文件', trigger: 'blur' }],
        version: [
          { required: true, message: '请输入版本号如1.1.2', trigger: 'blur' }
        ]
      },
      multiFormRules: {
        version: [
          { required: true, message: '请输入版本号如1.1.2', trigger: 'blur' }
        ]
      },
      fileList: [],
      rolesData: [],
      file: undefined,
      loading: false,
      showUrl: false,
      accountId: ''
    }
  },

  computed: {},
  mounted() {
    this.getList()
    this.getRoleList()
  },
  methods: {
    /** 添加 */
    addVersion() {
      this.form = {}
      this.showDialog = true
      this.fileList = []
      this.file = undefined
      this.showUrl = false
      this.onlyUpload = false
    },
    /** 查询 */
    queryVersion() {
      this.getList()
      this.getRoleList()
    },
    /** 切换每页条数 */
    handleSizeChange(val) {
      console.log(`每页 ${val} 条`)
      this.currentSize = val
      this.getList()
    },
    /** 切换当前页 */
    handleCurrentChange(val) {
      console.log(`当前页: ${val}`)
      this.currentPage = val
      this.getList()
    },
    /** 获取表数据 */
    async getList() {
      const params = {
        pageno: this.currentPage,
        pagesize: this.currentSize
      }
      const res = await fetchListByPage(params)
      if (res.head.ret === 0) {
        const data = res.data
        this.total = data.totalCount
        this.tableList = data.result
      }
    },
    clearSelection() {
      this.$refs.multipleTable.clearSelection()
    },
    /** 勾选事件 */
    handleSelectionChange(val) {
      this.multipleSelection = val
      console.log('handleSelectionChange: ' + this.multipleSelection)
    },
    /** 编辑 */
    handleEdit(index, row) {
      this.form = Object.assign({}, row)
      // this.form.password = ''
      this.showDialog = true
      this.fileList = []
      this.file = undefined
      this.showUrl = true
      this.onlyUpload = false
    },
    /** 编辑 仅上传文件 */
    handleOnlyFile(index, row) {
      this.form = row
      this.showDialog = true
      this.fileList = []
      this.file = undefined
      this.showUrl = true
      this.onlyUpload = true
    },

    /** 删除单行 */
    async handleDeleteRow(index, row) {
      const params = {
        id: row.id
      }
      this.$confirm('此操作将永久删除该记录, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          deleteRecord(params).then(res => {
            if (res.head.ret === 0) {
              this.$message({
                type: 'success',
                message: '删除成功!'
              })
              this.getList()
            }
          })
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    },
    /** 删除单行 */
    async handleMultiDel() {
      this.$confirm('此操作将永久删除记录, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          let i = 0
          const len = this.multipleSelection.length
          for (i = 0; i < len; i++) {
            // const params = {
            //   id: this.multipleSelection[i].id
            // }
          }
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    },

    // 表单编辑
    /** 保存 */
    submitForm() {
      this.saveConfig()
    },
    async saveConfig() {
      const res = await saveRecord(this.form)
      if (res.head.ret === 0) {
        this.$message.success('保存成功')
      } else {
        this.$message.error(res.head.msg)
        this.loading = false
        return
      }
      this.loading = false
      this.showDialog = false
      this.onlyUpload = false
      this.getList()
    },
    async handleAuthoriza(index, row) {
      this.accountId = row.id
      // 读取当前用户授权的
      const params = {
        id: row.id
      }
      const res = await fetchAccountRoleList(params)
      console.log(res)
      if (res.head.ret === 0) {
        this.roleList = res.data
      }
      this.showDialogAuth = true
    },
    async submitFormAuth(index, row) {
      this.loading = true
      const params = {
        id: this.accountId,
        roleIds: this.roleList.join(';')
      }
      const res = await disrole(params)
      if (res.head.ret === 0) {
        this.$message.success('保存成功')
        this.showDialogAuth = false
        this.loading = false
        this.getList()
      } else {
        this.$message.error(res.head.msg)
        this.loading = false
        return
      }
    },
    async getRoleList() {
      const res = await fetchRoleAllList()
      if (res.head.ret === 0) {
        this.rolesData = res.data
      }
    },
    /** 点击批量修改 */
    handleMultiModify() {
      this.showMuliDialog = true
    },
    /** 批量保存 */
    submitMultiForm() {
      this.$refs['ruleMultiForm'].validate(valid => {
        if (valid) {
          this.loading = true
          console.log('this.multiForm: ', this.multiForm)
          this.doMultiSave()
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    async doMultiSave() {
      let i = 0
      const len = this.multipleSelection.length
      let count = 0
      for (i = 0; i < len; i++) {
        const data = { ...this.multipleSelection[i] }
        data.version = this.multiForm.version
        data.remark = this.multiForm.remark
        const res = await saveRecord(data)
        if (res.head.ret === 0) {
          count++
        }
      }
      this.$message.success('保存成功[' + count + ']条, 共' + len + '条')
      this.loading = false
      this.showMuliDialog = false
      this.getList()
    },

    /** 移除之前 */
    handleRemove(file, fileList) {
      console.log('handleRemove')
      this.showUrl = true
    },
    /** 上传前 */
    beforeUpload(file) {
      return false
    },
    changeFile(file) {
      console.log('changeFile')
      console.log(file)
      this.file = file.raw
      this.fileList.pop()
      this.fileList.push({
        name: file.name,
        url: file.name
      })
      this.showUrl = false
    }
  }
}
</script>

<style lang="scss" scoped>
.app-container {
  margin: 20px;
  .control-container {
    margin-bottom: 20px;
  }
  .choose-container {
    margin-bottom: 20px;
    background-color: #e6f7ff;
    border: 1px solid #91d5ff;

    span {
      margin-left: 20px;
      line-height: 48px;
    }
    a {
      margin-left: 20px;
      color: #1890ff;
      text-decoration: none;
      background-color: transparent;
      outline: none;
      cursor: pointer;
      transition: color 0.3s;
    }
  }
}
</style>
