<template>
  <div v-if="visiable" class="edit-dialog">
    <el-dialog
      ref="dialog"
      :show-close="false"
      :title="form.id === '' ? '新增标签' : '编辑标签' "
      :visible.sync="visiable"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      width="50%"
      center
    >
      <el-form ref="form" :model="form" :rules="rules" label-width="100px" class="edit-form">
        <el-form-item label="键值" prop="name">
          <el-input v-model="form.name" />
        </el-form-item>
        <el-form-item label="描述" prop="remark">
          <el-input v-model="form.remark" />
        </el-form-item>
        <el-form-item label="中文名" prop="remark">
          <el-input v-model="form.titleZh" />
        </el-form-item>
        <el-form-item label="英文名" prop="remark">
          <el-input v-model="form.titleEn" />
        </el-form-item>
        <el-form-item prop="sort">
          <el-tooltip slot="label" class="item " effect="dark" content="用于排序" placement="top">
            <span>
              序号<i class="el-icon-warning-outline" />
            </span>
          </el-tooltip>
          <el-input v-model="form.sort" />
        </el-form-item>

        <el-form-item label="类型" prop="showType">
          <el-radio-group v-model="form.showType">
            <el-radio-button label="0">全部</el-radio-button>
            <el-radio-button label="1">素材</el-radio-button>
            <el-radio-button label="2">发帖</el-radio-button>
            <el-radio-button label="99">不显示</el-radio-button>
          </el-radio-group>
        </el-form-item>

        <el-form-item>
          <el-button @click="handleClose">返回</el-button>
          <el-button type="primary" @click="submit">保存</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import { saveRecord } from '@/api/community/tag'

export default {
  name: 'EditDialog',
  props: {
    visiable: {
      type: Boolean,
      required: true,
      default: false
    },
    formData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      /** 表单 */
      form: {},
      /** 校验规则 */
      rules: {
        name: [
          { required: true, message: '请输入键值', trigger: 'blur' },
          { min: 2, max: 20, message: '长度在 2 到 30 个字符', trigger: 'blur' }
        ],
        remark: [
          { required: true, message: '请输入描述', trigger: 'blur' },
          { min: 2, max: 20, message: '长度在 2 到 30 个字符', trigger: 'blur' }
        ],
        titleZh: [
          { required: true, message: '请输入中文名', trigger: 'blur' },
          { min: 2, max: 20, message: '长度在 2 到 30 个字符', trigger: 'blur' }
        ],
        titleEn: [
          { required: true, message: '请输入英文名', trigger: 'blur' },
          { min: 2, max: 20, message: '长度在 2 到 30 个字符', trigger: 'blur' }
        ],
        jumpType: [
          { required: true, message: '请选择类型', trigger: 'change' }
        ],
        sort: [
          { required: true, message: '请输入序号，序号用于排序', trigger: 'blur' }
        ]
      }

    }
  },
  computed: {
  },
  watch: {
    formData: function(val) {
      this.form = { ...val }
    }
  },
  mounted() {
    this.show = this.visiable
  },
  methods: {
    /** 返回 */
    handleClose(code) {
      this.$emit('closeDialog', code)
    },
    /** 提交 */
    submit() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          this.addUpdateRecord()
        } else {
          return -1
        }
      })
    },
    async addUpdateRecord() {
      // 新增
      if (!this.form.id) {
        this.form.id = ''
      }
      // 更新
      const res = await saveRecord(this.form)
      if (res.head.ret === 0) {
        this.$message.success('保存成功')
        this.handleClose('1')
      }
    }
  }

}
</script>

<style lang='scss' scoped>

</style>
