/*
 * @Author: your name
 * @Date: 2019-10-22 17:29:38
 * @LastEditTime: 2019-10-22 17:29:57
 * @LastEditors: your name
 * @Description: In User Settings Edit
 * @FilePath: \star-printer-admin\src\utils\axios.js
 */
import axios from 'axios'
import { BASE_URL, getUserAgent, TOKEN_KEY, USER_AGENT } from '@/consts/index'
import { getToken } from './auth'
import { getUrlParameter } from './common'

// 无效token
const TOKEN_INVALID = 2
// token为空
const TOKEN_NULL = 3
// token命名空间
const namespace = 'STAR_PRINTER_AUTH'

/**
 * 获取当前状态
 * @param {object} config axios 配置
 */
function _getCurrentState(config) {
  const currentState = config[namespace] || {}
  currentState.retryCount = currentState.retryCount || 0
  config[namespace] = currentState
  return currentState
}

const options = {
  baseURL: BASE_URL, // 默认请求API网关
  timeout: 10000
}
// 创建一个axios实例
const instance = axios.create(options)

/**
 * 设置请求头
 */
instance.interceptors.request.use(config => {
  const currentState = _getCurrentState(config)
  currentState.lastRequestTime = Date.now()
  config.headers['Content-Type'] = 'application/json;charset=UTF-8'
  config.headers[USER_AGENT] = getUserAgent()
  const query = getUrlParameter()
  const queryToken = query[TOKEN_KEY]
  config.headers[TOKEN_KEY] = query && queryToken ? queryToken : getToken()
  return config
})

/**
 * 处理失效的用户token，对于失效的token，需要刷新token，再发起请求
 * 重试次数3次，超过重试次数则提示用户登录会话过期，让用户重新登录
 */
instance.interceptors.response.use(async res => {
  // 只处理非登录请求的失效token的response
  if (res.data.head.ret !== TOKEN_INVALID && res.data.head.ret !== TOKEN_NULL) {
    return res
  } else {
    // 跳转去登录页面
    // @HACK
    /* eslint-disable no-underscore-dangle */
    window.g_app._store.dispatch({
      type: 'login/logout'
    })
    return res
  }
})

export default instance
