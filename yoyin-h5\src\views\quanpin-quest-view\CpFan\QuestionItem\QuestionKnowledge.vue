<template>
  <!--考点展示区-->
  <div class="ques-knowledge" v-if="showKnowledge">
    <div class="ques-knowledge-box">
      <div class="ques-knowledge-cell knowledge-border-bottom">
        <div class="knowledge-title knowledge-height-28">【考点】</div>
        <div class="knowledge-content" v-if="knowledgesData.length > 0">
          <span
            class="knowledge-item"
            v-for="(item, index) in knowledgesData"
            :key="index"
            v-html="item.name"
          >
          </span>
        </div>
        <div class="knowledge-content" v-else>
          <span class="knowledge-text">暂无考点</span>
        </div>
      </div>
      <div class="ques-knowledge-cell knowledge-border-bottom" v-if="showLogin">
        <div class="knowledge-title">【答案】</div>
        <div class="knowledge-content">
          请<span class="knowledge-login" @click="onLogin">登录</span>后查看
        </div>
      </div>
      <div class="ques-knowledge-cell" v-if="showLogin">
        <div class="knowledge-title">【解析】</div>
        <div class="knowledge-content">
          请<span class="knowledge-login" @click="onLogin">登录</span>后查看
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  export default {
    name: 'QuestionKnowledge',
    props: {
      showLogin: {
        type: Boolean,
        default: false,
      },
      showKnowledge: {
        type: Boolean,
        default: false,
      },
      knowledgesData: {
        type: Array,
        default() {
          return []
        },
      },
      showExplainCode: {
        type: Number,
        default: 0,
      },
    },
    methods: {
      onLogin() {
        this.$emit('onShowLogin')
      },
    },
  }
</script>

<style lang="scss" scoped>
  .ques-knowledge {
    .ques-knowledge-box {
      // padding:10px 0;
    }
  }
  .ques-knowledge-cell {
    display: flex;
    line-height: 20px;
    padding: 10px 0 5px 0;
    .knowledge-title {
      width: 60px;
      color: #619cf5;
    }
    .knowledge-content {
      flex: 1;
      color: #666666;
      .knowledge-text {
        padding-right: 10px;
        white-space: nowrap; /*强制span不换行*/
      }

      .knowledge-item {
        display: inline-block;
        line-height: 28px;
        padding: 0 10px;
        margin-right: 10px;
        margin-bottom: 5px;
        white-space: nowrap; /*强制span不换行*/
        background: #ebf2fb;
        border-radius: 14px;
        color: #666666;
      }

      .knowledge-login {
        cursor: pointer;
        color: #619cf5;
      }
    }
  }
  .knowledge-border-bottom {
    border-bottom: 1px dashed #bababa;
  }
  .knowledge-height-28 {
    line-height: 28px;
    font-weight: 700;
  }
</style>
