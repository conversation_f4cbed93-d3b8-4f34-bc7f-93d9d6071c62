<template>
  <div class="content">
    <div v-if="detailData" class="content-box">
      <img class="user-pic" :src="detailData.userPic" />
      <div class="user-name">{{detailData.nickName}}</div>
      <div class="createtime">{{detailData.createDate}}</div>

      <div v-if="detailData.content" class="content">
        {{detailData.content}}
      </div>
      <!-- <div v-if="audioUrl" class="rules-line">  -->
      <div v-if="audioUrl" style="min-height: 2.25rem;">
        <audio :src="audioUrl" id="player" ref="player" preload="true" @timeupdate="onPlayTimeUpdate" @play="onPlay" @pause="onPause" @canplay="ready" @ended="onEnd"></audio>
      </div>
      <div v-if="audioUrl" class="audio-box">
        <img class="play-btn" v-if="!playing" @click="play" src="./assets/img/print_ic_paly.png" />
        <img class="play-btn" v-else @click="pause" src="./assets/img/print_ic_stop.png" />
        <div class="text-progress"></div>
        <div class="progressbar">
          <div class="progress" :style="{width:progress+'%'}"></div>
        </div>
        <div class="total-seconds">{{currentTimeFmt}}</div>
      </div>
      <!-- <div v-if="audioUrl" class="want-record" @click="navDownloadApp">
        我也要录制
      </div> -->
    </div>
    <div class="logo">
      <img src="./assets/img/common_ic_logo.png" />
    </div>
  </div>
</template>

<script>
import { getNoteDetail } from './assets/js/api'
import { isAppClient, getUrlParameter, browserInfo } from '@/utils/common'
export default {
  data () {
    return {
      moduleKey: 'note-detail',
      noteid: '',
      isAppClient: isAppClient,
      detailData: null,
      audioUrl: null,
      // 该字段是音频是否处于播放状态的属性
      playing: false,
      // 音频当前播放时长
      currentTime: 0,
      // 音频最大播放时长
      maxTime: 0,
      progress: 0,
      flag: false
    }
  },
  async mounted () {
    const query = getUrlParameter()
    // window.addEventListener('contextmenu', function (e) {
    //   e.preventDefault()
    // })
    this.noteid = query.id
    this.loadData()
  },
  watch: {
    flag () {
      console.log('load set duration')
      // 每一次动态改变aduio的src后，都要load一次
      this.$refs.player.load()
      this.$refs.player.oncanplay = () => {
      this.maxTime = parseInt(this.$refs.player.duration)
      console.log(this.maxTime)
      // 获取视频音频总时长
      this.$refs.player.ondurationchange = () => {
        this.maxTime = parseInt(this.$refs.player.duration)
      }
      }
    }

  },
  computed: {
    currentTimeFmt() {
      const currentTime = this.currentTime ? this.currentTime : this.maxTime
      console.log('this.maxTime', this.maxTime)
      if (currentTime) {
        if (currentTime < 10) {
          return '00:0' + currentTime
        } else if (currentTime < 60) {
          return '00:' + currentTime
        } else {
          // 这里最多也就60秒，所以最后返回个固定值
          return '01:00'
        }
      } else {
        return '00:00'
      }
    }
  },
  methods: {
    loadData () {
      const _this = this
      getNoteDetail(this.noteid).then(res => {
        if (res.head.ret === 0) {
          _this.detailData = res.data
          if (res.data.resUrl && res.data.resUrl.pic) {
            _this.audioUrl = res.data.resUrl.pic
            console.log(_this.audioUrl)
             _this.$nextTick(() => {
               _this.$refs.player.load()
               _this.play()
               _this.pause()
              _this.flag = !_this.flag
            })
          }
        }
      })
    },

    // 控制音频的播放与暂停
    startPlayOrPause () {
      return this.playing ? this.pause() : this.play()
    },
    ready () {
      console.log('ready')
      this.maxTime = parseInt(this.$refs.player.duration)
      console.log(this.maxTime)
    },
    // 播放音频
    play () {
      this.$refs.player.play()
    },
    // 暂停音频
    pause () {
      this.$refs.player.pause()
    },
    onPlayTimeUpdate (res) {
      this.progress =
        (this.$refs.player.currentTime / this.$refs.player.duration) * 100
      if (this.progress > 100) {
        this.progress = 100
      }
      this.currentTime = parseInt(this.$refs.player.currentTime)
    },
    onPlay () {
      this.playing = true
    },
    onPause () {
      this.playing = false
    },
    onEnd () {
      this.playing = false
    },
    navDownloadApp() {
      window.location.href = 'https://a.app.qq.com/o/simple.jsp?pkgname=com.starprinter.app'
    }
  },
  components: {}
}
</script>

<style lang="scss">
$width: 100vw;

html {
  font-size: 16px;
  background: #F5F5F5;
  padding-top: 2.5rem;
  padding-bottom: 100px;
}

.content-box {
  background: white;
  border-radius: 0.5rem;
  margin-left: 0.75rem;
  margin-right: 0.75rem;
  padding-bottom: 1.25rem;
  text-align: center;

  .user-pic {
    width: 3rem;
    height: 3rem;
    margin-top: -1.5rem;
    border-radius: 50%;
  }

  .user-name {
    font-size: 1rem;
    color: black;
    margin-top: 0.55rem;
  }

  .createtime {
    font-size: 0.75rem;
    color: #8e8e93;
    width: 6rem;
    margin-top: 0.55rem;
    line-height: 1.25rem;
    margin-left: auto;
    margin-right: auto;
    text-align: center;
    vertical-align: middle;
    border-top: 0.03125rem solid #d5d4d9;
    border-bottom: 0.03125rem solid #d5d4d9;
  }

  .content {
    color: black;
    font-size: 1rem;
    line-height: 1.4375rem;
    text-align: left;
    margin-left: 1rem;
    margin-right: 1rem;
    margin-top: 1rem;
    padding-bottom: 1.75rem;
  }

  .rules-line {
    height: 1px;
    margin-left: 1rem;
    margin-right: 1rem;
    background: #d5d4d9;
  }

  .audio-box {
    // height: 4.6875rem;
    // margin-left: 0.75rem;
    // margin-right: 0.75rem;
    // line-height: 4.6875rem;

    height: 3.125rem;
    margin-left: 1.90625rem;
    margin-right: 1.90625rem;
    line-height: 3.125rem;

    background: #F5F5F5;
    border-radius: 200px;
    padding: 0 0.628rem;

    text-align: left;
    vertical-align: middle;
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    align-items: center;
    .audio-img {
      width: 1.25rem;
      height: 1.25rem;
    }

    .text-progress {
      font-size: 0.75rem;
      color: #bcbbbf;
      line-height: 0.75rem;
      letter-spacing: 1.2px;
      // margin-left: 0.75rem;
      margin-right: 0.5rem;
    }

    .progressbar {
      flex: 1;
      height: 0.1875rem;
      background: #E5E5E5;
      .progress {
        height: 100%;
        background: #0068FF;
      }
    }

    .total-seconds {
      font-size: 0.75rem;
      color: #666666;
      letter-spacing: 1.2px;
      // margin-right: 0.75rem;
      margin-left: 0.5rem;
    }

    .play-btn {
      width: 1.875rem;
      height: 1.875rem;
    }
  }

  .want-record {
    margin: 2.625rem 4.71875rem 0rem 4.71875rem;
    height: 2.375rem;
    line-height: 2.375rem;
    background: linear-gradient(90deg, #20A5FF 0%, #20A5FF 0%, #0068FF 100%);
    color: #FFFFFF;
    font-size: 1rem;
    border-radius: 100px;
  }
}

.logo {
  margin: 20px 0;
    text-align: center;
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
  img {
    width: 56px;
    height: 16px;
    // margin-right: 0.3125rem;
  }
  span {
    font-size: 0.6875rem;
    color: #333333;
    line-height: 1.225rem;
    vertical-align: middle;
  }
}
</style>
