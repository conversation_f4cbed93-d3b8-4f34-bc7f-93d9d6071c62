<template>
  <div class="content">

  </div>
</template>

<script>
import { getUserInfo } from './assets/js/api'
import { getUrlParameter } from '@/utils/common'
export default {
  data () {
    return {
      moduleKey: 'oauth-transfer'
    }
  },
  async mounted () {
    const currentUrl = window.location.href
    const spliceIndex = currentUrl.indexOf('#')
    if (spliceIndex > -1) {
      const params = getUrlParameter(currentUrl.replace('#', ''))
      const url = getUserInfo(params.access_token, params.state)
      window.location.href = url
    }
  },
  methods: {
    sharePage () {
    }
  },
  components: {}
}
</script>

<style lang="scss">
.content {
}
</style>
