<template>
  <div>
    <div class="prod-content" :key="'showFlagSSS'">
      <transition-group name="dis">
        <div class="prod-content-info" v-show="showFlag.a" :key="'showFlagA'">
          <img src="https://m.yoyin.net/h5/img/mobile/download/1.png">
          <div class="prod-content-info-title">4寸打印机</div>
          <div class="prod-content-info-subtitle">适用型号：TP4    更新时间：2021/9/9</div>
          <div class="prod-content-info-button">
            <div class="prod-content-info-button-info" @click="download('tp4-win')">
              <img src="https://m.yoyin.net/h5/img/mobile/download/drive-win.png">
              <div class="prod-content-info-button-info-text">win</div>
            </div>
<!--            <div class="prod-content-info-button-info">-->
<!--              <img src="https://m.yoyin.net/h5/img/mobile/download/drive-apple.png">-->
<!--              <div class="prod-content-info-button-info-text">mac</div>-->
<!--            </div>-->
          </div>
        </div>

        <div class="prod-content-info" v-show="showFlag.b" :key="'showFlagB'">
          <img src="https://m.yoyin.net/h5/img/mobile/download/2.png">
          <div class="prod-content-info-title">A4打印机</div>
          <div class="prod-content-info-subtitle">适用型号：TP8    更新时间：2021/9/9</div>
          <div class="prod-content-info-button">
            <div class="prod-content-info-button-info" @click="download('a4-win')">
              <img src="https://m.yoyin.net/h5/img/mobile/download/drive-win.png">
              <div class="prod-content-info-button-info-text">win</div>
            </div>
           <div class="prod-content-info-button-info" @click="download('a4-mac')">
             <img src="https://m.yoyin.net/h5/img/mobile/download/drive-apple.png">
             <div class="prod-content-info-button-info-text">mac</div>
           </div>
          </div>
        </div>
      </transition-group>
    </div>
      <transition name="dis">
        <div v-show="showFlag.foot" :key="'showFlagFoot'">
          <foot-info></foot-info>
        </div>
      </transition>
  </div>
</template>

<script>
import FootInfo from './FootInfo'
export default {
  name: 'DriveDownload',
  components: {FootInfo},
  data () {
    return {
      showFlag: {
        a: false,
        b: false,
        foot: false
      }
    }
  },
  mounted () {
    const that = this
    setTimeout(function () {
      that.showFlag.a = true
    }, 300)
    setTimeout(function () {
      that.showFlag.b = true
    }, 500)
    setTimeout(function () {
      that.showFlag.foot = true
    }, 700)
  },
  methods: {
    download (type) {
      if (type === 'tp4-win') {
        document.location.href = 'https://m.yoyin.net/h5/download/xprinter-tp4-driver.zip'
      } else if (type === 'a4-win') {
        document.location.href = 'https://m.yoyin.net/h5/download/xprinter-a4-driver.zip'
      } else if (type === 'a4-mac') {
        document.location.href = 'https://m.yoyin.net/h5/download/net.xprinter.drv_3.0.8.pkg'
      }
    }
  }
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
html, body {
  overflow-x: hidden;
  touch-action:none;
  touch-action:pan-y;
}
.prod-content {
  display: flex;
  flex-wrap: wrap;
  width: 100%;
  padding-bottom: 1.3rem;
  &-info {
    width: 27.8rem;
    height: 30.9rem;
    background: #FFFFFF;
    box-shadow: 0px 10px 13px rgba(0, 0, 0, 0.03);
    opacity: 1;
    border-radius: 10px;
    text-align: center;
    margin-bottom: 1.1rem;
    img {
      padding-top: 3.3rem;
      width: 12.8rem;
      height: 11rem;
    }
    &-title {
      padding-top: 1.8rem;
      font-size: 1.7rem;
      line-height: 2.4rem;
      font-weight: 600;
    }

    &-subtitle {
      font-size: 1.2rem;
      line-height: 1.7rem;
      color: #444444;
    }

    &-button {
      display: flex;
      justify-content: center;
      &-info {
        width: 11.2rem;
        height: 4.2rem;
        border: 1px solid #63C184;
        opacity: 1;
        border-radius: 21px;
        margin: 1.5rem 0.6rem;
        display: flex;
        justify-content: center;
        align-items: center;
        img {
          padding-top: 0;
          width: 2.6rem;
          height: 2.6rem;
        }
        &-text{
          padding-left: 1.1rem;
          color: #63C184;
          font-size: 1.5rem;
        }
      }
    }
  }

}

.dis-enter{
  opacity:0;
}
.dis-enter-active{
  transition:opacity 1.5s;
}
.dis-leave-active{
  transition:transform 1s;
}
.dis-leave-to{
  transform: translateX(0);
}
</style>
