<template>
  <div class="faq">
    <div class="faq-title" v-if="ques==1"><span>Unable to match/connect</span></div>
    <div class="faq-content" v-if="ques==1">
      <p class="first-line">Note that the Bluetooth of the mobile phone is turned on and the APP authorization is normal.</p>
      <p class="first-line">1. First you need to download the official APP "Starpany" and download the QR code on the manual.</p>
      <p class="first-line">2. Do not connect the satellite in the system settings. Please open the APP and click "Add device". If it is already paired in the system settings, please cancel the pairing first.</p>
      <p class="first-line">3. You need to turn on the Bluetooth of your phone.</p>
      <p class="first-line">4. When installing the application, all permission requests applied by the application must be allowed. For example, Xiaomi mobile phone will request location permission. Because Xiaomi mobile phone has special requirements, if it is not allowed, it is not possible to search for Bluetooth devices and use it.</p>
      <p class="first-line">5. After opening the APP, click "Add Device" to search for "starpany" option, click to start connection.</p>
      <p class="first-line">6. Please try to restart the APP and restart the pocket printer device.</p>
      <p class="first-line">7. If you still cannot connect, you can "delete the device" in the APP settings and then reconnect.</p>
      <p class="first-line">8. If you still cannot connect, please contact online customer service directly for consultation.</p>
    </div>

    <div class="faq-title" v-if="ques==2"><span>Printing is not clear</span></div>
    <div class="faq-content" v-if="ques==2">
      <p class="first-line">1. Confirm whether the machine box cover is closed.</p>
      <p class="first-line">2. Whether the coating on the paper roll is sticky, print more.</p>
      <p class="first-line">3. Please confirm whether the content you need to print is a picture or text, and then select the printing mode. Original image printing, document printing, large print, etc.</p>
    </div>

    <div class="faq-title" v-if="ques==3"><span>Cannot print photos</span></div>
    <div class="faq-content" v-if="ques==3">
      <p class="first-line">Please use original image printing or graphic editing for printing.</p>
    </div>

    <div class="faq-title" v-if="ques==4"><span>Only black and white printing</span></div>
    <div class="faq-content" v-if="ques==4">
      <p class="first-line">Because it is a thermal printer, it can only print black. But users can use different color paper rolls for printing.</p>
    </div>

    <div class="faq-title" v-if="ques==5"><span>Cannot print to self-adhesive paper</span></div>
    <div class="faq-content" v-if="ques==5">
      <p class="first-line">Stickers can be printed. Note that the installation direction of the paper rolls needs to be reversed.</p>
    </div>

    <div class="faq-title" v-if="ques==6"><span>Light color or severe fading</span></div>
    <div class="faq-content" v-if="ques==6">
      <p class="first-line">1. Please set the print density on the print preview of the APP. Charge the machine in time, the amount of power will affect the printing effect.</p>
      <p class="first-line">2. The storage temperature of the paper should be -20°~60°, avoid damp, heat and high pressure, avoid direct sunlight and heat light source, avoid scratches and severe friction.</p>
    </div>

    <div class="faq-title" v-if="ques==7"><span>APP printing is incomplete or not working</span></div>
    <div class="faq-content" v-if="ques==7">
      <p class="first-line">Please check</p>
      <p class="first-line">1. Whether the original printed content is clear.</p>
      <p class="first-line">2. Check whether the machine has low battery. </p>
      <p class="first-line">3. Check if the print head of the device is dirty.</p>
      <p class="first-line">4. Whether the printing paper is official printing paper. </p>
      <p class="first-line">5. If none of the above situations are true, please contact online customer service directly.</p>
    </div>

    <div class="faq-title" v-if="ques==8"><span>Unable to support iPhone phones</span></div>
    <div class="faq-content" v-if="ques==8">
      <p class="first-line">Support Apple mobile phones, if the user's Apple mobile phone cannot be used, please provide the mobile phone brand and model to the online customer service.</p>
    </div>

    <div class="faq-title" v-if="ques==9"><span>The effect of QR code</span></div>
    <div class="faq-content" v-if="ques==9">
      <p class="first-line">Use a small thermal printer, turn it on, double-click the button, and type a QR code to connect to the mobile phone software APP. A smart device needs to use this QR code to connect to the printer for the first time, scan the code to match the connection, and do not need to connect again for subsequent use. If you change the device to use, you need to adapt the connection according to this step.</p>
    </div>
  </div>
</template>

<script>
import { getUrlParameter } from '@/utils/common'
export default {
  data () {
    return {
      // moduleKey: 'faq',
      ques: 0
    }
  },
  async mounted () {
    // 判断url
    const query = getUrlParameter()
    this.ques = query.ques
  },
  methods: {},
  components: {}
}
</script>

<style lang="scss">
@import '../../assets/scss/faq.scss'; // 全局faq自定义的css样式
</style>
