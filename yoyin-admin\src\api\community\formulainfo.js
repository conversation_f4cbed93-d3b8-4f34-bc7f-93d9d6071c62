import request from '@/utils/request'

/** 保存公式表 */
export const saveFormulainfo = async params => {
    return request({
      url: "/platform/gam/community/v1/formulainfo/addOrUpdateFormulaInfo",
      method: 'post',
      data: params
    })
}

/** 获取公式表 */
export const fetchFormulaInfoPage = async params => {
    return request({
      url: "/platform/gam/community/v1/formulainfo/findFormulaInfoPage",
      method: 'get',
      params
    })
}

/***删除公式表 */
export const deleteFormulaInfo = async params => {
    return request({
      url: "/platform/gam/community/v1/formulainfo/deleteFormulaInfo",
      method: 'post',
      data: params
    })
}

/***保存知识点表 */
export const saveFormulaKnowladge = async params => {
    return request({
      url: "/platform/gam/community/v1/formulainfo/addOrUpdateFormulaKnowledge",
      method: 'post',
      data: params
    })
}

/** 获取知识点表 */
export const fetchFormulaKnowladgePage = async params => {
    return request({
      url: "/platform/gam/community/v1/formulainfo/findFormulaKnowldegePage",
      method: 'get',
      params
    })
}

/***删除知识点表 */
export const deleteFormulaKnowladge = async params => {
    return request({
      url: "/platform/gam/community/v1/formulainfo/deleteFormulaKnowledge",
      method: 'post',
      data: params
    })
}

/***保存科目表 */
export const saveFormulaSubject = async params => {
    return request({
      url: "/platform/gam/community/v1/formulainfo/addOrUpdateFormulaSubject",
      method: 'post',
      data: params
    })
}

/** 获取科目表 */
export const fetchFormulaSubjectPage = async params => {
    return request({
      url: "/platform/gam/community/v1/formulainfo/findFormulaSubjectPage",
      method: 'get',
      params
    })
}

/***删除科目表 */
export const deleteFormulaSubject = async params => {
    return request({
      url: "/platform/gam/community/v1/formulainfo/deleteFormulaSubject",
      method: 'post',
      data: params
    })
}

export const gradesData=[{id: "xx",name: "小学"}, {id: "cz",name: "初中"}, {id: "gz",name: "高中"}]
export const subjectsData=[{id: "Mathematics",name: "数学"}, {id: "Physics",name: "物理"}, {id: "Chemistry",name: "化学"}, {id: "Biology",name: "生物"}]