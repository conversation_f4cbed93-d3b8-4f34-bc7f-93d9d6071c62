<!--
 * @Description: In User Settings Edit
 * @Author: your name
 * @Date: 2019-10-16 11:28:20
 * @LastEditTime: 2019-10-16 11:38:48
 * @LastEditors: Please set LastEditors
 -->
<template>
  <div class="app-container">
    <div class="table-query-container">
      <el-button @click="handleAdd(null)">新增</el-button>
    </div>
    <div class="table-container">
      <el-table
        ref="multipleTable"
        :data="list"
        tooltip-effect="dark"
        style="width: 100%"
        border
        max-height="700px"
      >
        <el-table-column label="排序" prop="sortNum" header-align="center" align="center" />
        <el-table-column label="名称" prop="name" header-align="center" align="center" />
        <el-table-column label="编号" prop="code" header-align="center" align="center" />
        <el-table-column label="是否展示new" prop="newFlag" header-align="center" align="center" :formatter="newFlagFormatter" />
        <el-table-column label="所属打印机" prop="printerTypes" header-align="center" align="center" />
        <el-table-column label="操作" width="245" header-align="center" align="center">
          <template slot-scope="scope">
            <el-button size="mini" type="error" @click="handleEdit(scope.row)">编辑</el-button>
            <el-button size="mini" type="error" @click="deleteFunctionSetting(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <!-- <div class="pagination-container" style="margin-top:20px;">
      <el-pagination
        :current-page="params.pageno"
        :page-sizes="[10, 20, 30, 40]"
        :page-size="params.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div> -->
    <el-dialog
      :visible.sync="showDialog"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      width="60%"
      title="编辑"
    >
      <el-form ref="form" :model="form" label-width="100px" class="edit-form">
        <el-form-item label="名称" prop="name">
          <el-input v-model="form.name" />
        </el-form-item>
        <el-form-item label="编号" prop="code">
          <el-input v-model="form.code" />
        </el-form-item>
        <!-- <el-form-item label="标签" prop="label">
          <el-input v-model="form.label" />
        </el-form-item> -->
        <el-form-item label="是否展示new" prop="newFlag">
          <el-radio-group v-model="form.newFlag">
            <el-radio-button label="0">不展示</el-radio-button>
            <el-radio-button label="1">展示</el-radio-button>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="所属打印机" prop="printerTypes">
          <!-- <el-input v-model="form.printerTypes" /> -->
          <el-checkbox-group v-model="printerTypeLabel">
            <el-checkbox-button v-for="item in printerTypeConstantList" :key="item.value" :label="item.value">{{ item.name }}</el-checkbox-button>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="排序" prop="sortNum">
          <el-input
            v-model="form.sortNum"
            type="text"
          />
        </el-form-item>
        <el-form-item>
          <el-button @click="handleClose">返回</el-button>
          <el-button type="primary" @click="submit">保存</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>
<script>
import { fetchList, deleteRecord, saveRecord } from '@/api/community/functionsetting'
import { printerTypeConstantList } from '@/consts/index'
import AliyunOSS from '@/utils/aliyunOSS'
const oss = new AliyunOSS()
export default {
  data() {
    return {
      // 查询参数
      params: {
        pageNo: 1,
        pageSize: 10,
        type: ''
      },
      printerTypeLabel: [],
      distinctTypeList: [],
      cacheKey: '',
      /** 列表 */
      list: [],
      /** 总条数 */
      total: 0,
      downloadUrl: '',
      /** 时间段 */
      timeRange: '',
      timeRange2: '',
      keyword: '',
      form: {
        name: '',
        type: '1',
        code: '',
        value: '',
        sortNum: 0,
        newFlag: '0',
        printerTypes: ''
      },
      printerTypeConstantList: printerTypeConstantList,
      /** 时间格式 */
      timeFormat: 'yyyy-MM-dd',
      showDialog: false,
      testUrl: '',
      /** 待上传的文件 */
      upFile: undefined
    }
  },
  watch: {
    // formData: function(val) {
    //   this.upFile = undefined
    //   this.form = { ...val }
    //   this.form.newFlag = this.form.newFlag + ''
    // },
    printerTypeLabel: function(val) {
      if (val && val.length > 0) {
        this.form.printerTypes = val.join(';') + ';'
      }
    }
  },
  mounted() {
    // this.getTypeList()
    this.getList()
  },
  methods: {
    newFlagFormatter(row) {
      if (row.newFlag === undefined || row.printerType === null || row.newFlag === 0 || row.newFlag === '0') {
        return '否'
      } else {
        return '是'
      }
    },
    // async getTypeList() {
    //   const res = await fetchDistinctTypeList()
    //   if (res.head.ret === 0) {
    //     this.distinctTypeList = res.data
    //   }
    // },
    async getList() {
      const res = await fetchList(this.params)
      if (res.head.ret === 0) {
        this.list = res.data
        // this.list = res.data.result
        // this.total = res.data.totalCount
      }
    },
    /** 切换每页条数 */
    handleSizeChange(val) {
      this.params.pageSize = val
      this.getList()
    },
    /** 切换当前页 */
    handleCurrentChange(val) {
      this.params.pageNo = val
      this.getList()
    },
    /** 查询 */
    handleQuery() {
      this.params.pageNo = 1
      this.params.pageSize = 10
      this.getList()
    },
    /** 查看动态 */
    handleEdit(row) {
      this.form = row
      this.showDialog = true
      try {
        if (this.form.printerTypes !== '') {
          this.printerTypeLabel = this.form.printerTypes.split(';').filter(d => d)
        }
      } catch (error) {
        console.log(error)
      }
    },
    deleteFunctionSetting(row) {
      const parms = {
        id: row.id
      }
      this.$confirm('此操作将永久删除，不可恢复, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteRecord(parms).then(res => {
          if (res.head.ret === 0) {
            this.$message.success('删除成功')
            this.getList()
            // this.getTypeList()
          }
        })
      }).catch(() => {
      })
    },
    /** 提交 */
    submit() {
      if (this.upFile) {
        oss.uploadFile(this.upFile, (result, error) => {
          if (error) {
            this.$message.error('上传文件失败')
            return -1
          }
          if (result) {
            console.log('上传完图片后返回的数据', result)
            this.form.icon = result.name
            this.handleSave(this.form)
          }
        })
      } else {
        // 直接保存
        this.handleSave(this.form)
      }
    },
    handleSave(parms) {
      saveRecord(parms).then(res => {
        if (res.head.ret === 0) {
          this.$message.success('保存成功')
          this.getList()
          // this.getTypeList()
        }
        this.showDialog = false
      })
    },
    handleAdd(row) {
      if (row) {
        this.form = {
          type: row.type,
          name: row.name,
          label: '',
          value: '',
          localeCode: '',
          icon: '',
          sortNum: 0
        }
      } else {
        this.form = {
          name: '',
          type: '',
          label: '',
          value: '',
          localeCode: '',
          icon: '',
          sortNum: 0
        }
      }
      this.showDialog = true
    },
    /** 选择图片*/
    changePic(file) {
      this.upFile = file.raw
      this.form.icon = URL.createObjectURL(file.raw)
      this.testUrl = URL.createObjectURL(file.raw)
      return false
    },
    /** 上传前 */
    beforeUpload(file) {
      return false
    },
    /** 返回 */
    handleClose(code) {
      this.showDialog = false
    }
  }
}
</script>

<style lang="scss" scoped>

</style>
