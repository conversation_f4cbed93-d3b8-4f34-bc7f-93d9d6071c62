import request from '@/utils/request'

/**
 * 查询列表数据
 * @param params 参数
 */
export const fetchListByPage = async params => {
  return request({
    url: '/platform/authority/auth/v1/role/findpage',
    method: 'get',
    params
  })
}

export const fetchRoleAllList = async params => {
  return request({
    url: '/platform/authority/auth/v1/role/findAllList',
    method: 'get',
    params
  })
}

/**
 * 保存记录
 * @param ids 记录id
 */
export const saveRecord = async params => {
  return request({
    url: '/platform/authority/auth/v1/role/saveorupdate',
    method: 'post',
    data: params
  })
}

export const deleteRecord = async params => {
  return request({
    url: '/platform/authority/auth/v1/role/del',
    method: 'post',
    data: params
  })
}

export const findResourcesListByRoleId = async params => {
  return request({
    url: '/platform/authority/auth/v1/role/findResourcesTreeByRoleId',
    method: 'get',
    params
  })
}

export const disResources = async params => {
  return request({
    url: '/platform/authority/auth/v1/role/disresource',
    method: 'post',
    data: params
  })
}

