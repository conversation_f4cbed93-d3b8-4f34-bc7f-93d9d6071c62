<script>
import { isAppClient } from '@/utils/common'
export default {
  props: {
    openUrl: {
      default: 'https://applink.sythealth.com'
    }
  },
  data() {
    return {
      isAppClient
    }
  }
}
</script>
<template>
  <div class="fixed-bot" v-if="!isAppClient">
    <img class="bg" src="../../assets/img/ip_bar_share.png" alt="打开轻加App查看">
    <div class="box">
      <img class="bg" src="../../assets/img/ip_bar_share.png" alt="打开轻加App查看">
      <div class="content">
        <div class="center">
          <a :href="openUrl" class="btn">
            <span>打开轻加查看更多</span>
            <i class="iconfont">&#xe604;</i>
          </a>
        </div>
      </div>
    </div>
  </div>
</template>
<style lang="scss">
@import '../../assets/scss/iconfont.css';

.fixed-bot {
  &>img {
    width: 100%;
    visibility: hidden;
  }
  .box {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    transition: bottom .6s;
    font-size: 1em;
    z-index: 999;
    box-sizing: border-box;
  }
  .bg {
    width: 100%;
    height: auto;
    margin-bottom: -5px;
  }
  .content {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    text-align: center;
    vertical-align: middle;
    .center {
      display: inline-block;
      vertical-align: middle;
      font-size: 1.1em;
      padding-top: 4%;
      a {
        color: #fff;
        display: inline-block;
        .iconfont {
          padding-left: .2em;
          font-size: 1.3em;
          color: #fff;
        }
      }
    }
    &:before {
      display: inline-block;
      content: '';
      height: 100%;
      width: 0;
      vertical-align: middle;
    }
  }
}
</style>
