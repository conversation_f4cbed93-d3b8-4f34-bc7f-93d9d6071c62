## 基于vue-cli 脚手架

- vue2.3.3 + webpack2 + es6 + vuex2.3.1 + scss
- 支持多页面router
- 支持国际化
- 使用axios异步方案
- js文件import/require scss可以autoprefixer

## 开发说明
该项目为轻加活动，使用vue开发各类活动需求，页面放在views下，以jira的需求名字定义[QAPP-xxx].html，
方便以后反查需求记录

## 布局说明

``` 布局样式说明
# 通用设置最大宽度 1200

# 在不同大小的屏幕下，字体大小也不同分别是
# 320 = 12
# 400 = 14
# 750 = 16
# 1200 = 12

# 在响应式的内容时，pc端用screen-pc，移动端用screen-phone进行显示

```

## 手动部署说明
运行编译命令`npm run build`
将`dist`下的所有内容上传至oss的html下`/activity/V6`文件夹，压缩的zip文件目录结构如下
````
|-- activity/V6
|----static
|----views
|-------QAPP-1347.html
|----vender.md5.css
````

## 自动部署说明
运行编译命令`npm run build`
> 如果未全局安装`gulp`，需要运行命令`cnpm install -g gulp`
打包并上传代码到local&test
````bash
gulp
````

上传代码到正式oss
````bash
gulp upload:oss
````



## v3.1.0 版本vw布局若缺少依赖 安装
cnpm i postcss-aspect-ratio-mini postcss-px-to-viewport postcss-write-svg postcss-cssnext postcss-viewport-units cssnano --S
cnpm i postcss-import postcss-url autoprefixer  --S
