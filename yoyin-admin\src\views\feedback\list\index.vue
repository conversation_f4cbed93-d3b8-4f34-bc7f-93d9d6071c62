<!--
 * @Description: In User Settings Edit
 * @Author: your name
 * @Date: 2019-10-16 11:28:20
 * @LastEditTime: 2019-10-16 11:38:48
 * @LastEditors: Please set LastEditors
 -->
<template>
  <div class="app-container">
    <div class="table-query-container">
      <span class="demonstration">反馈时间:</span>
      <el-date-picker
        v-model="timeRange"
        :value-format="timeFormat"
        :format="timeFormat"
        :unlink-panels="true"
        type="daterange"
        :clearable="false"
        range-separator=" 至 "
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        @change="changeTimeRange"
      />
      <el-button type="primary" @click="handleQuery">查询</el-button>
    </div>
    <div class="table-container">
      <el-table
        ref="multipleTable"
        :data="list"
        tooltip-effect="dark"
        style="width: 100%"
        border
        max-height="700px"
      >
        <!-- <el-table-column prop="userId" label="用户Id" width="210" /> -->
        <el-table-column prop="userNo" label="用户Id" width="100" />
        <el-table-column prop="mobile" width="150" label="手机号" />
        <el-table-column prop="clientInfo" width="250" label="手机信息" />
        <el-table-column prop="printerType" width="120" label="打印机类型" />
        <el-table-column prop="type" :formatter="typeFormatter" width="80" label="反馈类型" />
        <el-table-column prop="qtype" :formatter="qtypeFormatter" width="200" label="问题类型" />
        <el-table-column prop="updateTime" label="反馈时间" width="150" />
        <el-table-column prop="content" width="400" label="问题描述" />
        <el-table-column width="400" label="答复">
          <template slot-scope="scope">
            <div style="white-space: pre-wrap;" v-html="scope.row.result" />
          </template>
        </el-table-column>
        <el-table-column width="120" fixed="right" label="问题截图">
          <template slot-scope="scope">
            <el-button v-if="scope.row.images.length > 0" type="primary" size="mini" @click="handlePic(scope.row.images)">点击查看</el-button>
            <span v-else>无附件</span>
          </template>
        </el-table-column>
        <el-table-column width="120" fixed="right" label="错误信息">
          <template slot-scope="scope">
            <el-button v-if="scope.row.errormsg.length > 0" type="primary" size="mini" @click="handleError(scope.row.errormsg)">查看</el-button>
            <span v-else>无</span>
          </template>
        </el-table-column>
        <el-table-column width="180" fixed="right" label="操作">
          <template slot-scope="scope">
            <el-button v-show="scope.row.result.length === 0" v-permission="'btn-menuUsermanage-feedback-edit'" type="primary" size="mini" @click="handleReply(scope.row)">回复</el-button>
            <el-button v-show="scope.row.result.length !== 0" v-permission="'btn-menuUsermanage-feedback-edit'" type="success" size="mini" @click="handleReplyMore(scope.row)">追加</el-button>
            <!-- <el-button type="primary" size="mini" @click="handleDelete(scope.row.id)">删除</el-button> -->
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="pagination-container" style="margin-top:20px;">
      <el-pagination
        :current-page="params.pageno"
        :page-sizes="[10, 20, 30, 40]"
        :page-size="params.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <el-dialog title="回复" :visible.sync="showEdit">
      <el-input
        v-model="remark"
        type="textarea"
        :rows="5"
        maxlength="200"
        placeholder="请输入回复内容"
      />
      <div slot="footer" class="dialog-footer">
        <el-button @click="showEdit = false">取 消</el-button>
        <el-button v-permission="'btn-menuUsermanage-feedback-edit'" type="primary" @click="handleOk">确 定</el-button>
      </div>
    </el-dialog>

    <el-dialog title="追加" :visible.sync="showEditMore">
      <el-input
        v-model="remarkMore"
        type="textarea"
        :rows="5"
        maxlength="200"
        placeholder="请输入回复内容"
      />
      <div slot="footer" class="dialog-footer">
        <el-button @click="showEditMore = false">取 消</el-button>
        <el-button v-permission="'btn-menuUsermanage-feedback-edit'" type="primary" @click="handleOkMore">确 定</el-button>
      </div>
    </el-dialog>

    <!-- 截图  -->
    <el-dialog title="查看附件" :visible.sync="showPic" width="30%">
      <div style="background: #F2F2F2; padding:10px;">
        <img v-for="(item, index) in pics" :key="index" width="100%" :src="item" alt="" style="margin-bottom: 20px;">

      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="showPic = false">返回</el-button>
      </div>
    </el-dialog>
    <!-- 错误信息 -->
    <el-dialog title="查看错误信息" :visible.sync="showError" width="80%">
      <pre><div style="background: #F2F2F2; padding:10px;">
        {{ errormsg }}
      </div>
      </pre>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="showError = false">返回</el-button>
      </div>
    </el-dialog>

  </div>
</template>
<script>
import { fetchListByPage, deleteRecord, saveRecord } from '@/api/feedback/list'
import { parseTime } from '@/utils/index'
// const softwareMap = {
//   'crash': '闪退 ',
//   'download': '下载',
//   'other': '其它',
//   'notresizetext': '不能调整文字大小',
//   'pagebeautifly': '页面美化',
//   'featureoptimization': '新功能优化'
// }
// const hardwareMap = {
//   'printjam': '打印卡纸',
//   'cannottake': '无法取纸 ',
//   'notclear': '打印不清晰',
//   'cannotconnect': '无法连接',
//   'other': '其他 '
// }
const problemMap = {
  'crash': '闪退 ',
  'download': '下载',
  'other': '其它',
  'notresizetext': '不能调整文字大小',
  'pagebeautifly': '页面美化',
  'featureoptimization': '新功能优化',
  'printjam': '打印卡纸',
  'cannottake': '无法取纸 ',
  'notclear': '打印不清晰',
  'cannotconnect': '无法连接'
}

export default {

  data() {
    return {
      // 查询参数
      params: {
        startDate: '',
        endDate: '',
        pageNo: 1,
        pageSize: 10
        // sort: 'desc',
        // sortType: 'time'
      },
      /** 列表 */
      list: [],
      /** 总条数 */
      total: 0,

      /** 时间段 */
      timeRange: '',
      /** 时间格式 */
      timeFormat: 'yyyy-MM-dd',
      // 处理内容
      remark: '',
      // 追加内容
      remarkMore: '',
      showEdit: false,
      showEditMore: false,
      // 当前ID
      currentId: -1,
      // 显示截图
      showPic: false,
      // 问题图片
      pics: [],
      // 显示错误信息
      showError: false,
      errormsg: '',
      tttttt: 'tttttt'

    }
  },
  mounted() {
    this.getList()
  },
  methods: {
    /** 获取列表 */
    async getList() {
      const res = await fetchListByPage(this.params)
      if (res.head.ret === 0) {
        this.list = res.data.result
        this.total = res.data.totalCount
      }
      console.log(this.list)
    },
    /** 切换每页条数 */
    handleSizeChange(val) {
      this.params.pageSize = val
      this.getList()
    },
    /** 切换当前页 */
    handleCurrentChange(val) {
      this.params.pageNo = val
      this.getList()
    },

    /** 选择时间段 */
    changeTimeRange(val) {
      this.params.startDate = val[0]
      this.params.endDate = val[1]
    },
    /** 查询 */
    handleQuery() {
      this.getList()
    },
    /** 删除 */
    async handleDelete(id) {
      const res = await deleteRecord({ id })
      if (res.head.ret === 0) {
        this.$message.success('保存成功')
      } else {
        this.$message.error('保存失败')
      }
      this.getList()
    },
    /** 回复 */
    async handleReply(row) {
      console.log('handleReply: ', row)
      this.currentId = row.id
      this.remark = row.result
      this.showEdit = true
    },
    /** 回复 */
    async handleReplyMore(row) {
      console.log('handleReply: ', row)
      this.currentId = row.id
      this.remark = row.result
      this.remarkMore = ''
      this.showEditMore = true
    },
    // 反馈类型
    typeFormatter(row, column) {
      const type = row.type.trim()
      if (type === 'software') {
        return '软件'
      } else if (type === 'hardware') {
        return '硬件'
      } else {
        return '未知'
      }
    },
    // 问题类型
    qtypeFormatter(row, column) {
      const qtype = row.qtype
      // const type = row.type
      const list = qtype.split('/')
      let res = ''
      list.map(q => {
        // console.log(q, problemMap[q.trim()])
        res += (problemMap[q.trim()] != null ? problemMap[q.trim()] : '未知') + ' '
        console.log('res=', res)
      })
      // if (type === 'software') {
      //   list.map(q => {
      //     res = res + ' ' + softwareMap[q.trim()]
      //   })
      // } else if (type === 'hardware') {
      //   list.map(q => {
      //     res = res + hardwareMap[q.trim()]
      //   })
      // } else {
      //   list.map(q => {
      //     res = res + ' ' + problemMap[q.trim()]
      //   })
      //   // res = '未知'
      // }
      return res
    },
    /** 确定处理 */
    async handleOk() {
      if (!this.remark) {
        this.$message.warning('回复内容不能为空')
        return
      }
      this.showEdit = false
      const curTime = new Date()
      const res = await saveRecord({
        id: this.currentId,
        remark: this.remark + '\n' + parseTime(curTime, '{y}-{m}-{d} {h}:{i}:{s}')
      })
      if (res.head.ret === 0) {
        this.$message.success('保存成功')
      } else {
        this.$message.error('保存失败')
      }
      this.getList()
    },
    /** 确定处理 */
    async handleOkMore() {
      if (!this.remarkMore) {
        this.$message.warning('回复内容不能为空')
        return
      }
      this.showEditMore = false
      const curTime = new Date()
      const res = await saveRecord({
        id: this.currentId,
        remark: this.remark + '\n\n' + this.remarkMore + '\n' + parseTime(curTime, '{y}-{m}-{d} {h}:{i}:{s}')
      })
      if (res.head.ret === 0) {
        this.$message.success('保存成功')
      } else {
        this.$message.error('保存失败')
      }
      this.getList()
    },
    /** 查看截图 */
    handlePic(pics) {
      this.showPic = true
      this.pics = pics
    },
    /**
     * 显示错误信息
     */
    handleError(errormsg) {
      console.log('errormsg: ', errormsg)
      this.errormsg = errormsg
      this.showError = true
    }

  }
}
</script>

<style lang="scss" scoped>
.tttttt {
   white-space: pre-wrap!important;
 }
</style>
