<template>
  <div class="faq">
    <div class="faq-title"><span>指示灯小贴士</span></div>
    <div class="faq-content">
      <p class="first-line">1.开机时：</p>
      <p class="first-line">①开关键指示灯常亮--正常使用状态；</p>
      <p class="first-line">②开关键指示灯快闪--缺纸、开盖、过热、电量过低；</p>
      <p class="first-line">③开关键指示灯慢闪--需关机充电。</p>
      <p class="first-line" style="margin-top: 10px;">2.关机时：</p>
      <p class="first-line">开关键指示灯慢闪--充电中，充满后灯灭。</p>
    </div>

  </div>
</template>

<script>
export default {
  data () {
    return {
       moduleKey: 'faq'
    }
  },
  async mounted () { },
  methods: {},
  components: {}
}
</script>

<style lang="scss">

</style>
