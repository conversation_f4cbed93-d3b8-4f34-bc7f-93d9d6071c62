<!--
 * @Description: In User Settings Edit
 * @Author: your name
 * @Date: 2019-10-16 11:28:20
 * @LastEditTime: 2019-10-22 17:03:31
 * @LastEditors: Please set LastEditors
 -->
<template>
  <div class="app-container">
    <div class="control-container">
      <!-- <el-button type="primary" @click="queryVersion">查询</el-button> -->
      <el-button v-permission="'btn-menuSystemsetting-installpackage-edit'" @click="addVersion">添加</el-button>
    </div>
    <div v-show="false" class="choose-container">
      <span>
        已选中
        <span>{{ multipleSelection.length }}</span> 项
      </span>
      <!-- <a @click="clearSelection">清空</a>
      <el-button type="primary" size="small" :disabled="multipleSelection.length == 0" style="margin-left: 20px;" @click="handleMultiModify">批量修改</el-button>
      <el-button type="primary" size="small" :disabled="multipleSelection.length == 0" style="margin-left: 20px;" @click="handleMultiDel">批量删除</el-button> -->
    </div>
    <div class="table-container">
      <el-table
        ref="multipleTable"
        :data="tableList"
        tooltip-effect="dark"
        style="width: 100%"
        border
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column label="发布渠道" width="120">
          <template slot-scope="scope">{{ scope.row.channel }}</template>
        </el-table-column>
        <el-table-column prop="version" label="版本号" width="120" />
        <el-table-column prop="needForceUpdate" label="强制升级" width="120">
          <template slot-scope="scope">{{ scope.row.needForceUpdate==0?"否":"是" }}</template>
        </el-table-column> />
        <el-table-column prop="url" label="下载地址" show-overflow-tooltip />
        <el-table-column label="弹窗标题" width="120">
          <template slot-scope="scope">{{ scope.row.title }}</template>
        </el-table-column>
        <el-table-column prop="remark" label="版本说明" width="500">
          <template slot-scope="scope"><pre> {{ scope.row.remark }}</pre></template>
        </el-table-column> />
        <el-table-column label="操作" fixed="right">
          <template slot-scope="scope">
            <el-button size="mini" @click="handleEdit(scope.$index, scope.row)">编辑</el-button>
            <el-button v-permission="'btn-menuSystemsetting-installpackage-edit'" size="mini" @click="handleOnlyFile(scope.$index, scope.row)">仅上传文件</el-button>
            <el-button
              v-permission="'btn-menuSystemsetting-installpackage-edit'"
              size="mini"
              type="danger"
              @click="handleDeleteRow(scope.$index, scope.row)"
            >删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="pagination-container" style="margin-top:20px;">
      <el-pagination
        :current-page="currentPage"
        :page-sizes="[10, 20, 30, 40]"
        :page-size="currentSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
    <!-- 弹框 -->
    <div class="dialog-container">
      <el-dialog
        :title="form.id === '' ? '新增标签' : '编辑标签' "
        :visible.sync="showDialog"
        width="50%"
        center
      >
        <el-form
          ref="ruleForm"
          :model="form"
          :rules="rules"
          label-width="100px"
          class="demo-ruleForm"
        >
          <el-form-item v-if="!onlyUpload" label="发布渠道" prop="channel">
            <el-input v-model="form.channel" />
          </el-form-item>
          <el-form-item v-if="!onlyUpload" label="版本号" prop="version">
            <el-input v-model="form.version" />
          </el-form-item>
          <el-form-item label="是否强制升级">
            <el-radio-group v-model="form.needForceUpdate">
              <el-radio-button label="0">否</el-radio-button>
              <el-radio-button label="1">是</el-radio-button>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="是否首页展示">
            <el-radio-group v-model="form.needIndexShow">
              <el-radio-button label="0">否</el-radio-button>
              <el-radio-button label="1">是</el-radio-button>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="弹窗标题" prop="title">
            <el-input v-model="form.title" />
          </el-form-item>
          <el-form-item v-if="!onlyUpload" label="版本说明">
            <el-input
              v-model="form.remark"
              type="textarea"
              :autosize="{ minRows: 2, maxRows: 20}"
              placeholder="请输入内容"
            />
          </el-form-item>
          <el-form-item v-show="showUrl" label="下载地址">
            <a :href="form.url" style=" color: blue;" class="link">{{ form.url }}</a>
          </el-form-item>
          <el-form-item label="更换文件">
            <el-upload
              ref="upload"
              class="upload-demo"
              action
              accept=".apk, .bin, .new"
              :auto-upload="false"
              :on-remove="handleRemove"
              :file-list="fileList"
              :on-change="changeFile"
              :before-upload="beforeUpload"
            >
              <el-button slot="trigger" size="small" type="primary">选取文件</el-button>
            </el-upload>
          </el-form-item>
          <el-form-item>
            <el-button @click="showDialog = false">返回</el-button>
            <el-button v-permission="'btn-menuSystemsetting-installpackage-edit'" type="primary" :loading="loading" @click="submitForm">保存</el-button>
          </el-form-item>
        </el-form>
      </el-dialog>
    </div>
    <!-- 批量修改弹框 -->
    <div class="dialog-container">
      <el-dialog
        title="编辑标签"
        :visible.sync="showMuliDialog"
        width="50%"
        center
      >
        <el-form
          ref="ruleMultiForm"
          :model="multiForm"
          label-width="100px"
          class="demo-ruleForm"
          :rules="multiFormRules"
        >
          <el-form-item label="版本号" prop="version">
            <el-input v-model="multiForm.version" />
          </el-form-item>
          <el-form-item label="版本说明">
            <el-input
              v-model="multiForm.remark"
              type="textarea"
              :autosize="{ minRows: 2, maxRows: 20}"
              placeholder="请输入内容"
            />
          </el-form-item>
          <el-form-item>
            <el-button @click="showMuliDialog = false">返回</el-button>
            <el-button v-permission="'btn-menuSystemsetting-installpackage-edit'" type="primary" :loading="loading" @click="submitMultiForm">保存</el-button>
          </el-form-item>
        </el-form>
      </el-dialog>
    </div>
  </div>
</template>
<script>
import { fetchListByPage, deleteRecord, saveRecord } from '@/api/version/list'
import AliyunOSS from '@/utils/aliyunOSS'
const oss = new AliyunOSS()
export default {
  data() {
    return {
      currentPage: 1,
      currentSize: 10,
      total: 100,
      tableList: [],
      multipleSelection: [],
      // 弹框
      showDialog: false,
      // 批量
      showMuliDialog: false,
      // 仅上传文件模式
      onlyUpload: false,
      // 表单
      form: {
        id: '',
        channel: '',
        url: '',
        param: '',
        version: '',
        remark: '',
        type: 'app'
      },
      // 表单
      multiForm: {
        version: '',
        remark: ''
      },
      // 检验规则
      rules: {
        channel: [
          { required: true, message: '请输入渠道', trigger: 'blur' },
          { min: 3, max: 20, message: '长度在 3 到 20 个字符', trigger: 'blur' }
        ],
        url: [{ required: true, message: '请选择文件', trigger: 'blur' }],
        version: [
          { required: true, message: '请输入版本号如1.1.2', trigger: 'blur' }
        ]
      },
      multiFormRules: {
        version: [
          { required: true, message: '请输入版本号如1.1.2', trigger: 'blur' }
        ]
      },
      fileList: [],
      file: undefined,
      loading: false,
      showUrl: false
    }
  },

  computed: {},
  mounted() {
    this.getList()
  },
  methods: {
    /** 添加 */
    addVersion() {
      this.form = {}
      this.showDialog = true
      this.fileList = []
      this.file = undefined
      this.showUrl = false
      this.onlyUpload = false
    },
    /** 查询 */
    queryVersion() {
      this.getList()
    },
    /** 切换每页条数 */
    handleSizeChange(val) {
      console.log(`每页 ${val} 条`)
      this.currentSize = val
      this.getList()
    },
    /** 切换当前页 */
    handleCurrentChange(val) {
      console.log(`当前页: ${val}`)
      this.currentPage = val
      this.getList()
    },
    /** 获取表数据 */
    async getList() {
      const params = {
        pageno: this.currentPage,
        pagesize: this.currentSize,
        type: 'app'
      }
      const res = await fetchListByPage(params)
      if (res.head.ret === 0) {
        const data = res.data
        this.total = data.totalCount
        this.tableList = data.result
      }
    },
    clearSelection() {
      this.$refs.multipleTable.clearSelection()
    },
    /** 勾选事件 */
    handleSelectionChange(val) {
      this.multipleSelection = val
      console.log('handleSelectionChange: ' + this.multipleSelection)
    },
    /** 编辑 */
    handleEdit(index, row) {
      this.form = row
      this.showDialog = true
      this.fileList = []
      this.file = undefined
      this.showUrl = true
      this.onlyUpload = false
    },
    /** 编辑 仅上传文件 */
    handleOnlyFile(index, row) {
      this.form = row
      this.showDialog = true
      this.fileList = []
      this.file = undefined
      this.showUrl = true
      this.onlyUpload = true
    },

    /** 删除单行 */
    async handleDeleteRow(index, row) {
      const params = {
        id: row.id
      }
      this.$confirm('此操作将永久删除该记录, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          deleteRecord(params).then(res => {
            if (res.head.ret === 0) {
              this.$message({
                type: 'success',
                message: '删除成功!'
              })
              this.getList()
            }
          })
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    },
    /** 删除单行 */
    async handleMultiDel() {
      this.$confirm('此操作将永久删除记录, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          let i = 0
          const len = this.multipleSelection.length
          for (i = 0; i < len; i++) {
            const params = {
              id: this.multipleSelection[i].id
            }
            deleteRecord(params).then(res => {
              if (res.head.ret === 0) {
                this.$message({
                  type: 'success',
                  message: '删除成功!'
                })
                this.getList()
              }
            })
          }
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    },

    // 表单编辑
    /** 保存 */
    submitForm() {
      this.$refs['ruleForm'].validate(valid => {
        if (valid) {
          this.loading = true
          console.log(this.file)
          if (!this.file) {
            this.saveConfig()
            return
          }
          oss.uploadFile(this.file, (result, error) => {
            if (error) {
              console.log('error')
              console.log(error)
              this.$message.error('上传文件失败')
              this.loading = false
              return
            }
            if (result) {
              this.form.url = result.url
              this.saveConfig()
              this.getList()
            }
          })
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    async saveConfig() {
      console.log('this.form')
      console.log(this.form)
      this.form['type'] = 'app'
      const res = await saveRecord(this.form)
      if (res.head.ret === 0) {
        this.$message.success('保存成功')
      }
      this.loading = false
      this.showDialog = false
      this.onlyUpload = false
      this.getList()
    },
    /** 点击批量修改 */
    handleMultiModify() {
      this.showMuliDialog = true
    },
    /** 批量保存 */
    submitMultiForm() {
      this.$refs['ruleMultiForm'].validate(valid => {
        if (valid) {
          this.loading = true
          console.log('this.multiForm: ', this.multiForm)
          this.doMultiSave()
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    async doMultiSave() {
      let i = 0
      const len = this.multipleSelection.length
      let count = 0
      for (i = 0; i < len; i++) {
        const data = { ...this.multipleSelection[i] }
        data.version = this.multiForm.version
        data.remark = this.multiForm.remark
        const res = await saveRecord(data)
        if (res.head.ret === 0) {
          count++
        }
      }
      this.$message.success('保存成功[' + count + ']条, 共' + len + '条')
      this.loading = false
      this.showMuliDialog = false
      this.getList()
    },

    /** 移除之前 */
    handleRemove(file, fileList) {
      console.log('handleRemove')
      this.showUrl = true
    },
    /** 上传前 */
    beforeUpload(file) {
      return false
    },
    changeFile(file) {
      console.log('changeFile')
      console.log(file)
      this.file = file.raw
      this.fileList.pop()
      this.fileList.push({
        name: file.name,
        url: file.name
      })
      this.showUrl = false
    }
  }
}
</script>

<style lang="scss" scoped>
.app-container {
  margin: 20px;
  .control-container {
    margin-bottom: 20px;
  }
  .choose-container {
    margin-bottom: 20px;
    background-color: #e6f7ff;
    border: 1px solid #91d5ff;

    span {
      margin-left: 20px;
      line-height: 48px;
    }
    a {
      margin-left: 20px;
      color: #1890ff;
      text-decoration: none;
      background-color: transparent;
      outline: none;
      cursor: pointer;
      transition: color 0.3s;
    }
  }
}
</style>
