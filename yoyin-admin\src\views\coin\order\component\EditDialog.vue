<template>
  <div v-if="visiable" class="edit-dialog">
    <el-dialog
      ref="dialog"
      :show-close="false"
      :title="form.id === '' ? '新增' : '编辑' "
      :visible.sync="visiable"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      width="80%"
      center
    >
      <el-form ref="form" :model="form" label-width="100px" class="edit-form">
        <el-form-item label="状态" prop="deliveryStatus">
          <el-radio-group v-model="form.deliveryStatus">
            <el-radio-button label="未发货">未发货</el-radio-button>
            <el-radio-button label="已发货">已发货</el-radio-button>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if="form.deliveryStatus=='已发货'" label="快递公司" prop="deliveryCompany">
          <el-input v-model="form.deliveryCompany" />
        </el-form-item>
        <el-form-item v-if="form.deliveryStatus=='已发货'" label="快递编号" prop="deliveryCode">
          <el-input v-model="form.deliveryCode" />
        </el-form-item>
        <el-form-item>
          <el-button @click="handleClose">返回</el-button>
          <el-button v-permission="'btn-menuCoin-order-edit'" type="primary" @click="submit">保存</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import { saveOrdersRecord } from '@/api/coin/order'

export default {
  name: 'EditDialog',
  props: {
    visiable: {
      type: Boolean,
      required: true,
      default: false
    },
    formData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      /** 表单 */
      form: {
      },
      /** 待上传的文件 */
      upFile: undefined,
      testUrl: '',
      /** 时间格式 */
      timeFormat: 'yyyy-MM-dd HH:mm:ss',
      fileList: [],
      preUploadList: [],
      showDialog: false,
      subjectList: [],
      checkboxGroup1: []
    }
  },
  computed: {
  },
  watch: {
    formData: function(val) {
      this.form = { ...val }
    }
  },
  mounted() {
    this.show = this.visiable
  },
  methods: {
    move(index, action) {
      if (action === 1) {
        if (index !== this.fileList.length - 1) {
          this.fileList[index] = this.fileList.splice(index + 1, 1, this.fileList[index])[0]
        }
      } else {
        if (index !== 0) {
          this.fileList[index] = this.fileList.splice(index - 1, 1, this.fileList[index])[0]
        }
      }
    },
    /** 返回 */
    handleClose(code) {
      this.$emit('closeDialog', code)
    },
    /** 提交 */
    submit() {
      this.addUpdateRecord()
    },
    async addUpdateRecord() {
      // 更新
      // console.log('fileList=', this.fileList)
      // console.log('form.pics=', this.form.pics)
      const res = await saveOrdersRecord(this.form)
      if (res.head.ret === 0) {
        this.$message.success('保存成功')
        this.handleClose('1')
      }
    }
  }

}
</script>

<style lang='scss' scoped>
  .avatar-uploader .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }
  .avatar-uploader .el-upload:hover {
    border-color: #409EFF;
  }
  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 178px;
    height: 178px;
    line-height: 178px;
    text-align: center;
  }
  .avatar {
    width: 178px;
    height: 178px;
    display: block;
  }
</style>
