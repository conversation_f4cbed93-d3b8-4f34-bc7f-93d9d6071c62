<template>
  <div>
    <div class="table-container2">
      <div
        class="table-container2-item"
        v-for="item in userList"
        :key="item.userInfoDto.userId"
      >
        <span
          >{{ item.accountInfoDto[0].account }}({{
            item.userInfoDto.nickName
          }})</span
        >
        <el-button
          size="mini"
          type="warning"
          @click="removeUser(null, item.userInfoDto)"
          >移除</el-button
        >
      </div>
    </div>
    <div class="control-container">
      <span class="demonstration" style="padding-right: 10px">账号/昵称：</span>
      <el-input
        v-model="keyword"
        type="text"
        maxlength="100"
        style="width:150px;padding-right: 10px"
      />
      <el-button type="primary" @click="queryUserList">查询</el-button>
    </div>
    <div class="table-container">
      <el-table
        ref="multipleTable1"
        :data="tableList"
        tooltip-effect="dark"
        style="width: 100%"
        border
      >
        <el-table-column label="用户codieId" width="120">
          <template slot-scope="scope">{{ scope.row.codeId }}</template>
        </el-table-column>
        <el-table-column label="用户账号" width="120">
          <template slot-scope="scope">
            <div v-for="item in scope.row.accountInfoDtoList" :key="item.id">
              {{ item.account }}
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="nickName" label="昵称" width="120" />
        <el-table-column label="操作" fixed="right" width="150" align="center">
          <template slot-scope="scope">
            <div v-if="!isExist(scope.row)">
              <el-button
                size="mini"
                v-permission="'btn-menuSystemsetting-version-edit'"
                @click="addUser(scope.$index, scope.row)"
                style
                >添加</el-button
              >
            </div>
            <div v-if="isExist(scope.row)">
              <el-button
                v-permission="'btn-menuSystemsetting-version-edit'"
                size="mini"
                type="danger"
                @click="removeUser(scope.$index, scope.row)"
                >取消</el-button
              >
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script>
import { findUserInfoByKeyword2 } from "@/api/user/list"
import {
  getWhiteUserList,
  addWhiteUser,
  removeWhiteUser
} from "@/api/version/driver.js";
export default {
  name: "WhiteUserList",
  props: {
    // your props here
  },
  data() {
    return {
      tableList: [],
      userList: [],
      keyword: ""
    };
  },
  computed: {
    // your computed properties here
  },
  watch: {
    // your watch here
  },
  created() {
    // your code here
  },
  mounted() {
    this.findAll();
  },
  methods: {
    findAll() {
      getWhiteUserList().then(res => {
        console.log(res);
        this.userList = res.data;
      });
    },
    addUser(index, row) {
      const param = {
        otherid: row.id
      };
      addWhiteUser(param).then(res => {
        this.$message.success("添加成功");
        this.findAll();
      });
    },
    removeUser(index, row) {
      const param = {
        otherid: row.userId
      };
      removeWhiteUser(param).then(res => {
        this.$message.success("移除成功");
        this.findAll();
      });
    },
    isExist(row) {
      let result = false;
      this.userList.forEach(item => {
        if (item.userInfoDto.userId === row.userId) {
          result = true;
        }
      });
      return result;
    },
    queryUserList(e) {
      const params = {
        pageNo: 1,
        pageSize: 10,
        sort: "desc",
        sortType: "time",
        keyword: this.keyword
      };
      findUserInfoByKeyword2(params).then(res => {
        if (res.head.ret === 0 && res.data.length > 0) {
          this.tableList = res.data
        } else {
          this.tableList = []
        }

        // if (res.head.ret === 0 && res.data.result.length > 0) {
        //   console.log(res.data.result);
        //   this.tableList = res.data.result;
        // } else {
        //   this.tableList = [];
        // }
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.table-container2 {
  display: flex;
  flex-wrap: wrap;
  align-content: flex-start;
  &-item {
    width: 230px;
    padding-right: 5px;
    margin-bottom: 10px;
  }
}
</style>
