<template>
  <div class="contents">
      <div class="logo">
          <img src="https://m.yoyin.net/h5/img/web/about/about-pingpaijieshao.png" style="width:100%; height:580px"/>
      </div>

      <div class="logo">
        <img src="https://m.yoyin.net/h5/img/web/about/about-jixiangwu.png" style="width:100%; height:723px"/>
          <!-- <img src="https://m.yoyin.net/h5/img/web/about/about-jxw-title.png" style="width:314px; height:72px"/> -->
      </div>
      <div class="jxw">
          <!-- <div class="jxw-left">
            <img src="https://m.yoyin.net/h5/img/web/about/about-jxw-mix.png" style="height:591px; width:612px;">
          </div>
          <div class="jxw-center">
            <div class="jwx-center-text">
              <img src="https://m.yoyin.net/h5/img/web/about/about-jxw-text.png" style="height:199px; width:295px;">
            </div>
            <div class="jxw-center-header">
              <div id="jxwCanvas" ref="jxwCanvas" style="height: 211px; width:163px"></div>
            </div>
            <div class="jxw-center-download">
              <img src="https://m.yoyin.net/h5/img/web/about/about-jxw-download.png" style="height:319px; width:468px;">
            </div>
          </div> -->
      </div>

      <div class="qiyejieshao">
          <img src="https://m.yoyin.net/h5/img/web/about/about-qiyejieshao.png" style="width:1200px; height:441px"/>
      </div>
  </div>
</template>

<script>
import SVGA from 'svgaplayerweb'
export default {
  name: 'AboutUs',
  data () {
    return {
      jxwCanvasUrl: 'https://m.yoyin.net/h5/img/web/about/aboutjxwhello.svga'
    }
  },
  mounted () {
    this.initMachineSVGA('jxwCanvas', this.jxwCanvasUrl)
  },
  methods: {
    initMachineSVGA (idValue, url) {
      let player = new SVGA.Player('#' + idValue)
      let parser = new SVGA.Parser('#' + idValue)
      // this.imageUrl 定义一个参数接收url
      parser.load(url, function (videoItem) {
        player.setVideoItem(videoItem)
        player.startAnimation()
      })
    }
  }
}
</script>

<style scoped>
.contents{

  width: 1200px;
    text-align: left;
    /* margin-top: 136px;
    margin-bottom: 136px; */
}
.logo{
    text-align: center;
    margin: 120px 0 60px 0;
}
.jxw {
    display: flex;
}

.jxw-left {
  padding-right: 120px;
}

.jxw-center {
  position: relative;
  width: 468px;
}

.jwx-center-text {
  padding-top: 20px;
  width: 294px;
  padding-right: 10px;
}

.jxw-center-header {
  position: absolute;
  right: 0;
  top: 0px;
}

.jxw-center-download {
  position: absolute;
  bottom: 0px;
}

.qiyejieshao {
  padding-top: 120px;
}

</style>
