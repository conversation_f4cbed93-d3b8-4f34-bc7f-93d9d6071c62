{"remainingRequest": "D:\\workspace\\xplife\\yoyin-ui\\yoyin-admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\workspace\\xplife\\yoyin-ui\\yoyin-admin\\src\\views\\office\\material\\index.vue?vue&type=template&id=22b158ed&scoped=true", "dependencies": [{"path": "D:\\workspace\\xplife\\yoyin-ui\\yoyin-admin\\src\\views\\office\\material\\index.vue", "mtime": 1751274032261}, {"path": "D:\\workspace\\xplife\\yoyin-ui\\yoyin-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\workspace\\xplife\\yoyin-ui\\yoyin-admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\workspace\\xplife\\yoyin-ui\\yoyin-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\workspace\\xplife\\yoyin-ui\\yoyin-admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\n<div class=\"app-container\">\n  <div class=\"list-contatiner\">\n    <div class=\"list-container-change\">\n      <el-radio-group\n        v-model=\"currentId\"\n        size=\"small\"\n        style=\"margin-right: 20px\"\n        @change=\"handleMaterialChange\"\n      >\n        <el-radio-button label=\"\">全部</el-radio-button>\n        <el-radio-button\n          v-for=\"item in industryList\"\n          :key=\"item.code\"\n          :label=\"item.code\"\n          >{{ item.name }}</el-radio-button\n        >\n      </el-radio-group>\n      <el-button type=\"primary\" @click=\"handleAdd\">新增办公素材</el-button>\n    </div>\n    <div class=\"list-container-data\">\n      <div class=\"list-container-data-cards\">\n        <div\n          v-for=\"(item, index) in list\"\n          :key=\"index\"\n          class=\"list-container-data-cards-item\"\n        >\n          <Card\n            :item=\"item\"\n            @notifyUpdate=\"notifyUpdate\"\n            @notifyEdit=\"notifyEdit\"\n          />\n        </div>\n      </div>\n      <div class=\"list-container-data-page\">\n        <el-pagination\n          :total=\"page.total\"\n          :current-page=\"page.no\"\n          :page-size=\"page.size\"\n          layout=\"total, sizes, prev, pager, next, jumper\"\n          @current-change=\"handleCurrentChange\"\n          @size-change=\"handleSizeChange\"\n        />\n      </div>\n    </div>\n  </div>\n  <div class=\"dialog-container\">\n    <el-dialog\n      :append-to-body=\"true\"\n      :title=\"currentTitile\"\n      :visible.sync=\"showAdd\"\n      center\n      :show-close=\"false\"\n      :destroy-on-close=\"true\"\n    >\n      <CommonForm\n        v-if=\"currentView === 'CommonForm'\"\n        :m-id=\"currentId\"\n        :m-name=\"currentName\"\n        :form-data=\"currentForm\"\n        @handleBack=\"handleBack\"\n      />\n      <ToDoListForm\n        v-if=\"currentView === 'ToDoListForm'\"\n        :m-id=\"currentId\"\n        :m-name=\"currentName\"\n        :form-data=\"currentForm\"\n        @handleBack=\"handleBack\"\n      />\n      <TextPopForm\n        v-if=\"currentView === 'TextPopForm'\"\n        :m-id=\"currentId\"\n        :m-name=\"currentName\"\n        :form-data=\"currentForm\"\n        @handleBack=\"handleBack\"\n      />\n      <FontForm\n        v-if=\"currentView === 'FontForm'\"\n        :m-id=\"currentId\"\n        :m-name=\"currentName\"\n        :form-data=\"currentForm\"\n        @handleBack=\"handleBack\"\n      />\n    </el-dialog>\n  </div>\n</div>\n", null]}