import request from '@/utils/request'

// 翻页查询
const LIST_URL = '/platform/gam/community/v1/titleType/findlist'
const DELETE_RECORD_URL = '/platform/gam/community/v1/titleType/deltitletype'
const SAVE_URL = '/platform/gam/community/v1/titleType/updatetitletype'
// 删除记录

/**
 * 查询列表数据
 * @param params 参数
 */
export const fetchListByPage = async params => {
  return request({
    url: LIST_URL,
    method: 'get',
    params: params
  })
}

/**
 * 删除记录
 * @param {String} id 记录id
 */
export const deleteRecord = async params => {
  return request({
    url: DELETE_RECORD_URL,
    method: 'get',
    params
  })
}

/** 保存记录 */
export const saveRecord = async params => {
  return request({
    url: SAVE_URL,
    method: 'post',
    data: params
  })
}

/**
 * 查询列表数据
 * @param params 参数
 */
export const fetchTitleTypeList = async params => {
  return request({
    url: LIST_URL,
    method: 'get',
    params: params
  })
}
