{"remainingRequest": "D:\\workspace\\xplife\\yoyin-ui\\yoyin-admin\\node_modules\\thread-loader\\dist\\cjs.js!D:\\workspace\\xplife\\yoyin-ui\\yoyin-admin\\node_modules\\babel-loader\\lib\\index.js!D:\\workspace\\xplife\\yoyin-ui\\yoyin-admin\\src\\router\\index.js", "dependencies": [{"path": "D:\\workspace\\xplife\\yoyin-ui\\yoyin-admin\\src\\router\\index.js", "mtime": 1751272607354}, {"path": "D:\\workspace\\xplife\\yoyin-ui\\yoyin-admin\\babel.config.js", "mtime": 1744192657864}, {"path": "D:\\workspace\\xplife\\yoyin-ui\\yoyin-admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\workspace\\xplife\\yoyin-ui\\yoyin-admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\workspace\\xplife\\yoyin-ui\\yoyin-admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}], "contextDependencies": [], "result": ["/*\r\n * @Description: In User Settings Edit\r\n * @Author: your name\r\n * @Date: 2019-10-16 10:58:32\r\n * @LastEditTime: 2019-10-16 18:13:52\r\n * @LastEditors: Please set LastEditors\r\n */\nimport Vue from 'vue';\nimport Router from 'vue-router';\nVue.use(Router);\n\n/* Layout */\nimport Layout from '@/layout';\n\n/**\r\n * Note: sub-menu only appear when route children.length >= 1\r\n * Detail see: https://panjiachen.github.io/vue-element-admin-site/guide/essentials/router-and-nav.html\r\n *\r\n * hidden: true                   if set true, item will not show in the sidebar(default is false)\r\n * alwaysShow: true               if set true, will always show the root menu\r\n *                                if not set alwaysShow, when item has more than one children route,\r\n *                                it will becomes nested mode, otherwise not show the root menu\r\n * redirect: noRedirect           if set noRedirect will no redirect in the breadcrumb\r\n * name:'router-name'             the name is used by <keep-alive> (must set!!!)\r\n * meta : {\r\n    roles: ['admin','editor']    control the page roles (you can set multiple roles)\r\n    title: 'title'               the name show in sidebar and breadcrumb (recommend set)\r\n    icon: 'svg-name'             the icon show in the sidebar\r\n    breadcrumb: false            if set false, the item will hidden in breadcrumb(default is true)\r\n    activeMenu: '/example/list'  if set path, the sidebar will highlight the path you set\r\n  }\r\n */\n\n/**\r\n * constantRoutes\r\n * a base page that does not have permission requirements\r\n * all roles can be accessed\r\n */\nexport var constantRoutes = [{\n  path: '/login',\n  component: function component() {\n    return import('@/views/login/index');\n  },\n  hidden: true\n}, {\n  path: '/404',\n  component: function component() {\n    return import('@/views/404');\n  },\n  hidden: true\n},\n// {\n//   path: '/',\n//   component: Layout,\n//   redirect: '/dashboard',\n//   children: [{\n//     path: 'dashboard',\n//     name: 'Dashboard',\n//     component: () => import('@/views/dashboard/index'),\n//     meta: { title: '首页', icon: 'dashboard' }\n//   }]\n// },\n\n{\n  path: '/',\n  component: Layout,\n  redirect: '/dataanalysis',\n  meta: {\n    title: 'Home',\n    authCode: 'menuAnalysis'\n  },\n  children: [{\n    path: 'dataanalysis',\n    name: 'dataanalysis',\n    component: function component() {\n      return import('@/views/dataanalysis/index');\n    },\n    meta: {\n      title: '数据统计',\n      icon: 'analysis',\n      authCode: 'menuAnalysis'\n    }\n  }]\n}, {\n  path: '/community',\n  component: Layout,\n  redirect: 'noRedirect',\n  name: 'community',\n  meta: {\n    title: '2寸、宽幅',\n    icon: 'community',\n    authCode: 'menu2cunkuanfu'\n  },\n  children: [{\n    path: 'material',\n    name: 'material',\n    component: function component() {\n      return import('@/views/community/material/index?menuType=a2');\n    },\n    meta: {\n      title: '素材管理',\n      icon: 'material',\n      authCode: 'menu2cunkuan-material'\n    }\n  }, {\n    path: 'soulword',\n    name: 'soulword',\n    component: function component() {\n      return import('@/views/community/soulword/index');\n    },\n    meta: {\n      title: '每日一言',\n      icon: 'table',\n      authCode: 'menu2cunkuan-soulword'\n    }\n  }, {\n    path: 'simplepicture',\n    name: 'simplepicture',\n    component: function component() {\n      return import('@/views/community/simplepicture/index');\n    },\n    meta: {\n      title: '简笔画',\n      icon: 'table',\n      authCode: 'menu2cunkuan-simplepicture'\n    }\n  }, {\n    path: 'englishword',\n    name: 'englishword',\n    component: function component() {\n      return import('@/views/words/list/index');\n    },\n    meta: {\n      title: '单词本',\n      icon: 'material',\n      authCode: 'menu2cunkuan-englishword'\n    }\n  }, {\n    path: 'knowledge',\n    name: 'knowledge',\n    component: function component() {\n      return import('@/views/community/knowledge/index');\n    },\n    meta: {\n      title: '干货区列表',\n      icon: 'table',\n      authCode: 'menu2cunkuan-knowledge'\n    }\n  }]\n}, {\n  path: '/xeasylabel',\n  component: Layout,\n  redirect: 'noRedirect',\n  name: 'xeasylabel',\n  meta: {\n    title: '标签打印机',\n    icon: 'xeasylabel',\n    authCode: 'xeasylabel'\n  },\n  children: [{\n    path: 'xeasy-dict',\n    name: 'xeasy-dict',\n    component: function component() {\n      return import('@/views/xeasylabel/dict/index');\n    },\n    meta: {\n      title: '字典项管理',\n      icon: 'dict',\n      authCode: 'xeasylabel-dict'\n    }\n  }, {\n    path: 'xeasy-material',\n    name: 'xeasy-material',\n    component: function component() {\n      return import('@/views/xeasylabel/material/index');\n    },\n    meta: {\n      title: '素材库',\n      icon: 'table',\n      authCode: 'xeasylabel-material'\n    }\n  }, {\n    path: 'xeasy-paperinfo',\n    name: 'xeasy-paperinfo',\n    component: function component() {\n      return import('@/views/xeasylabel/paperinfo/index');\n    },\n    meta: {\n      title: '耗材',\n      icon: 'paperinfo',\n      authCode: 'xeasylabel-paperinfo'\n    }\n  }, {\n    path: 'xeasy-templatedraft',\n    name: 'xeasy-templatedraft',\n    component: function component() {\n      return import('@/views/xeasylabel/templatedraft/index');\n    },\n    meta: {\n      title: '标签模板',\n      icon: 'templatedraft',\n      authCode: 'xeasylabel-templatedraft'\n    }\n  }, {\n    path: 'xeasy-userdraft',\n    name: 'xeasy-userdraft',\n    component: function component() {\n      return import('@/views/xeasylabel/userdraft/index');\n    },\n    meta: {\n      title: '用户素材模板',\n      icon: 'table',\n      authCode: 'xeasylabel-userdraft'\n    }\n  }]\n}, {\n  path: '/formula',\n  component: Layout,\n  redirect: 'noRedirect',\n  name: 'formula',\n  meta: {\n    title: '学公式',\n    icon: 'setting',\n    authCode: 'menuFormula'\n  },\n  children: [{\n    path: 'formula-subject',\n    name: 'formula-subject',\n    component: function component() {\n      return import('@/views/community/formulainfo/formulasubject');\n    },\n    meta: {\n      title: '年段学科配置',\n      icon: 'dict',\n      authCode: 'menuFormula-subject'\n    }\n  }, {\n    path: 'formula-info',\n    name: 'formula-info',\n    component: function component() {\n      return import('@/views/community/formulainfo/formulaInfo');\n    },\n    meta: {\n      title: '公式管理',\n      icon: 'dict',\n      authCode: 'menuFormula-info'\n    }\n  }]\n}, {\n  path: '/initialTraining',\n  component: Layout,\n  redirect: 'noRedirect',\n  name: 'initialTraining',\n  meta: {\n    title: '启蒙训练',\n    icon: 'setting',\n    authCode: 'menuTraining'\n  },\n  children: [{\n    path: 'initialTraining-info',\n    name: 'initialTraining-info',\n    component: function component() {\n      return import('@/views/initialtraining/info');\n    },\n    meta: {\n      title: '资源内容',\n      icon: 'dict',\n      authCode: 'menuTraining-info'\n    }\n  }, {\n    path: 'initialTraining-dict',\n    name: 'initialTraining-dict',\n    component: function component() {\n      return import('@/views/initialtraining/dict');\n    },\n    meta: {\n      title: '类目管理',\n      icon: 'dict',\n      authCode: 'menuTraining-dict'\n    }\n  }]\n}, {\n  path: '/community2',\n  component: Layout,\n  redirect: 'noRedirect',\n  name: 'community2',\n  meta: {\n    title: 'A4',\n    icon: 'community',\n    authCode: 'menuA4'\n  },\n  children: [{\n    path: 'material2',\n    name: 'material2',\n    component: function component() {\n      return import('@/views/community/material-a4/index');\n    },\n    meta: {\n      title: '模板素材A4/A5',\n      icon: 'material',\n      authCode: 'menuA4-material'\n    }\n  },\n  // {\n  //   path: 'materialUsLetter',\n  //   name: 'materialUsLetter',\n  //   component: () => import('@/views/community/material-usletter/index'),\n  //   meta: { title: '模板素材-usletter', icon: 'material', authCode: 'menuA4-usletter' }\n  // },\n  {\n    path: 'simplepicture2',\n    name: 'simplepicture2',\n    component: function component() {\n      return import('@/views/community/simplepicture-a4/index');\n    },\n    meta: {\n      title: '图片素材',\n      icon: 'table',\n      authCode: 'menuA4-simplepicture'\n    }\n  }, {\n    path: 'examination',\n    name: 'examination',\n    component: function component() {\n      return import('@/views/community/examination/index');\n    },\n    meta: {\n      title: '学习资源',\n      icon: 'table',\n      authCode: 'menuA4-examination'\n    }\n  }, {\n    path: 'office-material',\n    name: 'office-material',\n    component: function component() {\n      return import('@/views/office/material/index');\n    },\n    meta: {\n      title: '办公素材',\n      icon: 'material',\n      authCode: 'menuA4-office-material'\n    }\n  }]\n}, {\n  path: '/user',\n  component: Layout,\n  redirect: '/user/usermanage',\n  name: 'user',\n  alwaysShow: true,\n  meta: {\n    title: '用户管理',\n    icon: 'usermanage',\n    authCode: 'menuUsermanage'\n  },\n  children: [{\n    path: 'usermanage',\n    name: 'usermanage',\n    component: function component() {\n      return import('@/views/user/list/index');\n    },\n    meta: {\n      title: '用户列表',\n      icon: 'table',\n      authCode: 'menuUsermanage-user'\n    }\n  }, {\n    path: 'userdraft',\n    name: 'userdraft',\n    component: function component() {\n      return import('@/views/user/userdraft/index');\n    },\n    meta: {\n      title: '用户动态',\n      icon: 'table',\n      authCode: 'menuUsermanage-draft'\n    }\n  }, {\n    path: 'feedback',\n    name: 'feedback',\n    component: function component() {\n      return import('@/views/feedback/list/index');\n    },\n    meta: {\n      title: '用户反馈',\n      icon: 'feedback',\n      authCode: 'menuUsermanage-feedback'\n    }\n  }]\n}, {\n  path: '/device',\n  component: Layout,\n  redirect: '/device/usermanage',\n  name: 'user',\n  alwaysShow: true,\n  meta: {\n    title: '设备管理',\n    icon: 'usermanage',\n    authCode: 'menuUsermanage'\n  },\n  children: [{\n    path: 'device',\n    name: 'device',\n    component: function component() {\n      return import('@/views/device/index');\n    },\n    meta: {\n      title: '设备管理',\n      icon: 'table',\n      authCode: 'menuUsermanage-device'\n    }\n  }]\n}, {\n  path: '/systemsetting',\n  component: Layout,\n  redirect: '/systemsetting/index',\n  name: 'systemsetting',\n  meta: {\n    title: '系统设置',\n    icon: 'setting',\n    authCode: 'menuSystemsetting'\n  },\n  children: [{\n    path: 'banner',\n    name: 'banner',\n    component: function component() {\n      return import('@/views/community/banner/index');\n    },\n    meta: {\n      title: 'banner管理',\n      icon: 'table',\n      authCode: 'menuSystemsetting-banner'\n    }\n  }, {\n    path: 'activion',\n    name: 'activion',\n    component: function component() {\n      return import('@/views/systemsetting/index');\n    },\n    meta: {\n      title: '活动设置',\n      icon: 'setting',\n      authCode: 'menuSystemsetting-activion'\n    }\n  }, {\n    path: 'help',\n    name: 'help',\n    component: function component() {\n      return import('@/views/systemhelp/index');\n    },\n    meta: {\n      title: '帮助FAQ',\n      icon: 'setting',\n      authCode: 'menuSystemsetting-help'\n    }\n  }, {\n    path: 'installpackage',\n    name: 'installpackage',\n    component: function component() {\n      return import('@/views/version/list/index');\n    },\n    meta: {\n      title: '安装包管理',\n      icon: 'table',\n      authCode: 'menuSystemsetting-installpackage'\n    }\n  }, {\n    path: 'version',\n    name: 'version',\n    component: function component() {\n      return import('@/views/version/list2/index');\n    },\n    meta: {\n      title: '固件版本管理',\n      icon: 'table',\n      authCode: 'menuSystemsetting-version'\n    }\n  }, {\n    path: 'functionssetting',\n    name: 'functionssetting',\n    component: function component() {\n      return import('@/views/functionssetting/index');\n    },\n    meta: {\n      title: '功能显示设定',\n      icon: 'setting',\n      authCode: 'menuSystemsetting-setting'\n    }\n  }, {\n    path: 'dict',\n    name: 'dict',\n    component: function component() {\n      return import('@/views/system/dict/index');\n    },\n    meta: {\n      title: '字典管理',\n      icon: 'dict',\n      authCode: 'menuSystemsetting-dict'\n    }\n  }]\n}, {\n  path: '/quanpin',\n  component: Layout,\n  redirect: '/quanpin/memgergoods',\n  name: 'memgergoods',\n  meta: {\n    title: 'vip会员管理',\n    icon: 'setting',\n    authCode: 'menuQuanpin'\n  },\n  children: [{\n    path: 'vipMember',\n    name: 'vipMember',\n    component: function component() {\n      return import('@/views/quanpin/vipmember/index');\n    },\n    meta: {\n      title: 'vip会员',\n      icon: 'table',\n      authCode: 'menuQuanpin-vipmember'\n    }\n  }, {\n    path: 'orderRecord',\n    name: 'orderRecord',\n    component: function component() {\n      return import('@/views/quanpin/orderrecord/index');\n    },\n    meta: {\n      title: '购买记录',\n      icon: 'table',\n      authCode: 'menuQuanpin-orderrecord'\n    }\n  }, {\n    path: 'memgergoodsvip',\n    name: 'memgergoodsvip',\n    component: function component() {\n      return import('@/views/quanpin/membergoods/index');\n    },\n    meta: {\n      title: 'vip页面管理',\n      icon: 'table',\n      authCode: 'menuQuanpin-memgergoodsvip'\n    }\n  }]\n}, {\n  path: '/coin',\n  component: Layout,\n  name: 'coin',\n  alwaysShow: true,\n  meta: {\n    title: '积分商城',\n    icon: 'usermanage',\n    authCode: 'menuCoin'\n  },\n  children: [{\n    path: 'titleList',\n    name: 'titleList',\n    component: function component() {\n      return import('@/views/user/titlelist/index');\n    },\n    meta: {\n      title: '挂件管理',\n      icon: 'table',\n      authCode: 'menuCoin-title'\n    }\n  }, {\n    path: 'goodslist',\n    name: 'goodslist',\n    component: function component() {\n      return import('@/views/coin/goods/index');\n    },\n    meta: {\n      title: '商品列表',\n      icon: 'table',\n      authCode: 'menuCoin-goods'\n    }\n  }, {\n    path: 'missionlist',\n    name: 'missionlist',\n    component: function component() {\n      return import('@/views/coin/mission/index');\n    },\n    meta: {\n      title: '积分任务列表',\n      icon: 'table',\n      authCode: 'menuCoin-mission'\n    }\n  }, {\n    path: 'orderlist',\n    name: 'orderlist',\n    component: function component() {\n      return import('@/views/coin/order/index');\n    },\n    meta: {\n      title: '兑换订单列表',\n      icon: 'table',\n      authCode: 'menuCoin-order'\n    }\n  },\n  // {\n  //   path: 'finishcount',\n  //   name: 'finishcount',\n  //   component: () => import('@/views/coin/analysiscountfinish/index'),\n  //   meta: { title: '任务次数统计', icon: 'table' }\n  // },\n  // {\n  //   path: 'rankcoin',\n  //   name: 'rankcoin',\n  //   component: () => import('@/views/coin/analysiscountrank/index'),\n  //   meta: { title: '新增积分统计', icon: 'table' }\n  // },\n  {\n    path: 'statisticscoin',\n    name: 'statisticscoin',\n    component: function component() {\n      return import('@/views/coin/analysis/index');\n    },\n    meta: {\n      title: '积分统计',\n      icon: 'table',\n      authCode: 'menuCoin-statisticscoin'\n    }\n  }]\n}, {\n  path: '/other',\n  component: Layout,\n  name: '其他配置',\n  alwaysShow: true,\n  meta: {\n    title: '其他配置',\n    icon: 'usermanage',\n    authCode: 'menuOther'\n  },\n  children: [{\n    path: 'materialtype',\n    name: 'materialtype',\n    component: function component() {\n      return import('@/views/community/materialtype/index');\n    },\n    meta: {\n      title: '素材库类型',\n      icon: 'material',\n      authCode: 'menuOther-materialtype'\n    }\n  }, {\n    path: 'tag',\n    name: 'tag',\n    component: function component() {\n      return import('@/views/community/tag/index');\n    },\n    meta: {\n      title: '社区标签',\n      icon: 'table',\n      authCode: 'menuOther-tag'\n    }\n  }, {\n    path: 'wordTypeList',\n    name: 'wordTypeList',\n    component: function component() {\n      return import('@/views/wordtype/index');\n    },\n    meta: {\n      title: '教材类型',\n      icon: 'material',\n      authCode: 'menuOther-wordtype'\n    }\n  }, {\n    path: 'account',\n    name: 'account',\n    component: function component() {\n      return import('@/views/auth/account/index');\n    },\n    meta: {\n      title: '账号管理',\n      icon: 'setting',\n      authCode: 'menuOther-account'\n    }\n  }, {\n    path: 'role',\n    name: 'role',\n    component: function component() {\n      return import('@/views/auth/role/index');\n    },\n    meta: {\n      title: '角色管理',\n      icon: 'setting',\n      authCode: 'menuOther-role'\n    }\n  }, {\n    path: 'resources',\n    name: 'resources',\n    component: function component() {\n      return import('@/views/auth/resources/index');\n    },\n    meta: {\n      title: '资源点设置',\n      icon: 'setting',\n      authCode: 'menuOther-resources'\n    }\n  }]\n},\n// 404 page must be placed at the end !!!\n{\n  path: '*',\n  redirect: '/404',\n  hidden: true\n}];\nvar createRouter = function createRouter() {\n  return new Router({\n    // mode: 'history', // require service support\n    scrollBehavior: function scrollBehavior() {\n      return {\n        y: 0\n      };\n    },\n    routes: constantRoutes\n  });\n};\nvar router = createRouter();\n\n// Detail see: https://github.com/vuejs/vue-router/issues/1234#issuecomment-357941465\nexport function resetRouter() {\n  var newRouter = createRouter();\n  router.matcher = newRouter.matcher; // reset router\n}\nexport default router;", null]}