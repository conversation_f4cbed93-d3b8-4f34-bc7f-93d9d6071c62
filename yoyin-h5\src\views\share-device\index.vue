<template>
  <div class="content">
    <!-- <div>{{debugMsg}}</div> -->
    <div class="send-box" ref="sendbox" id="sendbox">
      <div class="sender">
        From {{isAnonymous?`${$t('share-device.anonymous')}`:oauthdata.userName}}
      </div>
      <div class="dashed-line"></div>
      <div v-if="content" class="content">{{content}}</div>
      <img v-for="(item, index) in pics" :key="index" :src="item" @click="deleteImage(index)" />
      <div class="dashed-line"></div>

      <div class="to-user">
        <div class="nick-name">To {{data.userName}}</div>
        <div class="send-time"> {{sendTime}}</div>

      </div>
    </div>
    <div class="show-view-bg">
      <div class="show-view">
        <div class="header-box">
          <div class="item">
            <div class="left">{{$t('share-device.share-theme')}}</div>
            <div class="right">{{data.title}}</div>
          </div>
          <div class="item center">
            <div class="left">{{$t('share-device.countdown')}}</div>
            <div class="right">{{showDate}}</div>
          </div>
          <div class="item">
            <div class="left">{{$t('share-device.organizer')}}</div>
            <div class="user-img"><img :src="data.userPic" />{{data.userName}}</div>
          </div>
        </div>
        <div class="outtime-view" v-if="isOutTime">{{$t('share-device.sharing-has-expired')}}</div>
        <div class="edit-box" v-if="!isOutTime">
          <textarea rel="content" v-model="content" :placeholder="$t('share-device.enter-your-content')" @blur="onInputBlur"></textarea>
          <div class="pics" v-if="pics.length>0">
            <div class="pic" v-for="(item, index) in pics" :key="index">
              <img class="pic-img" :src="item" />
              <img class="delete-img" @click="deleteImage(index)" src="./assets/img/common_ic_h5shut.png" />
            </div>

          </div>
          <div class="btn-add-img" @click="closeBoard"  >
            <input ref="imageInput" type="file" accept="image/*" id="uploadImg" @change.stop="chooseImage($event)">
            <img src="./assets/img/print_ic_addlist.png" />{{$t('share-device.add-image')}}
          </div>
          <!-- <div>
            1834 uploadUrl: {{uploadUrl}}
            <img  :src="uploadUrl"  />
          </div> -->
        </div>
        <div v-if="!isOutTime" class="btn-anonymous" @click="changeAnonymous">
          <div class="btn-img" :class="{'checked':isAnonymous}"></div>{{$t('share-device.anonymous')}}
        </div>
        <div style="width:1px;height:6.5625rem" v-if="!isOutTime"></div>

        <div class="btn-send" v-if="!isOutTime" @click="sendNote">{{$t('share-device.send-to-friends')}}</div>
        <div class="bottom-placeholder"></div>
      </div>
    </div>
  </div>
</template>

<script>
import AliyunOSS from '@/utils/oss-util'
import { login, oauthData2User } from '@/utils/login'
import dayjs from 'dayjs'
import html2canvas from 'html2canvas'
import { setTimeout, setInterval, clearInterval } from 'timers'
import { getUrlParameter } from '@/utils/util'
import { canvasToBlob } from '@/utils/image-converter'
import { getShareInfo, savePrinterRecord } from './assets/js/api.js'
import { FILE_URL } from '@/consts/index'
import { getThirdAuthInfo, setThirdAuthInfo, browserInfo } from '@/utils/common'
export default {
  data () {
    return {
      moduleKey: 'share-device',
      shareId: '',
      isLoading: false,
      timer: null,
      isAnonymous: false,
      pics: [],
      content: '',
      showDate: '--',
      oauthdata: {},
      isOutTime: false,
      sendTime: '',
      data: {},
      uploadUrl: '',
      debugMsg: ''
    }
  },
  async mounted () {
    this.init()
    // let self = this
    // let i = 0
    // window.addEventListener('focusout', function () {
    //   setTimeout(() => {
    //     i++
    //     self.uploadUrl = 'focusout: ' + i
    //       window.scrollTo(0, document.documentElement.scrollTop || document.body.scrollTop)
    //       // window.scrollTo(0, document.documentElement.clientHeight)
    //     document.activeElement.scrollIntoViewIfNeeded(true)
    //   }, 50)
    // })
  },
  watch: {},
  methods: {
    init () {
      console.log('init', window.location.host, window.location.hostname)
      const params = getUrlParameter()
      if (!params) {
        return
      }

      let oauthdata = getThirdAuthInfo()
      const browser = browserInfo()
      // 本地和url参数中均无授权信息 调用登录方法
      if (!oauthdata && !params.oauthdata) {
        login()
        return
      }

      if (params.oauthdata) {
        oauthdata = oauthData2User(decodeURIComponent(params.oauthdata))
        setThirdAuthInfo(oauthdata)
      }

      this.oauthdata = oauthdata
      this.shareId = params.id
      this.loadData()
    },
    async loadData () {
      this.$loading(this.$t('loading'))
      const { head, data } = await getShareInfo(this.shareId)
      this.$loading.close()
      if (head.ret === 0) {
        this.data = data
        this.countDown(data.validityTime)
      } else {
        this.isOutTime = true
      }
    },
    countDown (deadline) {
      var times = dayjs(deadline).unix() - dayjs().unix()
      if (!this.timer) {
        this.startCountDown(times)
      }
      times--
      this.timer = setInterval(() => {
        this.startCountDown(times)
        times--
        if (this.showDate === ' 00:00:00') {
          // 已过期
          this.isOutTime = true
          if (this.timer) {
            clearInterval(this.timer)
          }
        } else {
          this.isOutTime = false
        }
      }, 1000)
    },

    startCountDown (times) {
      let day = 0
      let hour = 0
      let minute = 0
      let second = 0
      if (times > 0) {
        day = Math.floor(times / (60 * 60 * 24))
        hour = Math.floor(times / (60 * 60)) - day * 24
        minute = Math.floor(times / 60) - day * 24 * 60 - hour * 60
        second =
          Math.floor(times) - day * 24 * 60 * 60 - hour * 60 * 60 - minute * 60
      }
      if (hour <= 9) hour = '0' + hour
      if (minute <= 9) minute = '0' + minute
      if (second <= 9) second = '0' + second
      this.showDate =
        (day > 0 ? day + this.$t('day') : '') + ' ' + hour + ':' + minute + ':' + second
    },

    changeAnonymous () {
      this.isAnonymous = !this.isAnonymous
    },
    deleteImage (index) {
      this.pics.splice(index, 1)
    },
    onInputBlur() {
      window.scrollTo(0, 0)
    },
    closeBoard() {
      // 滚动到顶部
      window.scrollTo(0, 0)
    },
    chooseImage (e) {
      let file = e.target.files[0]
      let reader = new FileReader()
      let that = this
      reader.readAsDataURL(file)
      reader.onload = function (e) {
        that.pics.push(this.result)
      }
    },
    sendNote () {
      console.log('sendNote')
      if (this.isOutTime) {
        return this.$toast(this.$t('share-device.sharing-has-expired'))
      }

      if (this.content.length === 0 && this.pics.length === 0) {
        return this.$toast(this.$t('share-device.enter-your-content'))
      }

      this.$loading(this.$t('share-device.sending'))
      const that = this
      this.sendTime = dayjs().format('YYYY-MM-DD HH:mm:ss')
      window.pageYOffset = 0
      document.documentElement.scrollTop = 0
      document.body.scrollTop = 0

      // 延迟50ms，防止时间
      setTimeout(() => {
        // html转canvas
        html2canvas(this.$refs.sendbox, {
          backgroundColor: null
        }).then(canvas => {
          // canvas转blob
          canvasToBlob(canvas, file => {
           that.doUploadFile(file, '.jpeg')
            // 文件上传
            // const oss = new AliyunOSS()
            // oss.uploadFile(file, '.jpeg', (result, error) => {
            //   if (!error) {
            //     const picUrl = FILE_URL + result.name
            //     that.saveRecord(picUrl)
            //   } else {
            //     that.$toast(this.$t('share-device.image-upload-failed'))
            //     that.$loading.close()
            //   }
            // })
          })
        })
      }, 50)
    },
    async doUploadFile(file, prefix) {
        const oss = new AliyunOSS()
        let res = await oss.uploadFileSync(file, prefix)
        // console.log('res: ', res)
        let that = this
        if (res) {
            const picUrl = FILE_URL + res
            that.saveRecord(picUrl)
        } else {
            that.$toast(this.$t('share-device.image-upload-failed'))
            that.$loading.close()
        }
    },

    /**
     * 保存记录到服务器
     */
    async saveRecord (picUrl) {
      const anonymous = this.isAnonymous ? 1 : 0
      const params = {
        shareId: this.shareId,
        pic: picUrl,
        anonymous,
        nickName: this.oauthdata.userName,
        userPic: this.oauthdata.userPic,
        sex: this.oauthdata.sex,
        from: this.oauthdata.from
      }
      const { head } = await savePrinterRecord(params)
      this.$loading.close()
      if (head.ret === 0) {
        this.pic = undefined
        this.$toast(this.$t('share-device.send-success'))
        this.resetData()
      } else {
        this.$toast(head.msg)
      }
    },

    resetData () {
      this.pics = []
      this.content = ''
    }
  }
}
</script>

<style lang="scss">
$maxWidth: 400px;
$textSize: 16px;
@supports (bottom: env(safe-area-inset-bottom)) {
  body {
      padding-bottom: env(safe-area-inset-bottom);
  }
}
.content {
  max-width: $maxWidth;
  background: white;
  margin-left: auto;
  margin-right: auto;
  height: 100%;
  .show-view-bg {
    background: #efeff4;
    height: 100%;
    max-width: $maxWidth;
  }
  .show-view {
    max-width: $maxWidth;
    background: url('./assets/img/print_banner_interactprint.png') no-repeat;
    background-size: 100%;
    padding-top: 14rem;

    .header-box {
      background: white;
      border-radius: 0.5rem;
      margin-left: 0.75rem;
      margin-right: 0.75rem;

      .item {
        display: flex;
        color: black;
        font-size: $textSize;
        padding: 1rem 1rem;

        &.center {
          border-top: #d5d4d9 solid 0.2px;
          border-bottom: #d5d4d9 solid 0.2px;
        }
        .left {
          flex: 1;
        }
        .right {
          text-align: right;
        }

        .user-img {
          text-align: right;
          align-items: center;
          font-size: $textSize * 0.75;
          img {
            width: 1.5rem;
            height: 1.5rem;
            border-radius: 50%;
            margin-right: 0.5rem;
          }
        }
      }
    }
    .line {
      width: 100%;
      height: 1px;
      background: #d5d4d9;
    }

    .outtime-view {
      width: 100%;
      margin-top: 2.5rem;
      text-align: center;
      font-size: $textSize;
      color: #bcbbbf;
    }

    .edit-box {
      background: white;
      border-radius: 0.5rem;
      margin: 0.75rem;
      textarea {
        padding: 1rem;
        font-size: $textSize;
        width: 100%;
        background: white;
        border: none;
        border-radius: 0;
        min-height: 8.75rem;
        ::placeholder {
          color: #bcbbbf;
        }
      }

      .pics {
        padding: 1rem 1rem 0 1rem;
        border-top: #d8d8d8 solid 0.5px;
        .pic {
          position: relative;
          .pic-img {
            width: 100%;
            height: auto;
            margin-bottom: 1rem;
          }
          .delete-img {
            position: absolute;
            width: 2.25rem;
            height: 2.25rem;
            right: 0;
            top: 0;
          }
        }
      }

      .btn-add-img {
        position: relative;
        padding: 0 0.625rem;
        height: 3.125rem;
        line-height: 3.125rem;
        text-align: center;
        vertical-align: middle;
        color: #5a7dff;
        font-size: $textSize;
        border-top: #d8d8d8 solid 0.5px;
        input {
          opacity: 0;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          position: absolute;
        }
        img {
          width: 1.25rem;
          height: 1.25rem;
          margin-right: 0.75rem;
        }
      }
    }

    .btn-anonymous {
      font-size: $textSize * 0.75;
      line-height: 1.25rem;
      vertical-align: middle;

      color: #bcbbbf;
      padding-right: 0.75rem;
      width: 100%;
      display: flex;
      justify-content: flex-end;
      .btn-img {
        width: 1.25rem;
        height: 1.25rem;
        margin-right: 0.5rem;
        background: url('./assets/img/common_ic_choicecircl.png');
        background-size: 100%;

        &.checked {
          background: url('./assets/img/common_ic_choicesure.png');
          background-size: 100%;
        }
      }
    }

    .btn-send {
      position: fixed;
      max-width: $maxWidth;
      bottom: 0;
      width: 100%;
      background: #5a7dff;
      height: 3.125rem;
      font-size: $textSize;
      color: white;
      text-align: center;
      line-height: 3.125rem;
      &:active {
        opacity: 0.8; //这
      }
    }
    // // 判断iphoneX 将 footerBox 的 padding-bottom 填充到最底部
    @supports (bottom: env(safe-area-inset-bottom)) {
    .btn-send {
        margin-bottom: env(safe-area-inset-bottom);
      }
    }
    .bottom-placeholder{
      position: fixed;
      bottom: 0;
      width: 100%;
      height: env(safe-area-inset-bottom);
      background-color: #fff;
    }

  }

  .send-box {
    max-width: $maxWidth;
    z-index: -2;
    position: absolute;
    top: 0;
    width: 100%;
    margin: 0 auto;
    background: #fff;
    color: black;
    font-size: $textSize * 1.4;
    .content {
      padding: 1rem;
      width: 100%;
    }
    img {
      width: 100%;
      height: auto;
    }
    .dashed-line {
      height: 1px;
      width: 100%;
      background: url('./assets/img/line02.png');
      background-size: 100%;
    }
    .sender {
      padding-top: 1rem;
      padding-bottom: 1rem;
      text-align: center;
    }
    .to-user {
      vertical-align: middle;
      text-align: center;
      padding: 0.75rem;
      max-width: $maxWidth;
      .nick-name {
      }
      .send-time {
        margin-top: 0.5rem;
        font-size: $textSize * 1.3;
        padding-left: 0.75rem;
      }
    }
  }
}
</style>
