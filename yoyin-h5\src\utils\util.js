/**
 * 获取地址参数
 *
 * @param {string} data N 链接
 * @returns JSON
 */
export const getUrlParameter = data => {
  let query = {}
  let url = data
    ? data.substr(data.indexOf('?'))
    : document.location.search.replace('?', '')
  let urlParams = []
  if (url) {
    urlParams = url.split('&')
    urlParams.forEach(item => {
      let value = item.split('=')
      query[value[0]] = value[1]
    })
  }
  return query
}

/**
 * 获取请求API HOST
 */
const getApiHost = (() => {
  const query = getUrlParameter()
  return query.apiHost || 'api'
})()

/**
 * 获取host
 */
export const host = `https://${getApiHost}.sythealth.com`
