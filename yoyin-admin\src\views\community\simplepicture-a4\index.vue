<!--
 * @Description: In User Settings Edit
 * @Author: your name
 * @Date: 2019-10-16 11:28:20
 * @LastEditTime: 2019-10-22 17:03:31
 * @LastEditors: Please set LastEditors
 -->
<template>
  <div class="app-container">
    <div class="control-container">
      <el-radio-group v-model="currentTypeId" size="small" @change="refreshTypesDisplay">
        <el-radio-button key="全部" label="">全部</el-radio-button>
        <el-radio-button v-for="item in typeData" :key="item.name" :label="item.id">{{ item.name }}</el-radio-button>
      </el-radio-group>
      <p>
        <el-button type="primary" @click="handleQuery">查询</el-button>
        <el-button v-permission="'btn-menuA4-simplepicture-edit'" @click="addSimplePicture">添加</el-button>
      </p>
    </div>

    <div class="table-container">
      <div v-for="(item, index) in tableList" :key="item.id" class="items" :offset="index > 0 ? 2 : 0">
        <img :src="item.picVo.pic" class="image">
        <div style="padding: 14px;">
          <span>{{ item.name }}</span>
          <div class="bottom">
            <el-button size="mini" @click="handleEdit(index, item)">编辑</el-button>
            <el-button
              v-permission="'btn-menuA4-simplepicture-edit'"
              size="mini"
              type="danger"
              @click="handleDeleteRow(index, item)"
            >删除</el-button>
          </div>
        </div>
      </div>

    </div>
    <div class="pagination-container" style="margin-top:20px;">
      <el-pagination
        :current-page="currentPage"
        :page-sizes="[10, 20, 30, 40]"
        :page-size="currentSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
    <!-- 弹框 -->
    <div class="dialog-container">
      <el-dialog
        :title="form.id === '' ? '新增' : '编辑' "
        :visible.sync="showDialog"
        width="50%"
        center
      >
        <el-form
          ref="ruleForm"
          :model="form"
          :rules="rules"
          label-width="100px"
          class="demo-ruleForm"
        >
          <el-form-item label="名称" prop="name">
            <el-input v-model="form.name" />
          </el-form-item>
          <el-form-item label="类型" prop="type">
            <el-radio-group v-model="form.type" size="small">
              <el-radio-button v-for="item in typeData" :key="item.name" :label="item.id">{{ item.name }}</el-radio-button>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="描述" prop="remark">
            <el-input v-model="form.remark" />
          </el-form-item>
          <el-form-item label="排序" prop="sort">
            <el-input v-model="form.sort" />
          </el-form-item>
          <el-form-item label="上传图片">
            <SingleUpload key="listUrl" key-word="listUrl" :init-url="form.picVo && form.picVo.pic ? form.picVo.pic: ''" @updateFile="updateFile" />
          </el-form-item>
          <el-form-item>
            <el-button @click="showDialog = false">返回</el-button>
            <el-button v-permission="'btn-menuA4-simplepicture-edit'" type="primary" :loading="loading" @click="submitForm">保存</el-button>
          </el-form-item>
        </el-form>
      </el-dialog>
    </div>
  </div>
</template>
<script>
import { fetchListByPage, fetchTypeList, deleteRecord, saveRecord } from '@/api/community/simplepicture'
import SingleUpload from '@/views/community/material/component/SingleUpload'
import AliyunOSS from '@/utils/aliyunOSS'
const oss = new AliyunOSS()
oss.initClient()
// const TIP_MESSAGES = {
//   listUrl: '请选择图'
// }
export default {
  components: { SingleUpload },
  data() {
    return {
      currentTypeId: '',
      currentPage: 1,
      currentSize: 10,
      total: 100,
      tableList: [],
      typeData: [],
      multipleSelection: [],
      // 弹框
      showDialog: false,
      uploadFiles: { picVo: {}},
      // 表单
      form: {
        id: '',
        channel: '',
        url: '',
        param: '',
        version: '',
        remark: '',
        sizeType: 'a4'
      },
      // 检验规则
      rules: {
        channel: [
          { required: true, message: '请输入渠道', trigger: 'blur' },
          { min: 3, max: 20, message: '长度在 3 到 20 个字符', trigger: 'blur' }
        ],
        url: [{ required: true, message: '请选择文件', trigger: 'blur' }],
        version: [
          { required: true, message: '请输入版本号如1.1.2', trigger: 'blur' }
        ]
      },
      fileList: [],
      file: undefined,
      loading: false,
      showUrl: false
    }
  },

  computed: {
    /** 编辑的数据 */
    files() {
      if (this.form && this.form.picVo) {
        console.log('files=', this.form.picVo)
        return this.form.picVo
      } else {
        return {}
      }
    }
  },
  mounted() {
    this.getList()
    this.initTypeData()
  },
  methods: {
    initTypeData() {
      fetchTypeList().then(res => {
        this.typeData = res.data
      })
    },
    refreshTypesDisplay(e) {
      this.getList()
    },
    /** 添加 */
    addSimplePicture() {
      this.form = {
        picVo: {}
      }
      this.showDialog = true
      this.fileList = []
      this.file = undefined
      this.showUrl = false
    },
    /** 查询 */
    handleQuery() {
      this.getList()
    },
    /** 切换每页条数 */
    handleSizeChange(val) {
      console.log(`每页 ${val} 条`)
      this.currentSize = val
      this.getList()
    },
    /** 切换当前页 */
    handleCurrentChange(val) {
      console.log(`当前页: ${val}`)
      this.currentPage = val
      this.getList()
    },
    /** 获取表数据 */
    async getList() {
      const params = {
        pageno: this.currentPage,
        pagesize: this.currentSize,
        type: this.currentTypeId,
        sizeType: 'a4'
      }
      const res = await fetchListByPage(params)
      if (res.head.ret === 0) {
        const data = res.data
        this.total = data.totalCount
        this.tableList = data.result
      }
    },
    clearSelection() {
      this.$refs.multipleTable.clearSelection()
    },
    /** 勾选事件 */
    handleSelectionChange(val) {
      this.multipleSelection = val
      console.log('handleSelectionChange: ' + this.multipleSelection)
    },
    /** 编辑 */
    handleEdit(index, row) {
      this.form = row
      console.log('handleEdit=', this.form)
      if (!this.form.picVo) {
        this.form.picVo = {}
      }
      this.showDialog = true
    },

    /** 删除单行 */
    async handleDeleteRow(index, row) {
      const params = {
        id: row.id
      }
      this.$confirm('此操作将永久删除该记录, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          deleteRecord(params).then(res => {
            if (res.head.ret === 0) {
              this.$message({
                type: 'success',
                message: '删除成功!'
              })
              this.getList()
            }
          })
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    },
    /** 删除单行 */
    async handleMultiDel() {
      this.$confirm('此操作将永久删除记录, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          let i = 0
          const len = this.multipleSelection.length
          for (i = 0; i < len; i++) {
            const params = {
              id: this.multipleSelection[i].id
            }
            deleteRecord(params).then(res => {
              if (res.head.ret === 0) {
                this.$message({
                  type: 'success',
                  message: '删除成功!'
                })
                this.getList()
              }
            })
          }
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    },
    updateFile(key, file) {
      console.log(key, file)
      this.file = file
      // this.fileList.pop()
      // this.fileList.push({
      //   name: file.name,
      //   url: file.name
      // })
      // this.fileList[key] = {
      //   file,
      //   key
      // }
    },
    // 表单编辑
    /** 保存 */
    submitForm() {
      this.$refs['ruleForm'].validate(valid => {
        if (valid) {
          this.loading = true
          console.log('this.file=', this.file)
          if (!this.file) {
            this.saveConfig()
            return
          }
          oss.uploadFile(this.file, (result, error) => {
            if (error) {
              console.log('error')
              console.log(error)
              this.$message.error('上传文件失败')
              this.loading = false
              return
            }
            console.log('result=', result)
            if (result) {
              this.form.picVo.pic = result.url
              this.saveConfig()
              this.getList()
            }
          })
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    async saveConfig() {
      this.form['sizeType'] = 'a4'
      const res = await saveRecord(this.form)
      if (res.head.ret === 0) {
        this.$message.success('保存成功')
      }
      this.loading = false
      this.showDialog = false
      this.onlyUpload = false
      this.getList()
    },

    /** 移除之前 */
    handleRemove(file, fileList) {
      console.log('handleRemove')
      this.showUrl = true
    },
    /** 上传前 */
    beforeUpload(file) {
      return false
    },
    changeFile(file) {
      console.log('changeFile')
      console.log(file)
      this.file = file.raw
      this.fileList.pop()
      this.fileList.push({
        name: file.name,
        url: file.name
      })
      this.showUrl = false
    }
  }
}
</script>

<style lang="scss" scoped>
.app-container {
  margin: 20px;
  .control-container {
    margin-bottom: 20px;
  }
  .choose-container {
    margin-bottom: 20px;
    background-color: #e6f7ff;
    border: 1px solid #91d5ff;

    span {
      margin-left: 20px;
      line-height: 48px;
    }
    a {
      margin-left: 20px;
      color: #1890ff;
      text-decoration: none;
      background-color: transparent;
      outline: none;
      cursor: pointer;
      transition: color 0.3s;
    }
  }
}

.table-container {
  display: flex;
  flex-wrap: wrap;
  .items {
    height: 300px;
    width: 200px;
    margin-top: 15px;
    padding-right: 15px;
    margin-right: 15px;
    margin-left: 15px;
    padding-bottom: 15px;
    border-radius: 15px;
    text-align: center;
    border: solid 1px;
    .image {
      width: 170px;
      object-fit: fill;
      height: 180px;
      margin: 15px 15px;
    }
    .bottom {
      text-align: center;
    }
  }
}
</style>
