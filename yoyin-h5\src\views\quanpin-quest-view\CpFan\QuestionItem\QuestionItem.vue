<template>
  <div class="question-container">
    <div
      class="question-content"
      ref="renderQuestionRef"
      @click="toggleExplain(questionIndex)"
    >
      <!--是否 有音频 -->
      <!-- <AudioPlay v-if="quesitonAudio" :audiourl="quesitonAudio"></AudioPlay> -->
      <!-- 试题展示区 -->
      <question-cell
        :showAttr="showAttr"
        :level="1"
        :quesStructPm="
          ques.quesStruct && ques.quesStruct.code ? ques.quesStruct.code : '-1'
        "
        :ques="ques"
        :showQuestionNum="showQuestionNum"
        :questionNum="questionIndex"
      >
      </question-cell>
    </div>
    <!--试题考点区-->
    <div class="ques-explain-part" v-if="showKnowledge">
      <slot name="quesAttribute" :data="ques">
        <question-knowledge
          :showKnowledge="showKnowledge"
          :showExplainCode="ques.showExplainCode"
          :knowledgesData="ques.knowledge"
          @onShowLogin="onShowLogin"
        >
        </question-knowledge>
      </slot>

      <!-- 试题解析答案 -->
      <answer-analysis v-if="showExplain" :ques="ques"> </answer-analysis>
    </div>
  </div>
</template>

<script>
  export default {
    props: {
      ques: {
        type: Object,
        default() {
          return {}
        }
      },
      showQuestionNum: {
        type: Boolean,
        default: false
      },
      questionIndex: {
        type: Number,
        default: 0
      },
      showExplain: {
        // 是否显示答案和解析
        type: Boolean,
        default: false
      },
      showKnowledge: {
        // 是否 显示知识点
        type: Boolean,
        default: true
      }
    },
    // computed: {
    //   quesitonAudio() {
    //     if (this.ques && this.ques.context && this.ques.context.audio) {
    //       return this.ques.context.audio
    //     } else {
    //       return ''
    //     }
    //   },
    // },
    data() {
      return {}
    },
    methods: {
      // 显示答案
      toggleExplain(questionIndex) {
        this.$emit('toggleExplain', this.ques, questionIndex)
      },
      onShowLogin() {
        this.$emit('onShowLogin')
      }
    }
  }
</script>

<style lang="scss" scoped>
  .question-container {
    border-radius: 6px;
  }
  .question-bottom {
    border-radius: 0 0 6px 6px;
    .question-operate {
      .operate-btn .iconfont {
        color: #fea600;
      }
      .operate-btn.remove-btn .iconremove,
      .operate-btn.add-btn .iconadd {
        color: #ffffff;
      }
      .operate-btn:hover {
        color: #619cf5;
        .iconfont {
          color: #fea600;
        }
        &.remove-btn,
        &.add-btn {
          color: #ffffff;
          .iconremove,
          .iconadd {
            color: #ffffff;
          }
        }
      }
    }
    .operate-btn.collected-btn.gl,
    .operate-btn.collect-btn.gl {
      width: 72px;
      display: inline-block;
    }
  }
  .ques-cell {
    width: 100%;
  }
  .ques-explain-part {
    padding: 10px 28px;
    border-top: 1px solid #eeeeee;
  }

  .explain-code-info {
    padding: 29px 0;
    text-align: center;
    background: #f4f4f4;
    color: #999999;
    margin: 10px 0;
    line-height: 20px;
    .active-serve {
      color: #619cf5;
      text-decoration: underline;
    }
  }
  .hide-collect-black {
    display: inline-block;
    height: 14px;
    width: 25px;
  }
</style>
