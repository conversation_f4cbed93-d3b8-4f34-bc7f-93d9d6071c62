<!--
 * @Description: In User Settings Edit
 * @Author: your name
 * @Date: 2019-10-16 11:28:20
 * @LastEditTime: 2019-10-22 17:03:31
 * @LastEditors: Please set LastEditors
 -->
<template>
  <div class="app-container">
    <div class="table-container">
      <el-tabs>
        <el-tab-pane v-for="item in typeList" :key="item.id" :label="item.name">
          <el-tabs>
            <el-tab-pane
              v-for="itemType in typeMap[item.id]"
              :key="itemType.id"
              :label="itemType.name"
            >
              <el-button
                v-permission="'btn-menu2cunkuan-englishword-edit'"
                @click="addCourse(item, itemType)"
                >添加</el-button
              >
              <el-table
                :data="courseMap[itemType.id]"
                tooltip-effect="dark"
                style="width: 100%"
                border
                max-height="700px"
              >
                <template v-if="!courseMap[itemType.id]">
                  <div>暂无数据</div>
                </template>
                <el-table-column label="封面" width="100">
                  <template slot-scope="scope">
                    <el-image
                      v-if="scope.row.pics && scope.row.pics['200x264']"
                      :src="getCoverUrl(scope.row.pics['200x264'])"
                      style="width: 80px; height: 100px; object-fit: cover"
                      :preview-src-list="[
                        getCoverUrl(scope.row.pics['200x264']),
                      ]"
                    />
                    <div
                      v-else
                      style="
                        width: 80px;
                        height: 100px;
                        background: #f5f7fa;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        color: #909399;
                      "
                    >
                      暂无封面
                    </div>
                  </template>
                </el-table-column>
                <el-table-column prop="name" label="名称" />
                <el-table-column prop="remark" label="备注" />
                <el-table-column prop="sort" label="排序" />
                <el-table-column prop="totalCount" label="单词总数" />
                <el-table-column label="操作">
                  <template slot-scope="scope">
                    <a
                      v-permission="'btn-menu2cunkuan-englishword-edit'"
                      style="
                        color: #1890ff;
                        text-decoration: none;
                        cursor: pointer;
                        transition: color 0.3s;
                      "
                      @click="handleViewCourse(scope.$index, scope.row)"
                      >修改</a
                    >
                    <a
                      style="
                        color: #1890ff;
                        text-decoration: none;
                        cursor: pointer;
                        transition: color 0.3s;
                      "
                      @click="handleViewDetails(scope.row)"
                      >查看</a
                    >
                    <a
                      style="
                        color: #ff4d4f;
                        text-decoration: none;
                        cursor: pointer;
                        margin-left: 10px;
                        transition: color 0.3s;
                      "
                      @click="handleDeleteRow(scope.$index, scope.row)"
                      >删除</a
                    >
                  </template>
                </el-table-column>
              </el-table>
            </el-tab-pane>
          </el-tabs>
        </el-tab-pane>
      </el-tabs>
    </div>
    <!-- 弹框 -->
    <div class="dialog-container">
      <el-dialog
        :title="form.id === '' ? '新增课本' : '编辑课本'"
        :visible.sync="showDialog"
        width="50%"
        center
      >
        <el-form
          ref="ruleForm"
          :model="form"
          :rules="rules"
          label-width="100px"
          class="demo-ruleForm"
        >
          <el-form-item label="名称" prop="name">
            <el-input v-model="form.name" />
          </el-form-item>
          <el-form-item label="备注" prop="remark">
            <el-input v-model="form.remark" />
          </el-form-item>
          <el-form-item label="排序">
            <el-input v-model="form.sort" />
          </el-form-item>
          <el-form-item label="单词总量">
            <el-input v-model="form.totalCount" />
          </el-form-item>
          <el-form-item label="更换封面">
            <el-upload
              ref="upload"
              class="upload-demo"
              action
              :auto-upload="false"
              :on-remove="handleRemove"
              :file-list="fileList"
              :on-change="changeFile"
              :before-upload="beforeUpload"
            >
              <el-button
                slot="trigger"
                v-permission="'btn-menu2cunkuan-englishword-edit'"
                size="small"
                type="primary"
                >选取文件</el-button
              >
            </el-upload>
          </el-form-item>
          <el-form-item>
            <el-button @click="showDialog = false">返回</el-button>
            <el-button
              v-permission="'btn-menu2cunkuan-englishword-edit'"
              type="primary"
              :loading="loading"
              @click="submitForm"
              >保存</el-button
            >
          </el-form-item>
        </el-form>
      </el-dialog>
    </div>
    <div class="dialog-container">
      <el-dialog :visible.sync="showCourseDetailsDialog" width="80%" center>
        <course-detail :course-id="courseId" />
      </el-dialog>
    </div>
  </div>
</template>
<script>
import { fetchListByPage, deleteRecord, saveRecord } from '@/api/words/list'
import AliyunOSS from '@/utils/aliyunOSS'
import CourseDetail from '../course'
const oss = new AliyunOSS()
export default {
  components: { CourseDetail },
  data() {
    return {
      currentPage: 1,
      currentSize: 10,
      total: 100,
      tableList: [],
      multipleSelection: [],
      // 弹框
      showDialog: false,
      // 仅上传文件模式
      onlyUpload: false,
      showCourseDetailsDialog: false,
      // 表单
      form: {
        id: '',
        name: '',
        sort: '',
        version: '',
        remark: '',
        totalCountL: 0,
        type: 0,
        courseId: '',
        typeName: '',
        subName: ''
      },
      // 表单
      multiForm: {
        version: '',
        remark: ''
      },
      // 检验规则
      rules: {
        // channel: [
        //   { required: true, message: '请输入渠道', trigger: 'blur' },
        //   { min: 3, max: 20, message: '长度在 3 到 20 个字符', trigger: 'blur' }
        // ],
        // url: [{ required: true, message: '请选择文件', trigger: 'blur' }],
        // version: [
        //   { required: true, message: '请输入版本号如1.1.2', trigger: 'blur' }
        // ]
      },
      multiFormRules: {
        version: [
          { required: true, message: '请输入版本号如1.1.2', trigger: 'blur' }
        ]
      },
      fileList: [],
      file: undefined,
      loading: false,
      showUrl: false,
      typeMap: {},
      courseMap: {},
      typeList: [],
      courseId: ''
    }
  },

  computed: {},
  mounted() {
    this.getList()
  },
  methods: {
    getCoverUrl(url) {
      if (url.indexOf('http') > -1) {
        return url
      } else {
        return 'https://m.starpany.cn/' + url
      }
    },
    handleViewDetails(item) {
      this.courseId = item.id
      this.showCourseDetailsDialog = true
    },
    handleViewCourse(index, item) {
      this.form = item
      this.showDialog = true
    },
    /** 添加 */
    addCourse(item, itemType) {
      this.form = {
        typeId: itemType.id,
        subName: itemType.name,
        typeName: item.name
      }
      this.fileList = []
      this.file = undefined
      this.showDialog = true
    },
    /** 查询 */
    queryVersion() {
      this.getList()
    },
    /** 切换每页条数 */
    handleSizeChange(val) {
      console.log(`每页 ${val} 条`)
      this.currentSize = val
      this.getList()
    },
    /** 切换当前页 */
    handleCurrentChange(val) {
      console.log(`当前页: ${val}`)
      this.currentPage = val
      this.getList()
    },
    /** 获取表数据 */
    async getList() {
      const params = {
        pageno: this.currentPage,
        pagesize: this.currentSize
      }
      const res = await fetchListByPage(params)
      if (res.head.ret === 0) {
        const data = res.data
        console.log('API返回数据:', data)
        console.log('wordCourseMap:', data.wordCourseMap)
        this.typeList = data.typeList
        this.typeMap = data.typeMap
        this.courseMap = data.wordCourseMap

        // 对 typeMap 中的每个数组按照 sort 字段进行排序
        Object.keys(this.typeMap).forEach(key => {
          if (Array.isArray(this.typeMap[key])) {
            this.typeMap[key].sort((a, b) => {
              const sortA = parseInt(a.sort) || 0
              const sortB = parseInt(b.sort) || 0
              return sortA - sortB
            })
          }
        })

        // 对 courseMap 中的每个数组按照 sort 字段进行排序
        Object.keys(this.courseMap).forEach(key => {
          if (Array.isArray(this.courseMap[key])) {
            this.courseMap[key].sort((a, b) => {
              const sortA = parseInt(a.sort) || 0
              const sortB = parseInt(b.sort) || 0
              return sortA - sortB
            })
          }
        })
      }
    },
    clearSelection() {
      this.$refs.multipleTable.clearSelection()
    },
    /** 勾选事件 */
    handleSelectionChange(val) {
      this.multipleSelection = val
      console.log('handleSelectionChange: ' + this.multipleSelection)
    },
    /** 编辑 */
    handleEdit(index, row) {
      this.form = row
      this.showDialog = true
      this.fileList = []
      this.file = undefined
      this.showUrl = true
      this.onlyUpload = false
    },
    /** 编辑 仅上传文件 */
    handleOnlyFile(index, row) {
      this.form = row
      this.showDialog = true
      this.fileList = []
      this.file = undefined
      this.showUrl = true
      this.onlyUpload = true
    },

    /** 删除单行 */
    async handleDeleteRow(index, row) {
      const params = {
        id: row.id
      }
      this.$confirm('此操作将永久删除该课程及其所有单词, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          deleteRecord(params).then(res => {
            if (res.head.ret === 0) {
              this.$message({
                type: 'success',
                message: '删除成功!'
              })
              this.getList()
            }
          })
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    },
    /** 删除单行 */
    async handleMultiDel() {
      this.$confirm('此操作将永久删除记录, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          let i = 0
          const len = this.multipleSelection.length
          for (i = 0; i < len; i++) {
            const params = {
              id: this.multipleSelection[i].id
            }
            deleteRecord(params).then(res => {
              if (res.head.ret === 0) {
                this.$message({
                  type: 'success',
                  message: '删除成功!'
                })
                this.getList()
              }
            })
          }
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    },

    // 表单编辑
    /** 保存 */
    submitForm() {
      this.$refs['ruleForm'].validate(valid => {
        if (valid) {
          this.loading = true
          console.log(this.file)
          if (!this.file) {
            this.saveCourse()
            return
          }
          oss.uploadFile(this.file, (result, error) => {
            if (error) {
              console.log('error')
              console.log(error)
              this.$message.error('上传文件失败')
              this.loading = false
              return
            }
            if (result) {
              this.form.url = result.url
              const url = result.url.replace('https://start-oss.oss-cn-hangzhou.aliyuncs.com', '')
              this.form.pics = {
                '200x264': url
              }
              this.saveCourse()
              this.getList()
            }
          })
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    async saveCourse() {
      const res = await saveRecord(this.form)
      if (res.head.ret === 0) {
        this.$message.success('保存成功')
      }
      this.loading = false
      this.showDialog = false
      this.onlyUpload = false
      this.getList()
    },

    /** 移除之前 */
    handleRemove(file, fileList) {
      console.log('handleRemove')
      this.showUrl = true
    },
    /** 上传前 */
    beforeUpload(file) {
      return false
    },
    changeFile(file) {
      console.log('changeFile')
      console.log(file)
      this.file = file.raw
      this.fileList.pop()
      this.fileList.push({
        name: file.name,
        url: file.name
      })
      this.showUrl = false
    }
  }
}
</script>

<style lang="scss" scoped>
.app-container {
  margin: 20px;
  .control-container {
    margin-bottom: 20px;
  }
  .choose-container {
    margin-bottom: 20px;
    background-color: #e6f7ff;
    border: 1px solid #91d5ff;

    span {
      margin-left: 20px;
      line-height: 48px;
    }
    a {
      margin-left: 20px;
      color: #1890ff;
      text-decoration: none;
      background-color: transparent;
      outline: none;
      cursor: pointer;
      transition: color 0.3s;
    }
  }
}
</style>
