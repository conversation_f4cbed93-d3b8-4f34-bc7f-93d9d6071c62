import request from '@/utils/request'
// 删除记录
const DELETE_RECORD_URL = '/platform/gam/community/v1/feedback/delete'
// 保存记录
const SAVE_RECORD_URL = '/platform/gam/community/v1/feedback/update'
// 列表
const getListUrl = '/platform/gam/community/v1/feedback/getpagelist'

export const fetchListByPage = async params => {
  return request({
    url: getListUrl,
    method: 'get',
    params
  })
}

/**
 * 删除记录
 * @param {String} id 记录id
 */
export const deleteRecord = async params => {
  return request({
    url: DELETE_RECORD_URL,
    method: 'get',
    params
  })
}

/**
 * 保存记录
 * @param ids 记录id
 */
export const saveRecord = async params => {
  return request({
    url: SAVE_RECORD_URL,
    method: 'post',
    data: params
  })
}
