import axios from '@/plugin/axios'

export const getFeedDetail = async feedid => {
  const params = {
    feedid: feedid,
    feedtype: 'feed'
  }
  const { data } = await axios.get('/api/gam/community/v1/feed/getfeednote', {
    params
  })
  return data
}

/** 关注状态获取 */
export const getFollowStatus = async friendid => {
  const params = {
    friendid: friendid
  }
  const { data } = await axios.get('/api/amigo/friend/v1/follow/getFollowStatus', {
    params
  })
  return data
}

export const addGiveLikefeed = async feedId => {
  const params = {
    feedId: feedId
  }
  const { data } = await axios.get('/api/gam/community/v1/feed/addGiveLikefeed', {
    params
  })
  return data
}

export const addShare = async feedId => {
  const params = {
    feedId: feedId
  }
  const { data } = await axios.get('/api/gam/community/v1/feed/addShare', {
    params
  })
  return data
}

export const delGiveLikefeed = async feedId => {
  const params = {
    feedId: feedId
  }
  const { data } = await axios.get('/api/gam/community/v1/feed/delGiveLikefeed', {
    params
  })
  return data
}

export const getMyInfo = async abc => {
  const { data } = await axios.get('/api/user/account/v1/user/getuserinfo')
  return data
}

/**
 * 获取动态评论列表
 * @param {*} feedid 动态id
 * @param {*} pageno 页码 从1开始
 * @param {*} pagesize 每页条数
 * @param {*} lastid 最后记录id
 */
export const getComments = async (feedid, pageno, pagesize, lastid) => {
  const params = {
    feedid,
    pageno,
    pagesize,
    lastid
  }
  const { data } = await axios.get(
    '/api/gam/community/v1/comment/getcommentlist',
    {
      params
    }
  )
  return data
}

/**
 * 删除评论
 * @param {String} id 评论id
 */
export const deleteCommentById = async id => {
  const params = {
    id
  }
  const { data } = await axios.get('/api/gam/community/v1/comment/delcomm', {
    params
  })
  return data
}
