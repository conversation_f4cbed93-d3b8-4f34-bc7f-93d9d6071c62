<!--
 * 办公素材管理页面，迁移自 material-a4，复用其交互和UI结构
-->
<template>
  <div class="app-container">
    <div class="list-contatiner">
      <div class="list-container-change">
        <el-radio-group
          v-model="currentId"
          size="small"
          style="margin-right: 20px"
          @change="handleMaterialChange"
        >
          <el-radio-button label="">全部</el-radio-button>
          <el-radio-button
            v-for="item in industryList"
            :key="item.code"
            :label="item.code"
            >{{ item.name }}</el-radio-button
          >
        </el-radio-group>
        <el-button type="primary" @click="handleAdd">新增办公素材</el-button>
      </div>
      <div class="list-container-data">
        <div class="list-container-data-cards">
          <div
            v-for="(item, index) in list"
            :key="index"
            class="list-container-data-cards-item"
          >
            <Card
              :item="item"
              @notifyUpdate="notifyUpdate"
              @notifyEdit="notifyEdit"
            />
          </div>
        </div>
        <div class="list-container-data-page">
          <el-pagination
            :total="page.total"
            :current-page="page.no"
            :page-size="page.size"
            layout="total, sizes, prev, pager, next, jumper"
            @current-change="handleCurrentChange"
            @size-change="handleSizeChange"
          />
        </div>
      </div>
    </div>
    <div class="dialog-container">
      <el-dialog
        :append-to-body="true"
        :title="currentTitile"
        :visible.sync="showAdd"
        center
        :show-close="false"
        :destroy-on-close="true"
      >
        <CommonForm
          v-if="currentView === 'CommonForm'"
          :m-id="currentId"
          :m-name="currentName"
          :form-data="currentForm"
          @handleBack="handleBack"
        />
        <ToDoListForm
          v-if="currentView === 'ToDoListForm'"
          :m-id="currentId"
          :m-name="currentName"
          :form-data="currentForm"
          @handleBack="handleBack"
        />
        <TextPopForm
          v-if="currentView === 'TextPopForm'"
          :m-id="currentId"
          :m-name="currentName"
          :form-data="currentForm"
          @handleBack="handleBack"
        />
        <FontForm
          v-if="currentView === 'FontForm'"
          :m-id="currentId"
          :m-name="currentName"
          :form-data="currentForm"
          @handleBack="handleBack"
        />
      </el-dialog>
    </div>
  </div>
</template>
<script>
import {
  fetchOfficeMaterialPageList,
  fetchOfficeMaterialDetail
} from '@/api/office/material'
import { fetchDictList } from '@/api/system/dict'
import Card from './component/Card/index'
import CommonForm from './component/CommonForm/index'
import ToDoListForm from './component/ToDoListForm/index'
import TextPopForm from './component/TextPopForm/index'
import FontForm from './component/FontForm/index'

export default {
  components: {
    Card,
    CommonForm,
    ToDoListForm,
    TextPopForm,
    FontForm
  },
  data() {
    return {
      list: [],
      industryList: [],
      page: {
        size: 20,
        no: 1,
        total: 0
      },
      showAdd: false,
      currentView: 'CommonForm',
      currentForm: {},
      currentTitile: '办公素材编辑',
      currentId: '',
      currentName: ''
    }
  },
  async mounted() {
    // 先获取行业数据，然后查询列表
    await this.fetchIndustryData()
    this.fetchList()
  },
  methods: {
    handleAdd() {
      this.industryList.forEach(m => {
        if (m.code === this.currentId) {
          this.currentName = m.name
          this.currentTitile = m.name
        }
      })
      this.currentForm = {
        mId: this.currentId
      }
      this.currentTitile = this.currentTitile + '新增'
      this.currentView = 'CommonForm'
      this.showAdd = true
    },
    notifyUpdate() {
      this.fetchList()
    },
    async notifyEdit(item) {
      await this.getDetail(item.id)
    },
    async getDetail(id) {
      try {
        const res = await fetchOfficeMaterialDetail({ id })
        if (res.head && res.head.ret === 0) {
          this.currentForm = res.data
          this.currentForm.id = id
          
          // 修改这里：使用素材自身的category而不是当前选中的currentId
          const materialCategory = res.data.category || ''
          this.industryList.forEach(m => {
            if (m.code === materialCategory) {
              this.currentName = m.name
              this.currentTitile = m.name
            }
          })
          
          this.currentTitile = this.currentTitile + '编辑'
          this.currentView = 'CommonForm'
          this.showAdd = true
        }
      } catch (error) {
        console.error('获取办公素材详情失败:', error)
      }
    },
    handleBack(val) {
      console.log('handleBack called with val:', val, 'type:', typeof val)
      this.currentForm = {}
      this.showAdd = false
      if (val === '1') {
        console.log('新增/编辑成功，开始刷新列表...')
        // 重置到第一页，确保能看到新增的数据
        this.page.no = 1
        this.fetchList()
      }
    },
    handleCurrentChange(val) {
      this.page.no = val
      this.fetchList()
    },
    handleSizeChange(val) {
      this.page.size = val
      this.fetchList()
    },

    handleMaterialChange() {
      this.page = {
        size: 20,
        no: 1,
        total: 0
      }
      this.industryList.forEach(m => {
        if (m.code === this.currentId) {
          this.currentName = m.name
          this.currentTitile = m.name
        }
      })
      this.fetchList()
    },
    async fetchList() {
      try {
        const params = {
          pageno: this.page.no,
          pagesize: this.page.size
        }
        if (this.currentId) {
          params.category = this.currentId
        }

        console.log('fetchList 请求参数:', params)
        const res = await fetchOfficeMaterialPageList(params)
        console.log('fetchList 响应结果:', res)
        if (res.head && res.head.ret === 0) {
          this.list = (res.data && res.data.result) || []
          this.page.total = (res.data && res.data.totalCount) || 0
          console.log('列表更新成功，当前数据量:', this.list.length, '总数:', this.page.total)
        }
      } catch (error) {
        console.error('获取办公素材列表失败:', error)
      }
    },

    async fetchIndustryData() {
      try {
        const res = await fetchDictList({ type: 'industry' })
        if (res.head && res.head.ret === 0) {
          this.industryList = res.data || []
          // 默认选中"全部"
          this.currentId = ''
        }
      } catch (error) {
        console.error('获取行业列表失败:', error)
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.app-container {
  .list-contatiner {
    .list-container-data {
      &-cards {
        margin-top: 20px;
        margin-bottom: 20px;
        display: flex;
        flex-flow: row wrap;
        justify-content: flex-start;
        &-item {
          width: 15%;
          margin-left: 1%;
          border: 1px solid grey;
          margin-bottom: 10px;
        }
      }
    }
  }
}
</style>
