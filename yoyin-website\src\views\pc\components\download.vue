<template>
  <div>
      <div class="title">
        <img src="https://m.yoyin.net/h5/img/web/download/download-pc-title.png" style="width:314px;height:74px">
      </div>
      <div class="download">
        <div class="item item1">
          <div class="item-image">
            <img src="https://m.yoyin.net/h5/img/web/download/print-proc.png" style="width:165px;height:158px">
          </div>
          <div class="item-center">
            <div class="item-center-title">PRO打印机</div>
            <div class="item-center-center">
              <div>适用型号：TP4<br>更新时间：2021/9/9</div>
              <div style="padding-left: 40px;">版本号：1231</div>
            </div>
            <div class="item-center-footer">
              <div class="item-center-footer-button" @click="download(1)"><img src="https://m.yoyin.net/h5/img/web/download/drive_win.png" style="width:25px; height:25px;padding-right:10px;">Win</div>
              <!-- <div class="item-center-footer-button" @click="download(2)"><img src="https://m.yoyin.net/h5/img/web/download/drive_mac.png" style="width:25px; height:25px;padding-right:10px;">Mac</div> -->
            </div>
          </div>
        </div>

        <div class="item">
          <div class="item-image">
            <img src="https://m.yoyin.net/h5/img/web/download/print-a4.png" style="width:165px;height:158px">
          </div>
          <div class="item-center">
            <div class="item-center-title">A4打印机</div>
            <div class="item-center-center">
              <div>适用型号：TP8<br>更新时间：2021/9/9</div>
              <div style="padding-left: 40px;">版本号：2321</div>
            </div>
            <div class="item-center-footer">
              <div class="item-center-footer-button" @click="download(3)"><img src="https://m.yoyin.net/h5/img/web/download/drive_win.png" style="width:25px; height:25px;padding-right:10px;">Win</div>
              <div class="item-center-footer-button" @click="download(4)"><img src="https://m.yoyin.net/h5/img/web/download/drive_mac.png" style="width:25px; height:25px;padding-right:10px;">Mac</div>
            </div>
          </div>
        </div>
      </div>

      <div class="downlaod-app">
        <img src="https://m.yoyin.net/h5/img/web/download/download-app.png" style="width:1296px; height:1186px">
      </div>
  </div>
</template>

<script>
export default {
  name: 'Download',
  methods: {
    download (index) {
      if (index === 1) {
        document.location.href = 'https://m.yoyin.net/h5/download/xprinter-tp4-driver.zip'
      } else if (index === 3) {
        document.location.href = 'https://m.yoyin.net/h5/download/xprinter-a4-driver.zip'
      } else if (index === 4) {
        document.location.href = 'https://m.yoyin.net/h5/download/net.xprinter.drv_3.0.8.pkg'
      }
    }
  }
}
</script>

<style scoped>
.title {
  padding-top: 120px;
  padding-bottom: 50px;
}
.download {
  display: flex;
  justify-content: center;
  align-items: center;
  padding-bottom: 80px;
}

.item {
  width: 590px;
  height: 243px;
  background: #FFFFFF;
  box-shadow: 0px 3px 30px 1px rgba(0, 0, 0, 0.07);
  border-radius: 10px 10px 10px 10px;
  display: flex;
  text-align: left;
}

.item1 {
  margin-right: 20px;
}

.item-image {
  margin: 43px;
}

.item-center-center {
  display: flex;
  font-size: 14px;
  color: #222222;
}
.item-center-title {
  margin: 43px 0 15px 0;
  font-size: 24px;
  line-height: 33px;
  color: #222222;
  font-weight: 600;
}

.item-center-footer {
  display: flex;
  margin: 43px 0;
  text-align: left;
}
.item-center-footer-button {
  width: 110px;
  height: 42px;
  border-radius: 63px 63px 63px 63px;
  opacity: 1;
  border: 1px solid #63C184;
  color: #63C184;
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center;
  line-height: 43px;
  margin-right: 10px;
}

</style>
