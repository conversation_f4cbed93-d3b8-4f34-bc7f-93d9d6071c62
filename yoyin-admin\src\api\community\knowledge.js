import request from '@/utils/request'

// 翻页查询
const LIST_URL = '/platform/gam/community/v1/knowledge/getknowledgelist'
const DELETE_RECORD_URL = '/platform/gam/community/v1/knowledge/delknowledge'
const SAVE_URL = '/platform/gam/community/v1/knowledge/saveorupdate'
const SUBJECT_URL = '/platform/gam/community/v1/knowledge/getSubjectList'
const EXPORT_URL = '/platform/gam/community/v1/knowledge/exportknowledgelist'
// 删除记录

/**
 * 查询列表数据
 * @param params 参数
 */
export const fetchListByPage = async params => {
  return request({
    url: LIST_URL,
    method: 'get',
    params: params
  })
}

/**
 * 删除记录
 * @param {String} id 记录id
 */
export const deleteRecord = async params => {
  return request({
    url: DELETE_RECORD_URL,
    method: 'get',
    params
  })
}

/** 保存记录 */
export const saveRecord = async params => {
  return request({
    url: SAVE_URL,
    method: 'post',
    data: params
  })
}

export const getSubject = async params => {
  return request({
    url: SUBJECT_URL,
    method: 'get'
  })
}

export const exportKnowledgeList = async params => {
  return request({
    url: EXPORT_URL,
    method: 'get',
    params: params
  })
}
