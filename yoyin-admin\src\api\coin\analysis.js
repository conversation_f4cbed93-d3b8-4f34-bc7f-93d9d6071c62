import request from '@/utils/request'

// 翻页查询
const LIST_URL = '/platform/gam/community/v1/coin/getcounttaskbybetweendateandtype'
const EXPORT_URL = '/platform/gam/community/v1/coin/exportcounttaskbybetweendateandtype'
const SAVE_URL = '/platform/gam/community/v1/coin/incrbycustom'
const RANK_LIST_URL = '/platform/gam/community/v1/coin/getrankcoinsbybetweendateandtype'
const RANK_EXPORT_URL = '/platform/gam/community/v1/coin/exportrankcoinsbybetweendateandtype'
const BASE_url = '/platform/gam/community/v1/coin/'
// 删除记录

/**
 * 查询列表数据
 * @param params 参数
 */
export const fetchCountListByPage = async params => {
  return request({
    url: LIST_URL,
    method: 'get',
    params: params
  })
}

export const exportCountListByPage = async params => {
  return request({
    url: EXPORT_URL,
    method: 'get',
    params: params
  })
}

/** 自定义发送积分奖励 */
export const giveCoinCurrRecord = async params => {
  return request({
    url: SAVE_URL,
    method: 'post',
    data: params
  })
}

/**
 * 查询列表数据
 * @param params 参数
 */
export const fetchRankListByPage = async params => {
  return request({
    url: RANK_LIST_URL,
    method: 'get',
    params: params
  })
}

export const exportRankListByPage = async params => {
  return request({
    url: RANK_EXPORT_URL,
    method: 'get',
    params: params
  })
}

/**
 * 统计积分任务每日的完成情况，统计所有用户
 */
export const getStatisticsMissionFinish = async params => {
  return request({
    url: BASE_url + 'getstatisticsmissionfinish',
    method: 'get',
    params: params
  })
}

/**
 * 统计积分任务每日的完成情况，统计所有用户（导出）
 */
export const exportStatisticsMissionFinish = async params => {
  return request({
    url: BASE_url + 'exportstatisticsmissionfinish',
    method: 'get',
    params: params
  })
}

/**
 * 查询积分商城的商品兑换情况
 */
export const getStatisticsCoinGoodsChangePage = async params => {
  return request({
    url: BASE_url + 'getstatisticsgoodschangepage',
    method: 'get',
    params: params
  })
}

/**
 * 查询积分商城的商品兑换情况（导出）
 */
export const getStatisticsCoinGoodsChangeList = async params => {
  return request({
    url: BASE_url + 'getstatisticsgoodschangelist',
    method: 'get',
    params: params
  })
}

/**
 * 积分余额排名
 */
export const getStatisticsUserCoinRankList = async params => {
  return request({
    url: BASE_url + 'getstatisticsusercoinranklist',
    method: 'get',
    params: params
  })
}

/**
 * 积分余额排名（导出）
 */
export const exportStatisticsUserCoinRankList = async params => {
  return request({
    url: BASE_url + 'exportstatisticsusercoinranklist',
    method: 'get',
    params: params
  })
}

