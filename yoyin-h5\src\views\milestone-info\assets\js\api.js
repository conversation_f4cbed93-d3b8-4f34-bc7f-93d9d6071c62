import axios from '@/plugin/axios'

/** 获取当前用户当前月份的打印数 */
export const getInfoByUserId = async userId => {
  const params = {
    // userId: userId
  }
  const { data } = await axios.get('/api/gam/community/v1/mileage/rankinguser', {
    params
  })
  return data
}

/** 获取所有用户当前月份的打印长度的排名前20 */
export const getRankingMonth = async userId => {
  const params = {
    // friendid: friendid
  }
  const { data } = await axios.get('/api/gam/community/v1/mileage/rankingmonth', {
    params
  })
  return data
}

/** 获取所有用户当前月份的打印长度的排名前20 */
export const getRankingYear = async userId => {
  const params = {
    // friendid: friendid
  }
  const { data } = await axios.get('/api/gam/community/v1/mileage/rankingyear', {
    params
  })
  return data
}

/** 获取所有用户的总打印长度的排名前20 */
export const getRankingTotal = async userId => {
  const params = {
    // friendid: friendid
  }
  const { data } = await axios.get('/api/gam/community/v1/mileage/rankingtotal', {
    params
  })
  return data
}

/** 获取所有用户的总打印长度的排名前20 */
export const getUserInfo = async userId => {
  const params = {
    // friendid: friendid
  }
  const { data } = await axios.get('/api/user/account/v1/user/getuserinfo', {
    params
  })
  return data
}
