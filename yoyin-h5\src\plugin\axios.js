import axios from 'axios'
import { BASE_URL, TOKEN_KEY, getUserAgent, USER_AGENT } from '@/consts/index'
import { getUrlParameter } from '@/utils/common'
import Cookies from 'js-cookie'

// 无效token
const TOKEN_INVALID = 2
// token为空
const TOKEN_NULL = 3
// 重试次数
const retries = 3
const namespace = 'STAR_PRINTER_AUTH'

/**
 * 获取当前状态
 * @param {object} config axios 配置
 */
function _getCurrentState(config) {
  const currentState = config[namespace] || {}
  currentState.retryCount = currentState.retryCount || 0
  config[namespace] = currentState
  return currentState
}

const options = {
  baseURL: BASE_URL, // 默认请求API网关
  timeout: 10000
}
// 创建一个axios实例
const instance = axios.create(options)

/**
 * 设置请求头
 */
instance.interceptors.request.use(config => {
  const currentState = _getCurrentState(config)
  currentState.lastRequestTime = Date.now()
  config.headers['Content-Type'] = 'application/json;charset=UTF-8'

  // TODO 通过域名判断哪些页面加白无需授权登录

  // 方便在浏览器中调试
  let flag = 'OS'
  if (window.StarPrinterJS) {
    config.headers[TOKEN_KEY] = window.StarPrinterJS.getToken()
  } else if (window.webkit && window.webkit.messageHandlers) {
    // token直接从localStorage中获取
    // window.webkit.messageHandlers.getToken.postMessage(null)
    // config.headers[TOKEN_KEY] = window.localStorage.getItem(TOKEN_KEY)
    config.headers[TOKEN_KEY] = Cookies.get('token')
  } else {
    flag = 'NOS'
  }
  config.headers[USER_AGENT] = getUserAgent(flag)
  return config
})

/**
 * 处理失效的用户token，对于失效的token，需要刷新token，再发起请求
 * 重试次数3次，超过重试次数则提示用户登录会话过期，让用户重新登录
 */
instance.interceptors.response.use(async res => {
  // 只处理非登录请求的失效token的response
  // console.log(res.headers)
  if (res.data.head.ret !== TOKEN_INVALID && res.data.head.ret !== TOKEN_NULL) {
    return res
  }

  const config = res.config
  const currentState = _getCurrentState(config)
  if (currentState.retryCount >= retries) {
    // 跳转去登录页面
    return res
  } else {
    currentState.retryCount++
    // console.log(`会话过期，重新发起请求，重试次数：${currentState.retryCount}`)
    if (window.StarPrinterJS) {
      await window.StarPrinterJS.quickLogin()
      console.log('您调用了weindow.StarPrinterJS.quickLogin')
    } else if (window.webkit && window.webkit.messageHandlers) {
      config.headers[TOKEN_KEY] = Cookies.get('token')
      // window.webkit.messageHandlers.loadFail.postMessage(0)
      // await window.webkit.messageHandlers.quickLogin.postMessage(null)
      console.log('您调用了window.webkit.messageHandlers.quickLogin')
    }
    return new Promise(resolve => setTimeout(() => resolve(instance(config)), 500))
  }
})

export default instance
