<!--
 * @Description: In User Settings Edit
 * @Author: your name
 * @Date: 2019-10-16 11:28:20
 * @LastEditTime: 2019-10-22 17:03:31
 * @LastEditors: Please set LastEditors
 -->
<template>
  <div class="app-container">
    <div class="control-container">
      <div class="myitems">
        <div class="myitems-1">学科：</div>
        <div class="myitems-2">
          <el-radio-group
            v-model="subject"
            size="small"
            @change="refreshTypesDisplay"
          >
            <el-radio-button key="全部" label="">全部</el-radio-button>
            <el-radio-button
              v-for="item in subjectData"
              :key="item.name"
              :label="item.id"
              >{{ item.name }}</el-radio-button
            >
          </el-radio-group>
        </div>
      </div>

      <div class="myitems">
        <div class="myitems-1">学年：</div>
        <div class="myitems-2">
          <el-radio-group
            v-model="gradeLevel"
            size="small"
            @change="refreshTypesDisplay"
          >
            <el-radio-button key="全部" label="">全部</el-radio-button>
            <el-radio-button
              v-for="item in gradeLevelData"
              :key="item.name"
              :label="item.id"
              >{{ item.name }}</el-radio-button
            >
          </el-radio-group>
        </div>
      </div>
      <div class="myitems">
        <div class="myitems-1">类型：</div>
        <div class="myitems-2">
          <el-radio-group
            v-model="examType"
            size="small"
            @change="refreshTypesDisplay"
          >
            <el-radio-button key="全部" label="">全部</el-radio-button>
            <el-radio-button
              v-for="item in examTypeData"
              :key="item.name"
              :label="item.id"
              >{{ item.name }}</el-radio-button
            >
          </el-radio-group>
        </div>
      </div>
      <p>
        <el-button type="primary" @click="handleQuery">查询</el-button>
        <el-button
          v-permission="'btn-menuA4-examination-edit'"
          @click="handleAdd"
          >添加</el-button
        >
      </p>
    </div>

    <div class="table-container">
      <div
        v-for="(item, index) in tableList"
        :key="item.id"
        class="items"
        :offset="index > 0 ? 2 : 0"
      >
        <img :src="item.headUrl" class="image" />
        <div style="padding: 14px">
          <div class="span">标题：{{ item.title }}</div>
          <div class="span">科目：{{ item.subjectName }}</div>
          <div class="span">学年：{{ item.gradeLevelName }}</div>
          <div class="span">类型：{{ item.examTypeName }}</div>
          <div class="bottom">
            <el-button size="mini" @click="handleEdit(index, item)"
              >编辑</el-button
            >
            <el-button
              v-permission="'btn-menuA4-examination-edit'"
              size="mini"
              type="danger"
              @click="handleDeleteRow(index, item)"
              >删除</el-button
            >
          </div>
        </div>
      </div>
    </div>
    <div class="pagination-container" style="margin-top: 20px">
      <el-pagination
        :current-page="currentPage"
        :page-sizes="[10, 20, 30, 40]"
        :page-size="currentSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
    <!-- 弹框 -->
    <div class="dialog-container">
      <el-dialog
        :title="form.id === '' ? '新增' : '编辑'"
        :visible.sync="showDialog"
        width="50%"
        center
        :destroy-on-close="true"
      >
        <el-form
          ref="ruleForm"
          :model="form"
          :rules="rules"
          label-width="100px"
          class="demo-ruleForm"
        >
          <el-form-item label="名称" prop="title">
            <el-input v-model="form.title" />
          </el-form-item>
          <el-form-item label="科目" prop="subject">
            <el-checkbox-group v-model="subjectArr" size="small">
              <el-checkbox-button
                v-for="item in subjectData"
                :key="item.name"
                :label="item.id"
                >{{ item.name }}</el-checkbox-button
              >
            </el-checkbox-group>
          </el-form-item>
          <el-form-item label="年级" prop="gradeLevel">
            <el-checkbox-group v-model="gradeLevelArr" size="small">
              <el-checkbox-button
                v-for="item in gradeLevelData"
                :key="item.name"
                :label="item.id"
                >{{ item.name }}</el-checkbox-button
              >
            </el-checkbox-group>
          </el-form-item>
          <el-form-item label="类型" prop="examType">
            <el-checkbox-group v-model="examTypeArr" size="small">
              <el-checkbox-button
                v-for="item in examTypeData"
                :key="item.id"
                :label="item.id"
                >{{ item.name }}</el-checkbox-button
              >
            </el-checkbox-group>
          </el-form-item>
          <el-form-item label="封面">
            <SingleUpload
              key="listUrl"
              key-word="listUrl"
              :init-url="form.headUrl && form.headUrl ? form.headUrl : ''"
              @updateFile="updateFile"
            />
          </el-form-item>
          <el-form-item label="上传文件">
            <PdfUpload
              key="listUrl"
              key-word="listUrl"
              :init-url="form.fileUrl && form.fileUrl ? form.fileUrl : ''"
              @updateFile="updateFile2"
            />
          </el-form-item>
          <el-form-item>
            <el-button @click="showDialog = false">返回</el-button>
            <el-button
              v-permission="'btn-menuA4-examination-edit'"
              type="primary"
              :loading="loading"
              @click="submitForm"
              >保存</el-button
            >
          </el-form-item>
        </el-form>
      </el-dialog>
    </div>
  </div>
</template>
<script>
import { fetchListByPage, deleteRecord, saveRecord } from '@/api/community/examination'
import SingleUpload from '@/views/community/material/component/SingleUpload'
import PdfUpload from '@/views/community/material/component/PdfUpload'
import AliyunOSS from '@/utils/aliyunOSS'
const oss = new AliyunOSS()
oss.initClient()
// const TIP_MESSAGES = {
//   listUrl: '请选择图'
// }
export default {
  components: { SingleUpload, PdfUpload },
  data() {
    return {
      examType: '',
      gradeLevel: '',
      subject: '',
      currentPage: 1,
      currentSize: 10,
      total: 100,
      tableList: [],
      typeData: [],
      multipleSelection: [],
      // 弹框
      showDialog: false,
      uploadFiles: { picVo: {} },
      // 表单
      form: {
        id: '',
        fileUrl: '',
        headUrl: ''
      },
      // 检验规则
      rules: {
        channel: [
          { required: true, message: '请输入渠道', trigger: 'blur' },
          { min: 3, max: 20, message: '长度在 3 到 20 个字符', trigger: 'blur' }
        ],
        url: [{ required: true, message: '请选择文件', trigger: 'blur' }],
        version: [
          { required: true, message: '请输入版本号如1.1.2', trigger: 'blur' }
        ]
      },
      fileList: [],
      file: undefined,
      file2: undefined,
      loading: false,
      showUrl: false,
      examTypeArr: [],
      examTypeData: [{
        'id': 'yuekao',
        'name': '月考试卷'
      }, {
        'id': 'qizhong',
        'name': '期中考试'
      }, {
        'id': 'qimo',
        'name': '期末考试'
      }, {
        'id': 'xiaoshengchu',
        'name': '小升初复习'
      }, {
        'id': 'zhongkao',
        'name': '中考复习'
      }, {
        'id': 'gaokao',
        'name': '高考复习'
      }, {
        'id': 'other',
        'name': '其他'
      }],
      gradeLevelArr: [],
      gradeLevelData: [{
        'id': '01',
        'name': '小学一年级'
      }, {
        'id': '02',
        'name': '小学二年级'
      }, {
        'id': '03',
        'name': '小学三年级'
      }, {
        'id': '04',
        'name': '小学四年级'
      }, {
        'id': '05',
        'name': '小学五年级'
      }, {
        'id': '06',
        'name': '小学六年级'
      }, {
        'id': '07',
        'name': '初一'
      }, {
        'id': '08',
        'name': '初二'
      }, {
        'id': '09',
        'name': '初三'
      }, {
        'id': '10',
        'name': '高一'
      }, {
        'id': '11',
        'name': '高二'
      }, {
        'id': '12',
        'name': '高三'
      }],
      subjectArr: [],
      subjectData: [{
        'id': 'Chinese',
        'name': '语文'
      }, {
        'id': 'Mathematics',
        'name': '数学'
      }, {
        'id': 'English',
        'name': '英语'
      }, {
        'id': 'Biology',
        'name': '生物'
      }, {
        'id': 'Politics',
        'name': '政治'
      }, {
        'id': 'History',
        'name': '历史'
      }, {
        'id': 'Geography',
        'name': '地理'
      }, {
        'id': 'Physics',
        'name': '物理'
      }, {
        'id': 'Chemistry',
        'name': '化学'
      }, {
        'id': 'Daofa',
        'name': '道德与法制'
      }, {
        'id': 'Science',
        'name': '科学'
      }]
    }
  },

  computed: {
    /** 编辑的数据 */
    files() {
      if (this.form && this.form.picVo) {
        console.log('files=', this.form.picVo)
        return this.form.picVo
      } else {
        return {}
      }
    }
  },
  mounted() {
    this.getList()
  },
  methods: {
    refreshTypesDisplay(e) {
      this.getList()
    },
    /** 添加 */
    handleAdd() {
      // 1. 重置表单数据对象
      this.form = {
        id: '', // 确保 id 为空，表示是新增操作
        title: '', // 清空标题
        subject: '', // 清空后台需要的字段
        subjectName: '', // 清空后台需要的字段
        gradeLevel: '', // 清空后台需要的字段
        gradeLevelName: '', // 清空后台需要的字段
        examType: '', // 清空后台需要的字段
        examTypeName: '', // 清空后台需要的字段
        fileUrl: '', // 清空文件 URL
        headUrl: ''  // 清空封面 URL
        // 如果 form 对象中还有其他需要在添加时清空的字段，也在此处添加并设置为空或默认值
      };

      // 2. 清空用于 el-checkbox-group 的数组模型
      this.subjectArr = [];
      this.gradeLevelArr = [];
      this.examTypeArr = [];

      // 3. 重置文件上传相关状态
      this.fileList = []; // 如果 fileList 用于显示已上传列表，也清空
      this.file = undefined; // 清空封面文件对象
      this.file2 = undefined; // 清空 PDF 文件对象
      this.showUrl = false; // 重置 URL 显示状态（如果需要）

      // 4. 显示弹窗
      this.showDialog = true;
    },
    /** 查询 */
    handleQuery() {
      this.getList()
    },
    /** 切换每页条数 */
    handleSizeChange(val) {
      console.log(`每页 ${val} 条`)
      this.currentSize = val
      this.getList()
    },
    /** 切换当前页 */
    handleCurrentChange(val) {
      console.log(`当前页: ${val}`)
      this.currentPage = val
      this.getList()
    },
    /** 获取表数据 */
    async getList() {
      const params = {
        pageno: this.currentPage,
        pagesize: this.currentSize,
        examType: this.examType,
        subject: this.subject,
        gradeLevel: this.gradeLevel
      }
      const res = await fetchListByPage(params)
      if (res.head.ret === 0) {
        const data = res.data
        this.total = data.totalCount
        this.tableList = data.result
      }
    },
    clearSelection() {
      this.$refs.multipleTable.clearSelection()
    },
    /** 勾选事件 */
    handleSelectionChange(val) {
      this.multipleSelection = val
      console.log('handleSelectionChange: ' + this.multipleSelection)
    },
    /** 编辑 */
    handleEdit(index, row) {
      this.form = row
      console.log('handleEdit=', this.form)
      // 初始化   年级、类型、科目
      if (this.form.subject) {
        this.subjectArr = this.form.subject.split(';')
      } else {
        this.subjectArr = []
      }
      if (this.form.examType) {
        this.examTypeArr = this.form.examType.split(';')
      } else {
        this.examTypeArr = []
      }
      if (this.form.gradeLevel) {
        this.gradeLevelArr = this.form.gradeLevel.split(';')
      } else {
        this.gradeLevelArr = []
      }

      this.showDialog = true
    },

    /** 删除单行 */
    async handleDeleteRow(index, row) {
      const params = {
        id: row.id
      }
      this.$confirm('此操作将永久删除该记录, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          deleteRecord(params).then(res => {
            if (res.head.ret === 0) {
              this.$message({
                type: 'success',
                message: '删除成功!'
              })
              this.getList()
            }
          })
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    },
    /** 删除单行 */
    async handleMultiDel() {
      this.$confirm('此操作将永久删除记录, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          let i = 0
          const len = this.multipleSelection.length
          for (i = 0; i < len; i++) {
            const params = {
              id: this.multipleSelection[i].id
            }
            deleteRecord(params).then(res => {
              if (res.head.ret === 0) {
                this.$message({
                  type: 'success',
                  message: '删除成功!'
                })
                this.getList()
              }
            })
          }
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    },
    updateFile(key, file) {
      this.file = file
    },
    updateFile2(key, file) {
      this.file2 = file
    },
    // 表单编辑
    /** 保存 */
    submitForm() {
      this.loading = true
      if (!this.file && !this.file2) {
        if (this.form.headUrl && this.form.fileUrl) {
          this.saveConfig()
        } else {
          this.$message.error('请上传封面和实体pdf文件')
          this.loading = false
        }
        return
      }

      if (this.file && this.file2) {
        console.log('this.file = ', this.file)
        oss.uploadFile(this.file, (result, error) => {
          if (error) {
            console.log('error')
            console.log(error)
            this.$message.error('上传文件失败')
            this.loading = false
            return
          }
          if (result) {
            this.form.headUrl = result.url
            oss.uploadFile(this.file2, (result2, error2) => {
              if (error2) {
                console.log('error')
                console.log(error2)
                this.$message.error('上传文件失败')
                this.loading = false
                return
              }
              if (result2) {
                this.form.fileUrl = result2.url
                if (this.loading) {
                  this.saveConfig()
                }

                return
              }
            })
          }
        })
      } else if (this.file) {
        console.log('this.form1', this.form)
        if (this.form.fileUrl) {
          oss.uploadFile(this.file, (result, error) => {
            if (error) {
              console.log('error')
              console.log(error)
              this.$message.error('上传文件失败')
              this.loading = false
              return
            }
            if (result) {
              this.form.headUrl = result.url
              if (this.loading) {
                this.saveConfig()
              }
              return
            }
          })
        } else {
          this.$message.error('请选择上传实体pdf文件')
          this.loading = false
          return
        }
      } else {
        if (this.form.headUrl) {
          oss.uploadFile(this.file2, (result, error) => {
            if (error) {
              console.log('error')
              console.log(error)
              this.$message.error('上传文件失败')
              this.loading = false
              return
            }
            if (result) {
              this.form.fileUrl = result.url
              if (this.loading) {
                this.saveConfig()
              }
              return
            }
          })
        } else {
          this.$message.error('请选择上传封面')
          this.loading = false
          return
        }
      }
    },
    async saveConfig() {
      if (this.subjectArr && this.subjectArr.length > 0) {
        let ids = ''
        let names = ''
        this.subjectArr.forEach(element => {
          this.subjectData.forEach(item => {
            if (item.id === element) {
              ids += item.id + ';'
              names += item.name + ';'
            }
          })
        })
        this.form.subject = ids
        this.form.subjectName = names
      }

      if (this.examTypeArr && this.examTypeArr.length > 0) {
        let ids = ''
        let names = ''
        this.examTypeArr.forEach(element => {
          this.examTypeData.forEach(item => {
            if (item.id === element) {
              ids += item.id + ';'
              names += item.name + ';'
            }
          })
        })
        this.form.examType = ids
        this.form.examTypeName = names
      }

      if (this.gradeLevelArr && this.gradeLevelArr.length > 0) {
        let ids = ''
        let names = ''
        this.gradeLevelArr.forEach(element => {
          this.gradeLevelData.forEach(item => {
            if (item.id === element) {
              ids += item.id + ';'
              names += item.name + ';'
            }
          })
        })
        this.form.gradeLevel = ids
        this.form.gradeLevelName = names
      }

      const res = await saveRecord(this.form)
      if (res.head.ret === 0) {
        this.$message.success('保存成功')
      }
      this.loading = false
      this.showDialog = false
      this.onlyUpload = false
      this.getList()
    },

    /** 移除之前 */
    handleRemove(file, fileList) {
      console.log('handleRemove')
      this.showUrl = true
    },
    /** 上传前 */
    beforeUpload(file) {
      return false
    },
    changeFile(file) {
      console.log('changeFile')
      console.log(file)
      this.file = file.raw
      this.fileList.pop()
      this.fileList.push({
        name: file.name,
        url: file.name
      })
      this.showUrl = false
    }
  }
}
</script>

<style lang="scss" scoped>
.app-container {
  margin: 20px;
  .control-container {
    margin-bottom: 20px;
  }
  .choose-container {
    margin-bottom: 20px;
    background-color: #e6f7ff;
    border: 1px solid #91d5ff;

    span {
      margin-left: 20px;
      line-height: 48px;
    }
    a {
      margin-left: 20px;
      color: #1890ff;
      text-decoration: none;
      background-color: transparent;
      outline: none;
      cursor: pointer;
      transition: color 0.3s;
    }
  }
}

.table-container {
  display: flex;
  flex-wrap: wrap;
  .items {
    height: 350px;
    width: 200px;
    margin-top: 15px;
    padding-right: 15px;
    margin-right: 15px;
    margin-left: 15px;
    padding-bottom: 15px;
    border-radius: 15px;
    text-align: center;
    border: solid 1px;
    .image {
      width: 170px;
      object-fit: fill;
      height: 180px;
      margin: 15px 15px;
    }
    .bottom {
      text-align: center;
    }
    .span {
      font-size: 12px;
      width: 180px;
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
      text-align: left;
    }
  }
}

.myitems {
  display: flex;
  line-height: 40px;
  &-1 {
    min-width: 50px;
  }
  &-2 {
    margin: 0 15px;
  }
}
</style>
