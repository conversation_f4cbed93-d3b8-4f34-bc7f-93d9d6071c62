{"code": "(window[\"webpackJsonp\"]=window[\"webpackJsonp\"]||[]).push([[\"chunk-e9d3ec58\"],{\"04bf\":function(e,t,a){},\"1cf5\":function(e,t,a){\"use strict\";a(\"04bf\")},\"37f7\":function(e,t,a){\"use strict\";var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a(\"div\",{staticClass:\"vue-hover-mask\"},[e._t(\"default\"),e._v(\" \"),a(\"span\",{staticClass:\"vue-hover-mask_action\",on:{click:e.handleClick}},[e._t(\"action\")],2)],2)},r=[],n={name:\"HoverMask\",methods:{handleClick:function(){this.$emit(\"click\")}}},l=n,s=(a(\"b6ba\"),a(\"2877\")),o=Object(s[\"a\"])(l,i,r,!1,null,null,null);t[\"a\"]=o.exports},\"4c6b\":function(e,t,a){\"use strict\";a.d(t,\"a\",(function(){return i}));var i=\"https://test.starpany.cn\"},\"579e\":function(e,t,a){\"use strict\";a.r(t);var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a(\"div\",{staticClass:\"app-container\"},[a(\"div\",{staticClass:\"list-contatiner\"},[a(\"div\",{staticClass:\"list-container-change\"},[a(\"el-radio-group\",{staticStyle:{\"margin-right\":\"20px\"},attrs:{size:\"small\"},on:{change:e.handleMaterialChange},model:{value:e.currentId,callback:function(t){e.currentId=t},expression:\"currentId\"}},[a(\"el-radio-button\",{attrs:{label:\"\"}},[e._v(\"全部\")]),e._v(\" \"),e._l(e.industryList,(function(t){return a(\"el-radio-button\",{key:t.code,attrs:{label:t.code}},[e._v(e._s(t.name))])}))],2),e._v(\" \"),a(\"el-button\",{attrs:{type:\"primary\"},on:{click:e.handleAdd}},[e._v(\"新增办公素材\")])],1),e._v(\" \"),a(\"div\",{staticClass:\"list-container-data\"},[a(\"div\",{staticClass:\"list-container-data-cards\"},e._l(e.list,(function(t,i){return a(\"div\",{key:i,staticClass:\"list-container-data-cards-item\"},[a(\"Card\",{attrs:{item:t},on:{notifyUpdate:e.notifyUpdate,notifyEdit:e.notifyEdit}})],1)})),0),e._v(\" \"),a(\"div\",{staticClass:\"list-container-data-page\"},[a(\"el-pagination\",{attrs:{total:e.page.total,\"current-page\":e.page.no,\"page-size\":e.page.size,layout:\"total, sizes, prev, pager, next, jumper\"},on:{\"current-change\":e.handleCurrentChange,\"size-change\":e.handleSizeChange}})],1)])]),e._v(\" \"),a(\"div\",{staticClass:\"dialog-container\"},[a(\"el-dialog\",{attrs:{\"append-to-body\":!0,title:e.currentTitile,visible:e.showAdd,center:\"\",\"show-close\":!1,\"destroy-on-close\":!0},on:{\"update:visible\":function(t){e.showAdd=t}}},[\"CommonForm\"===e.currentView?a(\"CommonForm\",{attrs:{\"m-id\":e.currentId,\"m-name\":e.currentName,\"form-data\":e.currentForm},on:{handleBack:e.handleBack}}):e._e(),e._v(\" \"),\"ToDoListForm\"===e.currentView?a(\"ToDoListForm\",{attrs:{\"m-id\":e.currentId,\"m-name\":e.currentName,\"form-data\":e.currentForm},on:{handleBack:e.handleBack}}):e._e(),e._v(\" \"),\"TextPopForm\"===e.currentView?a(\"TextPopForm\",{attrs:{\"m-id\":e.currentId,\"m-name\":e.currentName,\"form-data\":e.currentForm},on:{handleBack:e.handleBack}}):e._e(),e._v(\" \"),\"FontForm\"===e.currentView?a(\"FontForm\",{attrs:{\"m-id\":e.currentId,\"m-name\":e.currentName,\"form-data\":e.currentForm},on:{handleBack:e.handleBack}}):e._e()],1)],1)])},r=[],n=a(\"53ca\"),l=(a(\"7f7f\"),a(\"ac6a\"),a(\"c7eb\")),s=(a(\"96cf\"),a(\"1da1\")),o=a(\"b775\");function c(e){return Object(o[\"a\"])({url:\"/platform/gam/community/v1/official-material/save\",method:\"post\",data:e})}function u(e){return Object(o[\"a\"])({url:\"/platform/gam/community/v1/official-material/delete\",method:\"post\",data:{id:e}})}function d(e){return Object(o[\"a\"])({url:\"/platform/gam/community/v1/official-material/detail\",method:\"get\",params:e})}function f(e){return Object(o[\"a\"])({url:\"/platform/gam/community/v1/official-material/pagelist\",method:\"get\",params:e})}var p=a(\"7754\"),m=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a(\"div\",{staticClass:\"card\"},[a(\"div\",{staticClass:\"card-info\"},[a(\"div\",{staticClass:\"card-info-id\"},[e._v(\"id:\"+e._s(e.item.id))]),e._v(\" \"),a(\"div\",{staticClass:\"card-info-size\"},[a(\"span\",[e._v(\"宽:\"+e._s(e.item.width)+\"px\")]),e._v(\" \"),a(\"span\",[e._v(\" 高:\"+e._s(e.item.height)+\"px\")])]),e._v(\" \"),a(\"div\",{staticClass:\"card-info-time\"},[e._v(\"创建时间：\"+e._s(e.item.createTime))]),e._v(\" \"),parseInt(e.item.length)>0?a(\"div\",{staticClass:\"card-info-type\"},[e._v(\"\\n      纸张类型: 标签纸-\"+e._s(e.item.length)+\"\\n    \")]):e._e(),e._v(\" \"),-1===parseInt(e.item.length)?a(\"div\",{staticClass:\"card-info-type\"},[e._v(\"\\n      纸张类型: 连续纸\\n    \")]):e._e(),e._v(\" \"),e.needShowNew(e.item.isNew,e.item.newFlagBeforeDate)?a(\"div\",[a(\"el-tag\",{attrs:{type:\"danger\"}},[e._v(\"显示最新\")])],1):e._e(),e._v(\" \"),e.item.name?a(\"div\",[e._v(\"名称：\"+e._s(e.item.name))]):e._e()]),e._v(\" \"),a(\"div\",{staticClass:\"card-image\"},[a(\"el-image\",{staticStyle:{width:\"100%\",height:\"300px\"},attrs:{src:e.item.pic,fit:\"scale-down\"}},[a(\"div\",{staticClass:\"image-slot\",attrs:{slot:\"placeholder\"},slot:\"placeholder\"},[e._v(\"\\n        加载中\"),a(\"span\",{staticClass:\"dot\"},[e._v(\"...\")])])])],1),e._v(\" \"),a(\"div\",{staticClass:\"card-control\"},[a(\"el-tooltip\",{attrs:{content:\"编辑\",placement:\"top\"}},[a(\"el-button\",{staticClass:\"el-icon-edit\",on:{click:e.notifyEdit}})],1),e._v(\" \"),a(\"el-tooltip\",{attrs:{content:\"删除\",placement:\"top\"}},[a(\"el-button\",{directives:[{name:\"permission\",rawName:\"v-permission\",value:\"btn-menuA4-material-edit\",expression:\"'btn-menuA4-material-edit'\"}],staticClass:\"el-icon-delete\",on:{click:function(t){return t.stopPropagation(),e.handelDelete(t)}}})],1)],1)])},h=[],v=(a(\"28a5\"),a(\"a481\"),{name:\"Card\",props:{item:{type:Object,required:!0,default:function(){return{}}}},methods:{handelDelete:function(){var e=Object(s[\"a\"])(Object(l[\"a\"])().mark((function e(){var t,a=this;return Object(l[\"a\"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:t=this,this.$confirm(\"此操作将永久删除素材，不可恢复, 是否继续?\",\"提示\",{confirmButtonText:\"确定\",cancelButtonText:\"取消\",type:\"warning\"}).then((function(){u(a.item.id).then((function(e){0===e.head.ret?t.$message.success(\"删除成功\"):t.$message.error(\"删除失败\"),t.notifyUpdate()}))})).catch((function(){}));case 2:case\"end\":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}(),notifyUpdate:function(){this.$emit(\"notifyUpdate\")},notifyEdit:function(){this.$emit(\"notifyEdit\",this.item)},needShowNew:function(e,t){if(e>0){if(t){var a=new Date;return!(a>new Date(Date.parse(t.replace(/-/g,\"/\"))))}return!0}return!1},getDate:function(e){var t=e,a=t.split(\" \"),i=a[0].split(\"-\"),r=a[1].split(\":\"),n=new Date(i[0],i[1],i[2],r[0],r[1],r[2]);return n}}}),g=v,b=(a(\"93a5\"),a(\"2877\")),w=Object(b[\"a\"])(g,m,h,!1,null,\"2e2741c4\",null),y=w.exports,F=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a(\"div\",{staticClass:\"todolist\"},[a(\"el-form\",{staticClass:\"demo-form-inline\"},[a(\"el-form-item\",{attrs:{label:\"名称：\"}},[a(\"el-input\",{staticStyle:{width:\"60%\"},attrs:{type:\"text\"},model:{value:e.formData.name,callback:function(t){e.$set(e.formData,\"name\",t)},expression:\"formData.name\"}})],1),e._v(\" \"),a(\"el-form-item\",{attrs:{label:\"行业分类：\"}},[a(\"el-select\",{staticStyle:{width:\"60%\"},attrs:{placeholder:\"请选择行业分类\"},model:{value:e.formData.category,callback:function(t){e.$set(e.formData,\"category\",t)},expression:\"formData.category\"}},e._l(e.industryList,(function(e){return a(\"el-option\",{key:e.code,attrs:{label:e.name,value:e.code}})})),1)],1),e._v(\" \"),a(\"el-row\",[a(\"el-col\",{attrs:{span:12}},[a(\"el-form-item\",{attrs:{label:\"预览图：\"}},[a(\"SingleUpload\",{key:\"listUrl\",attrs:{\"key-word\":\"listUrl\",\"init-url\":e.isEdit?e.files.listUrl.pic:\"\"},on:{updateFile:e.updateFile}})],1)],1),e._v(\" \"),a(\"el-col\",{attrs:{span:12}},[a(\"el-form-item\",{attrs:{label:\"资源图：\"}},[a(\"SingleUpload\",{key:\"resUrl\",attrs:{\"key-word\":\"resUrl\",\"init-url\":e.isEdit?e.files.resUrl.pic:\"\"},on:{updateFile:e.updateFile}})],1)],1)],1),e._v(\" \"),a(\"el-form-item\",{attrs:{label:\"是否展示new:\",prop:\"shareFlag\"}},[a(\"el-radio-group\",{model:{value:e.formData.isNew,callback:function(t){e.$set(e.formData,\"isNew\",t)},expression:\"formData.isNew\"}},[a(\"el-radio-button\",{attrs:{label:\"0\"}},[e._v(\"不展示\")]),e._v(\" \"),a(\"el-radio-button\",{attrs:{label:\"1\"}},[e._v(\"展示\")])],1),e._v(\" \"),a(\"span\",{staticStyle:{\"padding-left\":\"20px\",\"padding-right\":\"15px\"}},[e._v(\"期限:\")]),e._v(\" \"),a(\"el-date-picker\",{attrs:{\"value-format\":\"yyyy-MM-dd\",type:\"date\",placeholder:\"选择展示期限\"},model:{value:e.formData.newFlagBeforeDate,callback:function(t){e.$set(e.formData,\"newFlagBeforeDate\",t)},expression:\"formData.newFlagBeforeDate\"}})],1),e._v(\" \"),a(\"el-tabs\",{attrs:{type:\"card\"},model:{value:e.tabsActiveName,callback:function(t){e.tabsActiveName=t},expression:\"tabsActiveName\"}},[a(\"el-tab-pane\",{attrs:{label:\"A4\",name:\"first\"}},[a(\"el-row\",[a(\"el-col\",{attrs:{span:12}},[a(\"el-form-item\",{attrs:{label:\"预览图：\"}},[a(\"SingleUpload\",{key:\"listUrl\",attrs:{\"key-word\":\"listUrl\",\"init-url\":e.isEdit&&e.filesA4.listUrl&&e.filesA4.listUrl.pic?e.filesA4.listUrl.pic:\"\"},on:{updateFile:e.updateFileA4}})],1)],1),e._v(\" \"),a(\"el-col\",{attrs:{span:12}},[a(\"el-form-item\",{attrs:{label:\"资源图：\"}},[a(\"SingleUpload\",{key:\"resUrl\",attrs:{\"key-word\":\"resUrl\",\"init-url\":e.isEdit&&e.filesA4.resUrl&&e.filesA4.resUrl.pic?e.filesA4.resUrl.pic:\"\"},on:{updateFile:e.updateFileA4}})],1)],1)],1)],1),e._v(\" \"),a(\"el-tab-pane\",{attrs:{label:\"A5\",name:\"second\"}},[a(\"el-row\",[a(\"el-col\",{attrs:{span:12}},[a(\"el-form-item\",{attrs:{label:\"预览图：\"}},[a(\"SingleUpload\",{key:\"listUrl\",attrs:{\"key-word\":\"listUrl\",\"init-url\":e.isEdit&&e.filesA5.listUrl&&e.filesA5.listUrl.pic?e.filesA5.listUrl.pic:\"\"},on:{updateFile:e.updateFileA5}})],1)],1),e._v(\" \"),a(\"el-col\",{attrs:{span:12}},[a(\"el-form-item\",{attrs:{label:\"资源图：\"}},[a(\"SingleUpload\",{key:\"resUrl\",attrs:{\"key-word\":\"resUrl\",\"init-url\":e.isEdit&&e.filesA5.resUrl&&e.filesA5.resUrl.pic?e.filesA5.resUrl.pic:\"\"},on:{updateFile:e.updateFileA5}})],1)],1)],1)],1)],1),e._v(\" \"),a(\"el-form-item\",[a(\"el-button\",{on:{click:e.handleBack}},[e._v(\"返回\")]),e._v(\" \"),a(\"el-button\",{directives:[{name:\"permission\",rawName:\"v-permission\",value:\"btn-menuA4-material-edit\",expression:\"'btn-menuA4-material-edit'\"}],attrs:{type:\"primary\"},on:{click:e.handleSave}},[e._v(\"保存\")])],1)],1)],1)},k=[],U=(a(\"456d\"),function(){var e=this,t=e.$createElement,a=e._self._c||t;return a(\"div\",{staticClass:\"singleupload\"},[a(\"el-upload\",{staticClass:\"avatar-uploader\",attrs:{accept:\"image/png, image/jpeg\",action:\"https://jsonplaceholder.typicode.com/posts/\",\"show-file-list\":!1,\"auto-upload\":!1,\"on-change\":e.onChnagePic}},[e.getUrl?a(\"HoverMask\",{scopedSlots:e._u([{key:\"action\",fn:function(){return[a(\"i\",{staticClass:\"el-icon-view\",on:{click:function(t){return t.stopPropagation(),e.handleClick(t)}}})]},proxy:!0}],null,!1,2272652603)},[a(\"img\",{staticClass:\"avatar\",attrs:{src:e.getUrl}})]):a(\"i\",{staticClass:\"el-icon-plus avatar-uploader-icon\"})],1),e._v(\" \"),a(\"el-dialog\",{attrs:{visible:e.dialogVisible,\"append-to-body\":\"\"},on:{\"update:visible\":function(t){e.dialogVisible=t}}},[a(\"img\",{attrs:{width:\"100%\",src:e.getUrl,alt:\"\"}})])],1)}),_=[],D=a(\"37f7\"),O=a(\"ed08\"),j={name:\"SingleUpload\",components:{HoverMask:D[\"a\"]},props:{keyWord:{type:String,default:\"\"},initUrl:{type:String,default:\"\"}},data:function(){return{imageUrl:\"\",dialogVisible:!1,currentFile:void 0}},computed:{getUrl:function(){var e=this.imageUrl||this.initUrl;return e}},watch:{},mounted:function(){},methods:{handleClick:function(){this.dialogVisible=!0},onChnagePic:function(e,t){Object(O[\"d\"])(e.raw)?(this.currentFile=e.raw,this.imageUrl=URL.createObjectURL(e.raw),this.$emit(\"updateFile\",this.keyWord,this.currentFile)):this.$message.error(\"仅支持图片上传\")}}},A=j,x=Object(b[\"a\"])(A,U,_,!1,null,null,null),E=x.exports,C=a(\"006f\"),B=new C[\"a\"],S={listUrl:\"请选择预览图\",resUrl:\"请选择资源图\"},$={listUrl:\"请选择A4预览图\",resUrl:\"请选择A4资源图\"},N={listUrl:\"请选择A5预览图\",resUrl:\"请选择A5资源图\"},I={components:{SingleUpload:E},props:{formData:{type:Object,default:function(){return{}}}},data:function(){return{uploadFiles:{listUrl:{},resUrl:{},topUrl:{},downUrl:{},leftUrl:{},rightUrl:{}},uploadFilesA4:{listUrl:{},resUrl:{},topUrl:{},downUrl:{},leftUrl:{},rightUrl:{}},uploadFilesA5:{listUrl:{},resUrl:{},topUrl:{},downUrl:{},leftUrl:{},rightUrl:{}},industryList:[],tabsActiveName:\"first\"}},computed:{isEdit:function(){return!(!this.formData||!this.formData.id)},files:function(){return this.isEdit&&this.formData.resMap||{}},filesA4:function(){return this.isEdit&&this.formData.resMapA4?this.formData.resMapA4:{}},filesA5:function(){return this.isEdit&&this.formData.resMapA5?this.formData.resMapA5:{}},addId:function(){return this.isEdit?this.formData.id:\"\"}},mounted:function(){this.fetchIndustryData()},methods:{handleBack:function(e){this.$emit(\"handleBack\",e)},updateFile:function(e,t){this.uploadFiles[e]={file:t,key:e}},updateFileA4:function(e,t){this.uploadFilesA4[e]={file:t,key:e}},updateFileA5:function(e,t){this.uploadFilesA5[e]={file:t,key:e}},validateFiles:function(){var e=this,t=!0;return this.isEdit&&(Object.keys(this.files).forEach((function(t){var a=e.uploadFiles[t];a&&a.hasOwnProperty(\"file\")||(e.uploadFiles[t]=e.files[t])})),Object.keys(this.filesA4).forEach((function(t){var a=e.uploadFilesA4[t];a&&a.hasOwnProperty(\"file\")||(e.uploadFilesA4[t]=e.filesA4[t])})),Object.keys(this.filesA5).forEach((function(t){var a=e.uploadFilesA5[t];a&&a.hasOwnProperty(\"file\")||(e.uploadFilesA5[t]=e.filesA5[t])}))),Object.keys(this.uploadFiles).forEach((function(a){if(t){var i=e.uploadFiles[a];S[a]&&i&&!i.file&&(!i.pic||i.pic.indexOf(\"http\")<0)&&(e.$message.error(S[a]),t=!1)}})),Object.keys(this.uploadFilesA4).forEach((function(a){if(t){var i=e.uploadFilesA4[a];$[a]&&i&&!i.file&&(!i.pic||i.pic.indexOf(\"http\")<0)&&(e.$message.error($[a]),t=!1)}})),Object.keys(this.uploadFilesA5).forEach((function(a){if(t){var i=e.uploadFilesA5[a];N[a]&&i&&!i.file&&(!i.pic||i.pic.indexOf(\"http\")<0)&&(e.$message.error(N[a]),t=!1)}})),t},handleSave:function(){var e=Object(s[\"a\"])(Object(l[\"a\"])().mark((function e(){var t,a,i,r,n,s,o=this;return Object(l[\"a\"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:this.validateFiles()&&(t=[],a={},Object.keys(this.uploadFiles).forEach((function(e){var i=o.uploadFiles[e];i.file?(i.file.key=e,t.push(i.file)):a[e]={pic:i.pic}})),i=[],r={},Object.keys(this.uploadFilesA4).forEach((function(e){var t=o.uploadFilesA4[e];t.file?(t.file.key=e,i.push(t.file)):r[e]={pic:t.pic}})),n=[],s={},Object.keys(this.uploadFilesA5).forEach((function(e){var t=o.uploadFilesA5[e];t.file?(t.file.key=e,n.push(t.file)):s[e]={pic:t.pic}})),this.uploadAllFiles(t,i,n,a,r,s));case 1:case\"end\":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}(),uploadAllFiles:function(e,t,a,i,r,n){var l=this;B.uploadFiles(e,(function(e,s){s?l.$message.error(\"文件上传失败，请检查\"):(e&&e.forEach((function(e){i[e.key]={pic:e.name}})),B.uploadFiles(t,(function(e,t){t?l.$message.error(\"A4文件上传失败，请检查\"):(e&&e.forEach((function(e){r[e.key]={pic:e.name}})),B.uploadFiles(a,(function(e,t){t?l.$message.error(\"A5文件上传失败，请检查\"):(e&&e.forEach((function(e){n[e.key]={pic:e.name}})),l.save(i,r,n))})))})))}))},save:function(e,t,a){var i=this,r={id:this.addId,name:this.formData.name,category:this.formData.category,isNew:this.formData.isNew,newFlagBeforeDate:this.formData.newFlagBeforeDate,picDto:e,picDtoA4:t,picDtoA5:a};console.log(\"saveOfficeMaterial params\",r),c(r).then((function(e){0===e.head.ret?(i.$message.success(\"保存成功\"),i.handleBack(\"1\")):i.$message.error(e.head.msg)}))},fetchIndustryData:function(){var e=Object(s[\"a\"])(Object(l[\"a\"])().mark((function e(){var t;return Object(l[\"a\"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,Object(p[\"b\"])({type:\"industry\"});case 3:t=e.sent,t.head&&0===t.head.ret&&(this.industryList=t.data||[]),e.next=10;break;case 7:e.prev=7,e.t0=e[\"catch\"](0),console.error(\"获取行业列表失败:\",e.t0);case 10:case\"end\":return e.stop()}}),e,this,[[0,7]])})));function t(){return e.apply(this,arguments)}return t}()}},L=I,M=Object(b[\"a\"])(L,F,k,!1,null,null,null),T=M.exports,P=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a(\"div\",{staticClass:\"todolist\"},[a(\"el-form\",{staticClass:\"demo-form-inline\",attrs:{inline:!0}},[a(\"el-form-item\",{attrs:{label:\"预览图：\"}},[a(\"SingleUpload\",{key:\"listUrl\",attrs:{\"key-word\":\"listUrl\",\"init-url\":e.isEdit&&e.files.listUrl?e.files.listUrl.pic:\"\"},on:{updateFile:e.updateFile}})],1),e._v(\" \"),a(\"el-form-item\",{attrs:{label:\"资源图：\"}},[a(\"SingleUpload\",{key:\"resUrl\",attrs:{\"key-word\":\"resUrl\",\"init-url\":e.isEdit&&e.files.resUrl?e.files.resUrl.pic:\"\"},on:{updateFile:e.updateFile}})],1),e._v(\" \"),a(\"el-form-item\",{attrs:{label:\"顶部图：\"}},[a(\"SingleUpload\",{attrs:{\"key-word\":\"topUrl\",\"init-url\":e.isEdit&&e.files.topUrl?e.files.topUrl.pic:\"\"},on:{updateFile:e.updateFile}})],1),e._v(\" \"),a(\"el-form-item\",{attrs:{label:\"底部图：\"}},[a(\"SingleUpload\",{attrs:{\"key-word\":\"downUrl\",\"init-url\":e.isEdit&&e.files.downUrl?e.files.downUrl.pic:\"\"},on:{updateFile:e.updateFile}})],1),e._v(\" \"),a(\"el-form-item\",{attrs:{label:\"左侧图：\"}},[a(\"SingleUpload\",{attrs:{\"key-word\":\"leftUrl\",\"init-url\":e.isEdit&&e.files.leftUrl?e.files.leftUrl.pic:\"\"},on:{updateFile:e.updateFile}})],1),e._v(\" \"),a(\"el-form-item\",{attrs:{label:\"右侧图：\"}},[a(\"SingleUpload\",{attrs:{\"key-word\":\"rightUrl\",\"init-url\":e.isEdit&&e.files.rightUrl?e.files.rightUrl.pic:\"\"},on:{updateFile:e.updateFile}})],1),e._v(\" \"),a(\"br\"),e._v(\" \"),a(\"el-form-item\",{attrs:{label:\"是否展示new\",prop:\"shareFlag\"}},[a(\"el-radio-group\",{model:{value:e.formData.isNew,callback:function(t){e.$set(e.formData,\"isNew\",t)},expression:\"formData.isNew\"}},[a(\"el-radio-button\",{attrs:{label:\"0\"}},[e._v(\"不展示\")]),e._v(\" \"),a(\"el-radio-button\",{attrs:{label:\"1\"}},[e._v(\"展示\")])],1)],1),e._v(\" \"),a(\"span\",{staticStyle:{\"padding-left\":\"20px\",\"padding-right\":\"15px\"}},[e._v(\"期限:\")]),a(\"el-date-picker\",{attrs:{type:\"date\",\"value-format\":\"yyyy-MM-dd\",placeholder:\"选择展示期限\"},model:{value:e.formData.newFlagBeforeDate,callback:function(t){e.$set(e.formData,\"newFlagBeforeDate\",t)},expression:\"formData.newFlagBeforeDate\"}}),e._v(\" \"),a(\"br\"),e._v(\" \"),a(\"el-form-item\",{staticStyle:{\"margin-left\":\"40%\"}},[a(\"el-button\",{on:{click:e.handleBack}},[e._v(\"返回\")]),e._v(\" \"),a(\"el-button\",{directives:[{name:\"permission\",rawName:\"v-permission\",value:\"btn-menuA4-material-edit\",expression:\"'btn-menuA4-material-edit'\"}],attrs:{type:\"primary\"},on:{click:e.handleSave}},[e._v(\"保存\")])],1)],1)],1)},V=[],z=new C[\"a\"],R={listUrl:\"请选择预览图\",resUrl:\"请选择资源图\"},H={components:{SingleUpload:E},props:{formData:{type:Object,default:function(){return{}}}},data:function(){return{uploadFiles:{listUrl:{},resUrl:{},topUrl:{},downUrl:{},leftUrl:{},rightUrl:{}},paperLabel:\"0\",placeType:\"0\",isNew:0}},computed:{isEdit:function(){return!(!this.formData||!this.formData.id)},files:function(){return this.isEdit&&this.formData.resMap||{}},addId:function(){return this.isEdit?this.formData.id:\"\"}},mounted:function(){},methods:{handleBack:function(e){this.$emit(\"handleBack\",e)},updateFile:function(e,t){this.uploadFiles[e]={file:t,key:e}},validateFiles:function(){var e=this,t=!0;return this.isEdit&&Object.keys(this.files).forEach((function(t){var a=e.uploadFiles[t];a&&a.hasOwnProperty(\"file\")||(e.uploadFiles[t]=e.files[t])})),Object.keys(this.uploadFiles).forEach((function(a){if(t){var i=e.uploadFiles[a];R[a]&&i&&!i.file&&(!i.pic||i.pic.indexOf(\"http\")<0)&&(e.$message.error(R[a]),t=!1)}})),t},handleSave:function(){var e=Object(s[\"a\"])(Object(l[\"a\"])().mark((function e(){var t,a,i=this;return Object(l[\"a\"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:this.validateFiles()&&(t=[],a={},Object.keys(this.uploadFiles).forEach((function(e){var r=i.uploadFiles[e];r.file?(r.file.key=e,t.push(r.file)):a[e]={pic:r.pic}})),z.uploadFiles(t,(function(e,t){if(t)i.$message.error(\"文件上传失败，请检查\");else{e&&e.forEach((function(e){a[e.key]={pic:e.name}}));var r={id:i.addId,isNew:i.formData.isNew,newFlagBeforeDate:i.formData.newFlagBeforeDate,picDto:a};c(r).then((function(e){0===e.head.ret?(i.$message.success(\"保存成功\"),i.handleBack(\"1\")):i.$message.error(e.head.msg)}))}})));case 1:case\"end\":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}()}},q=H,J=Object(b[\"a\"])(q,P,V,!1,null,null,null),W=J.exports,G=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a(\"div\",{staticClass:\"todolist\"},[a(\"el-form\",{staticClass:\"demo-form-inline\",attrs:{inline:!0}},[a(\"el-form-item\",{attrs:{label:\"预览图：\"}},[a(\"SingleUpload\",{key:\"listUrl\",attrs:{\"key-word\":\"listUrl\",\"init-url\":e.isEdit?e.files.listUrl.pic:\"\"},on:{updateFile:e.updateFile}})],1),e._v(\" \"),a(\"el-form-item\",{attrs:{label:\"资源图：\"}},[a(\"SingleUpload\",{key:\"resUrl\",attrs:{\"key-word\":\"resUrl\",\"init-url\":e.isEdit?e.files.resUrl.pic:\"\"},on:{updateFile:e.updateFile}})],1),e._v(\" \"),a(\"el-form-item\",{attrs:{label:\"是否展示new\",prop:\"shareFlag\"}},[a(\"el-radio-group\",{model:{value:e.formData.isNew,callback:function(t){e.$set(e.formData,\"isNew\",t)},expression:\"formData.isNew\"}},[a(\"el-radio-button\",{attrs:{label:\"0\"}},[e._v(\"不展示\")]),e._v(\" \"),a(\"el-radio-button\",{attrs:{label:\"1\"}},[e._v(\"展示\")])],1),e._v(\" \"),a(\"span\",{staticStyle:{\"padding-left\":\"20px\",\"padding-right\":\"15px\"}},[e._v(\"期限:\")]),a(\"el-date-picker\",{attrs:{\"value-format\":\"yyyy-MM-dd\",type:\"date\",placeholder:\"选择展示期限\"},model:{value:e.formData.newFlagBeforeDate,callback:function(t){e.$set(e.formData,\"newFlagBeforeDate\",t)},expression:\"formData.newFlagBeforeDate\"}})],1),e._v(\" \"),a(\"el-form-item\",[a(\"el-button\",{on:{click:e.handleBack}},[e._v(\"返回\")]),e._v(\" \"),a(\"el-button\",{directives:[{name:\"permission\",rawName:\"v-permission\",value:\"btn-menuA4-material-edit\",expression:\"'btn-menuA4-material-edit'\"}],attrs:{type:\"primary\"},on:{click:e.handleSave}},[e._v(\"保存\")])],1)],1)],1)},K=[],Q=new C[\"a\"],X={listUrl:\"请选择预览图\",resUrl:\"请选择资源图\"},Y={components:{SingleUpload:E},props:{formData:{type:Object,default:function(){return{}}}},data:function(){return{uploadFiles:{listUrl:{},resUrl:{},topUrl:{},downUrl:{},leftUrl:{},rightUrl:{}}}},computed:{isEdit:function(){return!(!this.formData||!this.formData.id)},files:function(){return this.isEdit&&this.formData.resMap||{}},addId:function(){return this.isEdit?this.formData.id:\"\"}},methods:{handleBack:function(e){this.$emit(\"handleBack\",e)},updateFile:function(e,t){this.uploadFiles[e]={file:t,key:e}},validateFiles:function(){var e=this,t=!0;return this.isEdit&&Object.keys(this.files).forEach((function(t){var a=e.uploadFiles[t];a&&a.hasOwnProperty(\"file\")||(e.uploadFiles[t]=e.files[t])})),Object.keys(this.uploadFiles).forEach((function(a){if(t){var i=e.uploadFiles[a];X[a]&&i&&!i.file&&(!i.pic||i.pic.indexOf(\"http\")<0)&&(e.$message.error(X[a]),t=!1)}})),t},handleSave:function(){var e=Object(s[\"a\"])(Object(l[\"a\"])().mark((function e(){var t,a,i=this;return Object(l[\"a\"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:this.validateFiles()&&(t=[],a={},Object.keys(this.uploadFiles).forEach((function(e){var r=i.uploadFiles[e];r.file?(r.file.key=e,t.push(r.file)):a[e]={pic:r.pic}})),Q.uploadFiles(t,(function(e,t){if(t)i.$message.error(\"文件上传失败，请检查\");else{e&&e.forEach((function(e){a[e.key]={pic:e.name}}));var r={id:i.addId,isNew:i.formData.isNew,newFlagBeforeDate:i.formData.newFlagBeforeDate,picDto:a};c(r).then((function(e){0===e.head.ret?(i.$message.success(\"保存成功\"),i.handleBack(\"1\")):i.$message.error(e.head.msg)}))}})));case 1:case\"end\":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}()}},Z=Y,ee=Object(b[\"a\"])(Z,G,K,!1,null,null,null),te=ee.exports,ae=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a(\"div\",{staticClass:\"font\"},[a(\"el-form\",{attrs:{\"label-width\":\"100px\"}},[a(\"el-form-item\",{attrs:{label:\"字体文件：\"}},[a(\"el-upload\",{attrs:{action:\"\",accept:\".ttf,.eot,.otf,.woff,.svg\",\"auto-upload\":!1,\"show-file-list\":!1,\"on-change\":e.onChangeFont}},[a(\"div\",{attrs:{slot:\"tip\"},slot:\"tip\"},[a(\"a\",{staticStyle:{color:\"#409eff\"},attrs:{href:e.fontFile.url,target:\"_blank\"}},[e._v(e._s(e.fontFile.url))])]),e._v(\" \"),a(\"el-button\",{attrs:{type:\"primary\",size:\"small\"}},[e._v(\"上传字体文件\")])],1)],1),e._v(\" \"),a(\"el-form-item\",{attrs:{label:\"预览图：\"}},[a(\"el-upload\",{attrs:{action:\"OSS上传路径，必填\",\"list-type\":\"picture-card\",accept:\"image/png, image/jpeg\",\"before-upload\":e.handelBeforeUpload,\"http-request\":e.upLoadNothing,\"on-preview\":e.handlePictureCardPreview,\"before-remove\":e.handleBeforeRemove,\"file-list\":e.previewFile}},[a(\"i\",{staticClass:\"el-icon-plus\"})]),e._v(\" \"),a(\"el-dialog\",{attrs:{visible:e.dialogVisible},on:{\"update:visible\":function(t){e.dialogVisible=t}}},[a(\"img\",{attrs:{width:\"100%\",src:e.dialogImageUrl,alt:\"\"}})])],1),e._v(\" \"),a(\"el-form-item\",{attrs:{label:\"是否展示new\",prop:\"shareFlag\"}},[a(\"el-radio-group\",{model:{value:e.isNew,callback:function(t){e.isNew=t},expression:\"isNew\"}},[a(\"el-radio-button\",{attrs:{label:\"0\"}},[e._v(\"不展示\")]),e._v(\" \"),a(\"el-radio-button\",{attrs:{label:\"1\"}},[e._v(\"展示\")])],1),e._v(\" \"),a(\"span\",{staticStyle:{\"padding-left\":\"20px\",\"padding-right\":\"15px\"}},[e._v(\"期限:\")]),a(\"el-date-picker\",{attrs:{\"value-format\":\"yyyy-MM-dd\",type:\"date\",placeholder:\"选择展示期限\"},model:{value:e.newFlagBeforeDate,callback:function(t){e.newFlagBeforeDate=t},expression:\"newFlagBeforeDate\"}})],1),e._v(\" \"),a(\"el-form-item\",[a(\"el-button\",{on:{click:e.handleBack}},[e._v(\"返回\")]),e._v(\" \"),a(\"el-button\",{directives:[{name:\"permission\",rawName:\"v-permission\",value:\"btn-menuA4-material-edit\",expression:\"'btn-menuA4-material-edit'\"}],attrs:{type:\"primary\"},on:{click:e.handleSave}},[e._v(\"保存\")])],1)],1)],1)},ie=[],re=a(\"4c6b\"),ne=new C[\"a\"],le={props:{formData:{type:Object,default:function(){return{}}}},data:function(){return{fontFile:{name:\"\",url:\"\",file:void 0},previewFile:[],hasUploadPre:!1,dialogVisible:!1,dialogImageUrl:\"\",info:void 0,isNew:0,newFlagBeforeDate:\"\",addId:\"\"}},watch:{formData:{immediate:!0,handler:function(e){if(e.id){this.addId=e.id,this.fontFile.url=e.resMap.downloadUrl.pic;var t={name:\"\",url:e.resMap.resUrl.pic};this.previewFile.pop(),this.previewFile.push(t),this.newFlagBeforeDate=e.newFlagBeforeDate,this.isNew=e.isNew}}}},mounted:function(){},methods:{handleBack:function(e){this.$emit(\"handleBack\",e)},handleSave:function(){var e=Object(s[\"a\"])(Object(l[\"a\"])().mark((function e(){var t,a,i,r;return Object(l[\"a\"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:t=[],a={},i=this,this.fontFile.file?t.push(this.fontFile.file):a.downloadUrl={pic:this.fontFile.url},r=this.previewFile[0],r&&r.file?t.push(r.file):a.resUrl={pic:r.url},t.length>0?ne.uploadFiles(t,(function(e,t){e&&(e.forEach((function(e){a[e.key]={pic:e.name.replace(re[\"a\"],\"\")}})),i.addOrSave(a))})):i.addOrSave(a);case 7:case\"end\":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}(),addOrSave:function(){var e=Object(s[\"a\"])(Object(l[\"a\"])().mark((function e(t){var a,i;return Object(l[\"a\"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return a={id:this.addId,isNew:this.isNew,newFlagBeforeDate:this.newFlagBeforeDate,picDto:t},e.next=3,c(a);case 3:i=e.sent,0===i.head.ret?(this.$message.success(\"保存成功\"),this.handleBack(\"1\")):this.$message.error(i.head.msg);case 5:case\"end\":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}(),onChangeFont:function(e){this.fontFile.name=e.name,this.fontFile.file=e.raw,this.fontFile.url=URL.createObjectURL(e.raw),this.fontFile.file.key=\"downloadUrl\"},handlePictureCardPreview:function(e){this.dialogImageUrl=e.url,this.dialogVisible=!0},handelBeforeUpload:function(e){var t=this,a={};return e&&/image\\//i.test(e.type)&&Object(O[\"b\"])(e,(function(i){a={url:i,file:e,uid:Object(O[\"g\"])()},a.file.key=\"resUrl\",t.previewFile.pop(),t.previewFile.push(a)})),!1},handleBeforeRemove:function(e,t){if(1===t.length)return this.$message.warning(\"请至少保留一张图片\"),!1},upLoadNothing:function(){}}},se=le,oe=Object(b[\"a\"])(se,ae,ie,!1,null,null,null),ce=oe.exports,ue={components:{Card:y,CommonForm:T,ToDoListForm:W,TextPopForm:te,FontForm:ce},data:function(){return{list:[],industryList:[],page:{size:20,no:1,total:0},showAdd:!1,currentView:\"CommonForm\",currentForm:{},currentTitile:\"办公素材编辑\",currentId:\"\",currentName:\"\"}},mounted:function(){var e=Object(s[\"a\"])(Object(l[\"a\"])().mark((function e(){return Object(l[\"a\"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,this.fetchIndustryData();case 2:this.fetchList();case 3:case\"end\":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}(),methods:{handleAdd:function(){var e=this;this.industryList.forEach((function(t){t.code===e.currentId&&(e.currentName=t.name,e.currentTitile=t.name)})),this.currentForm={mId:this.currentId},this.currentTitile=this.currentTitile+\"新增\",this.currentView=\"CommonForm\",this.showAdd=!0},notifyUpdate:function(){this.fetchList()},notifyEdit:function(){var e=Object(s[\"a\"])(Object(l[\"a\"])().mark((function e(t){return Object(l[\"a\"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,this.getDetail(t.id);case 2:case\"end\":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}(),getDetail:function(){var e=Object(s[\"a\"])(Object(l[\"a\"])().mark((function e(t){var a,i=this;return Object(l[\"a\"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,d({id:t});case 3:a=e.sent,a.head&&0===a.head.ret&&(this.currentForm=a.data,this.currentForm.id=t,this.industryList.forEach((function(e){e.code===i.currentId&&(i.currentName=e.name,i.currentTitile=e.name)})),this.currentTitile=this.currentTitile+\"编辑\",this.currentView=\"CommonForm\",this.showAdd=!0),e.next=10;break;case 7:e.prev=7,e.t0=e[\"catch\"](0),console.error(\"获取办公素材详情失败:\",e.t0);case 10:case\"end\":return e.stop()}}),e,this,[[0,7]])})));function t(t){return e.apply(this,arguments)}return t}(),handleBack:function(e){console.log(\"handleBack called with val:\",e,\"type:\",Object(n[\"a\"])(e)),this.currentForm={},this.showAdd=!1,\"1\"===e&&(console.log(\"新增/编辑成功，开始刷新列表...\"),this.page.no=1,this.fetchList())},handleCurrentChange:function(e){this.page.no=e,this.fetchList()},handleSizeChange:function(e){this.page.size=e,this.fetchList()},handleMaterialChange:function(){var e=this;this.page={size:20,no:1,total:0},this.industryList.forEach((function(t){t.code===e.currentId&&(e.currentName=t.name,e.currentTitile=t.name)})),this.fetchList()},fetchList:function(){var e=Object(s[\"a\"])(Object(l[\"a\"])().mark((function e(){var t,a;return Object(l[\"a\"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=0,t={pageno:this.page.no,pagesize:this.page.size},this.currentId&&(t.category=this.currentId),console.log(\"fetchList 请求参数:\",t),e.next=6,f(t);case 6:a=e.sent,console.log(\"fetchList 响应结果:\",a),a.head&&0===a.head.ret&&(this.list=a.data&&a.data.result||[],this.page.total=a.data&&a.data.totalCount||0,console.log(\"列表更新成功，当前数据量:\",this.list.length,\"总数:\",this.page.total)),e.next=14;break;case 11:e.prev=11,e.t0=e[\"catch\"](0),console.error(\"获取办公素材列表失败:\",e.t0);case 14:case\"end\":return e.stop()}}),e,this,[[0,11]])})));function t(){return e.apply(this,arguments)}return t}(),fetchIndustryData:function(){var e=Object(s[\"a\"])(Object(l[\"a\"])().mark((function e(){var t;return Object(l[\"a\"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,Object(p[\"b\"])({type:\"industry\"});case 3:t=e.sent,t.head&&0===t.head.ret&&(this.industryList=t.data||[],this.currentId=\"\"),e.next=10;break;case 7:e.prev=7,e.t0=e[\"catch\"](0),console.error(\"获取行业列表失败:\",e.t0);case 10:case\"end\":return e.stop()}}),e,this,[[0,7]])})));function t(){return e.apply(this,arguments)}return t}()}},de=ue,fe=(a(\"1cf5\"),Object(b[\"a\"])(de,i,r,!1,null,\"87a6ebce\",null));t[\"default\"]=fe.exports},7754:function(e,t,a){\"use strict\";a.d(t,\"c\",(function(){return o})),a.d(t,\"b\",(function(){return c})),a.d(t,\"d\",(function(){return u})),a.d(t,\"a\",(function(){return d}));var i=a(\"b775\"),r=\"/platform/gam/community/v1/dict/list\",n=\"/platform/gam/community/v1/dict/pagelist\",l=\"/platform/gam/community/v1/dict/save\",s=\"/platform/gam/community/v1/dict/delete\";function o(e){return Object(i[\"a\"])({url:n,method:\"get\",params:e})}function c(e){return Object(i[\"a\"])({url:r,method:\"get\",params:e})}function u(e){return Object(i[\"a\"])({url:l,method:\"post\",data:e})}function d(e){return Object(i[\"a\"])({url:s,method:\"post\",data:{id:e}})}},\"93a5\":function(e,t,a){\"use strict\";a(\"f839\")},b6ba:function(e,t,a){\"use strict\";a(\"d696\")},d696:function(e,t,a){},f839:function(e,t,a){}}]);", "extractedComments": []}