/*
 * @Description: In User Settings Edit
 * @Author: your name
 * @Date: 2019-10-16 10:58:32
 * @LastEditTime: 2019-10-16 18:13:52
 * @LastEditors: Please set LastEditors
 */
import Vue from 'vue'
import Router from 'vue-router'

Vue.use(Router)

/* Layout */
import Layout from '@/layout'

/**
 * Note: sub-menu only appear when route children.length >= 1
 * Detail see: https://panjiachen.github.io/vue-element-admin-site/guide/essentials/router-and-nav.html
 *
 * hidden: true                   if set true, item will not show in the sidebar(default is false)
 * alwaysShow: true               if set true, will always show the root menu
 *                                if not set alwaysShow, when item has more than one children route,
 *                                it will becomes nested mode, otherwise not show the root menu
 * redirect: noRedirect           if set noRedirect will no redirect in the breadcrumb
 * name:'router-name'             the name is used by <keep-alive> (must set!!!)
 * meta : {
    roles: ['admin','editor']    control the page roles (you can set multiple roles)
    title: 'title'               the name show in sidebar and breadcrumb (recommend set)
    icon: 'svg-name'             the icon show in the sidebar
    breadcrumb: false            if set false, the item will hidden in breadcrumb(default is true)
    activeMenu: '/example/list'  if set path, the sidebar will highlight the path you set
  }
 */

/**
 * constantRoutes
 * a base page that does not have permission requirements
 * all roles can be accessed
 */
export const constantRoutes = [
  {
    path: '/login',
    component: () => import('@/views/login/index'),
    hidden: true
  },

  {
    path: '/404',
    component: () => import('@/views/404'),
    hidden: true
  },

  // {
  //   path: '/',
  //   component: Layout,
  //   redirect: '/dashboard',
  //   children: [{
  //     path: 'dashboard',
  //     name: 'Dashboard',
  //     component: () => import('@/views/dashboard/index'),
  //     meta: { title: '首页', icon: 'dashboard' }
  //   }]
  // },

  {
    path: '/',
    component: Layout,
    redirect: '/dataanalysis',
    children: [
      {
        path: 'dataanalysis',
        name: 'dataanalysis',
        component: () => import('@/views/dataanalysis/index'),
        meta: { title: '数据统计', icon: 'analysis' }
      }
    ]
  },

  {
    path: '/community',
    component: Layout,
    redirect: 'noRedirect',
    name: 'community',
    meta: { title: '2寸、宽幅', icon: 'community' },
    children: [
      {
        path: 'banner',
        name: 'banner',
        component: () => import('@/views/community/banner/index'),
        meta: { title: 'banner列表', icon: 'table' }
      },
      {
        path: 'knowledge',
        name: 'knowledge',
        component: () => import('@/views/community/knowledge/index'),
        meta: { title: '干货区列表', icon: 'table' }
      },
      {
        path: 'material',
        name: 'material',
        component: () => import('@/views/community/material/index'),
        meta: { title: '素材库', icon: 'material' }
      },
      {
        path: 'materialtype',
        name: 'materialtype',
        component: () => import('@/views/community/materialtype/index'),
        meta: { title: '素材库类型', icon: 'material' }
      },
      // {
      //   path: 'materialoversea',
      //   name: 'materialoversea',
      //   component: () => import('@/views/community/materialoversea/index'),
      //   meta: { title: '素材库(海外)', icon: 'material' }
      // },
      {
        path: 'tag',
        name: 'tag',
        component: () => import('@/views/community/tag/index'),
        meta: { title: '标签列表', icon: 'table' }
      }, // ,
      // {
      //   path: 'activity',
      //   name: 'activity',
      //   component: () => import('@/views/community/activity/index'),
      //   meta: { title: '动态列表', icon: 'table' }
      // },
      // {
      //   path: 'activitydelayed',
      //   name: 'activitydelayed',
      //   component: () => import('@/views/community/activitydelayed/index'),
      //   meta: { title: '动态列表(未发布)', icon: 'table' }
      // },
      // {
      //   path: 'comment',
      //   name: 'comment',
      //   component: () => import('@/views/community/comment/index'),
      //   meta: { title: '评论列表', icon: 'table' }
      // }
      {
        path: 'soulword',
        name: 'soulword',
        component: () => import('@/views/community/soulword/index'),
        meta: { title: '文案列表', icon: 'table' }
      },
      {
        path: 'simplepicture',
        name: 'simplepicture',
        component: () => import('@/views/community/simplepicture/index'),
        meta: { title: '简笔画', icon: 'table' }
      },
      {
        path: 'examination',
        name: 'examination',
        component: () => import('@/views/community/examination/index'),
        meta: { title: '试卷资源', icon: 'table' }
      }
    ]
  },
  {
    path: '/community2',
    component: Layout,
    redirect: 'noRedirect',
    name: 'community',
    meta: { title: 'A4', icon: 'community' },
    children: [
      {
        path: 'banner',
        name: 'banner',
        component: () => import('@/views/community/banner/index'),
        meta: { title: 'banner列表', icon: 'table' }
      },
      {
        path: 'knowledge',
        name: 'knowledge',
        component: () => import('@/views/community/knowledge/index'),
        meta: { title: '干货区列表', icon: 'table' }
      },
      {
        path: 'material',
        name: 'material',
        component: () => import('@/views/community/material/index'),
        meta: { title: '素材库', icon: 'material' }
      },
      {
        path: 'materialtype',
        name: 'materialtype',
        component: () => import('@/views/community/materialtype/index'),
        meta: { title: '素材库类型', icon: 'material' }
      },
      // {
      //   path: 'materialoversea',
      //   name: 'materialoversea',
      //   component: () => import('@/views/community/materialoversea/index'),
      //   meta: { title: '素材库(海外)', icon: 'material' }
      // },
      {
        path: 'tag',
        name: 'tag',
        component: () => import('@/views/community/tag/index'),
        meta: { title: '标签列表', icon: 'table' }
      }, // ,
      // {
      //   path: 'activity',
      //   name: 'activity',
      //   component: () => import('@/views/community/activity/index'),
      //   meta: { title: '动态列表', icon: 'table' }
      // },
      // {
      //   path: 'activitydelayed',
      //   name: 'activitydelayed',
      //   component: () => import('@/views/community/activitydelayed/index'),
      //   meta: { title: '动态列表(未发布)', icon: 'table' }
      // },
      // {
      //   path: 'comment',
      //   name: 'comment',
      //   component: () => import('@/views/community/comment/index'),
      //   meta: { title: '评论列表', icon: 'table' }
      // }
      {
        path: 'soulword',
        name: 'soulword',
        component: () => import('@/views/community/soulword/index'),
        meta: { title: '文案列表', icon: 'table' }
      },
      {
        path: 'simplepicture',
        name: 'simplepicture',
        component: () => import('@/views/community/simplepicture/index'),
        meta: { title: '简笔画', icon: 'table' }
      },
      {
        path: 'examination',
        name: 'examination',
        component: () => import('@/views/community/examination/index'),
        meta: { title: '试卷资源', icon: 'table' }
      }
    ]
  },

  {
    path: '/user',
    component: Layout,
    redirect: '/user/list',
    name: 'user',
    alwaysShow: true,
    meta: { title: '用户管理', icon: 'usermanage' },
    children: [
      {
        path: 'list',
        name: 'list',
        component: () => import('@/views/user/list/index'),
        meta: { title: '用户列表', icon: 'table' }
      }
    ]
  },

  {
    path: '/message',
    component: Layout,
    redirect: '/message/list',
    name: 'message',
    alwaysShow: true,
    meta: { title: '消息管理', icon: 'message' },
    children: [
      {
        path: 'list',
        name: 'list',
        component: () => import('@/views/message/list/index'),
        meta: { title: '消息列表', icon: 'table' }
      }
    ]
  },

  {
    path: '/version',
    component: Layout,
    redirect: '/version/list',
    name: 'version',
    alwaysShow: true,
    meta: { title: '版本管理', icon: 'version' },
    children: [
      {
        path: 'list',
        name: 'list',
        component: () => import('@/views/version/list/index'),
        meta: { title: '版本列表', icon: 'table' }
      }
    ]
  },
  {
    path: '/coin',
    component: Layout,
    name: 'user',
    alwaysShow: true,
    meta: { title: '积分商城', icon: 'usermanage' },
    children: [
      {
        path: 'titleList',
        name: 'titleList',
        component: () => import('@/views/user/titlelist/index'),
        meta: { title: '挂件管理', icon: 'table' }
      },
      {
        path: 'goodslist',
        name: 'goodslist',
        component: () => import('@/views/coin/goods/index'),
        meta: { title: '商品列表', icon: 'table' }
      },
      {
        path: 'missionlist',
        name: 'missionlist',
        component: () => import('@/views/coin/mission/index'),
        meta: { title: '积分任务列表', icon: 'table' }
      },
      {
        path: 'orderlist',
        name: 'orderlist',
        component: () => import('@/views/coin/order/index'),
        meta: { title: '兑换订单列表', icon: 'table' }
      },
      // {
      //   path: 'finishcount',
      //   name: 'finishcount',
      //   component: () => import('@/views/coin/analysiscountfinish/index'),
      //   meta: { title: '任务次数统计', icon: 'table' }
      // },
      // {
      //   path: 'rankcoin',
      //   name: 'rankcoin',
      //   component: () => import('@/views/coin/analysiscountrank/index'),
      //   meta: { title: '新增积分统计', icon: 'table' }
      // },
      {
        path: 'statisticscoin',
        name: 'statisticscoin',
        component: () => import('@/views/coin/analysis/index'),
        meta: { title: '积分统计', icon: 'table' }
      }
    ]
  },
  {
    path: '/systemsetting',
    component: Layout,
    redirect: '/systemsetting/index',
    name: 'systemsetting',
    meta: { title: '系统设置', icon: 'setting' },
    children: [
      {
        path: 'index',
        name: 'index',
        component: () => import('@/views/systemsetting/index'),
        meta: { title: '系统设置', icon: 'setting' }
      },
      {
        path: 'help',
        name: 'help',
        component: () => import('@/views/systemhelp/index'),
        meta: { title: '帮助FAQ', icon: 'setting' }
      }
    ]
  },
  {
    path: '/feedback',
    component: Layout,
    redirect: '/feedback/list',
    name: 'feedback',
    // alwaysShow: true,
    meta: { title: '用户反馈', icon: 'feedback' },
    children: [
      {
        path: 'list',
        name: 'list',
        component: () => import('@/views/feedback/list/index'),
        meta: { title: '用户反馈', icon: 'feedback' }
      }
    ]
  },
  {
    path: '/words',
    component: Layout,
    redirect: '/words/list',
    name: 'words',
    // alwaysShow: true,
    meta: { title: '单词本', icon: 'material' },
    children: [
      {
        path: 'list',
        name: 'list',
        component: () => import('@/views/words/list/index'),
        meta: { title: '课本', icon: 'material' }
      },
      {
        path: 'wordTypeList',
        name: 'wordTypeList',
        component: () => import('@/views/wordtype/index'),
        meta: { title: '教材类型', icon: 'material' }
      }
    ]
  },
  // 404 page must be placed at the end !!!
  { path: '*', redirect: '/404', hidden: true }
]

const createRouter = () => new Router({
  // mode: 'history', // require service support
  scrollBehavior: () => ({ y: 0 }),
  routes: constantRoutes
})

const router = createRouter()

// Detail see: https://github.com/vuejs/vue-router/issues/1234#issuecomment-357941465
export function resetRouter() {
  const newRouter = createRouter()
  router.matcher = newRouter.matcher // reset router
}

export default router
