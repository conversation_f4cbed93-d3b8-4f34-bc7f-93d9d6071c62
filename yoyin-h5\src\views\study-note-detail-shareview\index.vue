<template>
  <div>
    <div class="content" v-infinite-scroll="loadMoreComments" infinite-scroll-disabled="isLoading" infinite-scroll-distance="10">
      <!-- <div>存在： {{hasProperty}}, 类型：  {{typeFunction}}</div> -->
      <div v-if="!detailData" class="loading-bg-box">
        <img class="loading-bg-default" src="./assets/img/common_bg_h5blank.png" />
      </div>
      <div class="top-line" v-if="detailData"></div>
      <div class="info-detail" v-if="detailData">
        <div class="header">
          <img class="header-img-official" v-if="detailData.userTitleObj && detailData.userTitleObj.borderUrl" :src="detailData.userTitleObj.borderUrl" @click="launchPersonalHome"/>
          <img class="header-img" :src="detailData.userPic" @click="launchPersonalHome"/>
          <!-- <div class="header-img" @click="launchPersonalHome">
            <div :style="{'background-image': 'url(' + detailData.userPic + ')'}" />
          </div> -->
          <div class="header-center-box">
            <div class="user-name">{{detailData.userName}}<img style="margin-left:5px;width:40px;height:20px;object-fit:contain" v-if="detailData.userTitleObj && detailData.userTitleObj.nameUrl" :src="detailData.userTitleObj.nameUrl"></div>
            <div v-if="isStarPrinterJS()">
                <!-- <div v-if="followStatus === 0 && !isMe && loadMeAndFollow === 2" class="follow-btn" style="margin: auto 0.3rem;border: 1px solid #0068FF;" @click="addFollowInner">{{$t(`${this.moduleKey}.follow`)}}</div> -->
                <!-- <div v-if="followStatus === 1" class="follow-btn" style="margin: auto 0.3rem;background: linear-gradient(90deg, #20A5FF 0%, #20A5FF 0%, #0068FF 100%); color:#ffffff;" @click="delFollowInner">{{$t(`${this.moduleKey}.followed`)}}</div>
                <div v-if="followStatus === 2" class="follow-btn" style="margin: auto 0.3rem;background: linear-gradient(90deg, #20A5FF 0%, #20A5FF 0%, #0068FF 100%); color:#ffffff;" @click="delFollowInner">{{$t(`${this.moduleKey}.mutual-followed`)}}</div> -->
            </div>
          </div>
          <img v-if="detailData.stickObj && detailData.stickObj.icon" class="header-stickobj" :src="detailData.stickObj.icon" />
        </div>
        <div class="title">{{detailData.title}}</div>
        <div class="desc" v-html="content"></div>
        <div v-if="detailData.smallPicDto" class="pic-box">
          <div :class="detailData.smallPicDto.length===1?'img-box-big':'img-box'" v-for="(item, index) in detailData.smallPicDto" :key="index" @click="launchFeedPic(index)">
            <div class="img" :style="{'background-image': 'url(' + item.pic + ')'}" />
            <!-- <img class="img" v-lazy="item.pic" /> -->
          </div>
        </div>

        <div v-if="detailData" class="fmttime" >
          {{detailData.fmtTime}}
        </div>

        <!-- <div v-if="detailData" class="menu-box" >
          <div class="menu" v-if="detailData.hadGivedLike===0" @click="_addGiveLikefeed">
            <img class="icon" src="./assets/img/community_ic_give_like.png"><span>{{detailData.giveLikeNum}}</span>
          </div>
          <div class="menu" v-if="detailData.hadGivedLike===1" @click="_delGiveLikefeed">
            <img class="icon" src="./assets/img/community_ic_give_like_light.png"><span>{{detailData.giveLikeNum}}</span>
          </div>
          <div class="menu">
            <img class="icon" src="./assets/img/community_ic_share.png" @click.stop="shareFeed"><span>{{detailData.shareNum}}</span>
          </div>
          <div class="menu">
            <img class="icon" src="./assets/img/community_ic_print.png" @click.stop="printFeed"><span>{{detailData.printNum}}</span>
          </div>
          <div class="menu">
            <img class="icon" src="./assets/img/discover_ic_del.png" v-if="isMe" @click.stop="deleteFeed">
          </div>
        </div> -->
      </div>

      <comment-list v-if="detailData&&comments" :totalSize="detailData.commentNum" :data="comments"> </comment-list>
    </div>
    <div class="foot" >
      <!-- <div class="foot-right-top"><font class="foot-right-top-font">{{model.clickNum}}人已获得干货</font></div> -->
      <!-- <div class="foot-center-one">试读结束</div>
      <div class="foot-center-two">下载打印机APP 获取干货入手不亏</div> -->
      <div class="foot-bottom">
        <div class="foot-bottom-img">
          <img src="./assets/img/star_logo.png" />
        </div>
        <div class="foot-bottom-center">
          <div class="foot-bottom-center-one">打印机</div>
          <div class="foot-bottom-center-two">错题整理打印 学霸高分秘籍</div>
        </div>
        <div class="foot-bottom-right" @click="navDownloadApp">立即打开</div>
      </div>
    </div>
  </div>
</template>
<script>
import { appJS, getUrlParameter, throttle } from '@/utils/common'
import { getFeedDetail, getComments } from './assets/js/api'
import CommentList from './components/comment-list'
import Cookies from 'js-cookie'

// import throttle from 'lodash/throttle'
import { THROTTLE_TIME } from '@/consts/index'
let vm
const throttleTime = THROTTLE_TIME()
export default {
  components: {
    CommentList
  },
  data () {
    return {
      isMe: false,
      moduleKey: 'feed-detail',
      feedid: '',
      detailData: null,
      comments: null,
      commentPageNo: 2,
      commentPageSize: 20,
      commentLastId: '',
      content: '',
      isLoading: false,
      noMoreData: false,
      /** 0 未关注 1 已关注 2 互相关注  */
      followStatus: -1,
      // set call back
      callBackString: '',
      appVersion: '',
      typeFunction: '',
      hasProperty: '',
      loadMeAndFollow: 0,
      showAllBtn: false
    }
  },
  async created () {
    this.$nextTick(() => {
      vm = this
      window.addCommentCallBack = item => {
        this.addCommentCallBack(item)
      }
      window.updateFollowCallBack = status => {
        this.updateFollowCallBack(status)
      }
    })
    window.setAppVersion = (res) => this.setAppVersion(res)
  },
  computed: {
    isiosSystem() {
      if (window.webkit !== undefined && window.webkit.messageHandlers !== undefined) {
        return true
      } else {
        return false
      }
    },
    getMyToken() {
      return '123'
    }
  },
  async mounted () {
    const query = getUrlParameter()
    // window.addEventListener('contextmenu', function (e) {
    //   e.preventDefault()
    // })
    this.feedid = query.feedid
    this.getFeedDetail()
    if (window.StarPrinterJS) {
      this.typeFunction = typeof (window.StarPrinterJS.getAppVersion)
      this.hasProperty = window.StarPrinterJS.getAppVersion instanceof Function
    } else if (this.isiosSystem) {
      // window.webkit.messageHandlers.getAppVersion.postMessage(null)
    }
  },
  methods: {
    setAppVersion(res) {
      console.log('调用setAppVersion方法，res=', res)
      this.typeFunction = typeof (res)
      this.hasProperty = res instanceof Function
    },
    /**
     * 获取动态内容
     */
    async getFeedDetail () {
      this.isLoading = true
      const { data, head } = await getFeedDetail(this.feedid)
      // alert(JSON.stringify(head))
      if (head.ret === 0) {
        this.detailData = data
        this.comments = data.feedCommentDto
        if (this.comments && this.comments.length > 0) {
          this.commentLastId = this.comments[this.comments.length - 1].commentId
        }
        this.content = this.transformateContent()
        /** 获取关注状态 */
        // this.getFollowStatus()
        // this._getMyInfo()
        this.isLoading = false
      } else if (head.ret === 17) {
        this.$toast(this.$t(`${this.moduleKey}.dear-starpany-user`))
          let flag = window.StarPrinterJS && typeof (window.StarPrinterJS.loadFail) === 'function'
          if (flag) {
            window.StarPrinterJS.loadFail(0)
          } else if (this.isiosSystem) {
            window.webkit.messageHandlers.loadFail.postMessage(0)
          }
        this.isLoading = true
      }
    },
        /**
     *关注状态
     */
    // async getFollowStatus () {
    //   if (this.isStarPrinterJS()) {
    //     this.isLoading = true
    //     const { data, head } = await getFollowStatus(this.detailData.userId)
    //     if (head.ret === 0) {
    //       this.followStatus = parseInt(data)
    //       // this.followStatus = data
    //       this.isLoading = false
    //       this.loadMeAndFollow++
    //     } else if (head.ret === 17) {
    //       this.$toast(this.$t(`${this.moduleKey}.dear-starpany-user`))
    //       this.isLoading = true
    //     }
    //   }
    // },

    // async _getMyInfo () {
    //   let { userId } = this.detailData
    //   if (this.isStarPrinterJS()) {
    //     this.isLoading = true
    //     const { data, head } = await getMyInfo()
    //     if (head.ret === 0) {
    //       const userInfoDto = data.userInfoDto
    //       if (userInfoDto && userInfoDto.userId === userId) {
    //         this.isMe = true
    //       } else {
    //         this.isMe = false
    //       }
    //       this.isLoading = false
    //       this.loadMeAndFollow++
    //     } else if (head.ret === 17) {
    //       this.$toast(this.$t(`${this.moduleKey}.dear-starpany-user`))
    //       this.isLoading = true
    //     }
    //   }
    // },

    // /**
    //  *点赞
    //  */
    // async _addGiveLikefeed () {
    //   if (this.isStarPrinterJS()) {
    //     this.isLoading = true
    //     const { data, head } = await addGiveLikefeed(this.detailData.feedId)
    //     if (head.ret === 0) {
    //       this.detailData.hadGivedLike = 1
    //       this.detailData.giveLikeNum = this.detailData.giveLikeNum + 1
    //       this.isLoading = false
    //     } else if (head.ret === 17) {
    //       this.$toast(this.$t(`${this.moduleKey}.dear-starpany-user`))
    //       // this.$toast('点赞失败')
    //       this.isLoading = false
    //     }
    //   }
    // },

    // /**
    //  *取消点赞
    //  */
    // async _delGiveLikefeed () {
    //   if (this.isStarPrinterJS()) {
    //     this.isLoading = true
    //     const { data, head } = await delGiveLikefeed(this.detailData.feedId)
    //     if (head.ret === 0) {
    //       this.detailData.hadGivedLike = 0
    //       this.detailData.giveLikeNum = this.detailData.giveLikeNum - 1
    //       // this.followStatus = data
    //       this.isLoading = false
    //     } else if (head.ret === 17) {
    //       this.$toast(this.$t(`${this.moduleKey}.dear-starpany-user`))
    //       this.isLoading = true
    //     }
    //   }
    // },

    /**
     * 加载更多评论
     */
    async loadMoreComments () {
      if (this.noMoreData) return
      this.isLoading = true

      const { data, head } = await getComments(
        this.feedid,
        this.commentPageNo,
        this.commentPageSize,
        this.commentLastId
      )
      this.isLoading = false
      if (head.ret === 0) {
        if (data && data.length > 0) {
          // this.comments.push(...data)
          this.commentLastId = data[data.length - 1].commentId
          this.comments.splice(this.comments.length - 1, 0, ...data)
          this.commentPageNo++
        } else {
          this.noMoreData = true
        }
        if (this.comments && this.comments.length > 0) {
        }
      }
    },

    /**
     * 动态内容富文本转换
     */
    transformateContent () {
      const label = this.detailData.labelMap
      let content = this.detailData.content
      if (label && content) {
        // if (!this.showAllBtn && content.length > 60) {
        //   content = content.substring(0, 60) + '...'
        //   this.showAllBtn = true
        // }
        content = content
          .replace(/\r\n/g, '<br/>')
          .replace(/&/g, '&amp;')
          .replace(/</g, '&lt;')
          .replace(/>/g, '&gt;')
          .replace(/\n/g, '<br>')
        for (let key in label) {
          const labelName = '#' + key + '#'
          content = content.replace(
            new RegExp(labelName, 'g'),
            '<span>' + labelName + '</span>'
          )
        }
      }
      return content
    },

    handleShowAll () {
      this.content = this.transformateContent()
      this.showAllBtn = false
    },

    addFriend: throttle(() => {
      if (window.webkit && window.webkit.messageHandlers) {
        window.webkit.messageHandlers.addFriend.postMessage(vm.detailData.userId)
      } else {
        window.StarPrinterJS.addFriend(vm.detailData.userId)
      }
    }, throttleTime),

    addFollowInner() {
      this.addFollow()
    },
    /** 加关注 */
    addFollow: throttle(() => {
      if (window.webkit && window.webkit.messageHandlers) {
        window.webkit.messageHandlers.addFollow.postMessage(vm.detailData.userId)
      } else {
        window.StarPrinterJS.addFollow(vm.detailData.userId)
      }
    }, throttleTime),

    delFollowInner() {
      this.delFollow()
    },
    /** 删除关注 */
    delFollow: throttle(() => {
      if (window.webkit && window.webkit.messageHandlers) {
        window.webkit.messageHandlers.delFollow.postMessage(vm.detailData.userId)
      } else {
        window.StarPrinterJS.delFollow(vm.detailData.userId)
      }
    }, throttleTime),

    /**
     * 跳转至个人中心
     */
    launchPersonalHome: throttle(() => {
      if (window.webkit && window.webkit.messageHandlers) {
        window.webkit.messageHandlers.launchPersonalHome.postMessage(vm.detailData.userId)
      } else {
        window.StarPrinterJS.launchPersonalHome(vm.detailData.userId)
      }
    }, throttleTime),

    /**
     * 查看大图
     */
    launchFeedPic: throttle((index) => {
      if (window.webkit && window.webkit.messageHandlers) {
        const parms = {
          'detailObj': vm.detailData,
          'index': index
        }
        window.webkit.messageHandlers.launchFeedPic.postMessage(JSON.stringify(parms))
      } else {
        window.StarPrinterJS.launchFeedPic(JSON.stringify(vm.detailData), index)
      }
    }, throttleTime),

    /**
     * 评论添加成功回调
     */
    addCommentCallBack (comment) {
      console.log('addCommentCallBack', comment)
      try {
        if (typeof comment === 'string') {
          comment = JSON.parse(comment)
        }
        // const d = new Date()
        // const myHours = d.getHours()
        // const myMinutes = d.getMinutes()
        // comment['fmtTime'] = myHours + ':' + myMinutes
        const d = new Date()
        const myHours = d.getHours()
        const myMinutes = d.getMinutes()
        const myHoursStr = (myHours < 10 ? '0' : '') + myHours
        const myMinutesStr = (myMinutes < 10 ? '0' : '') + myMinutes
        comment['fmtTime'] = myHoursStr + ':' + myMinutesStr
        this.comments.splice(0, 0, comment)
        const commentNum = this.detailData.commentNum + 1
        this.$set(this.detailData, 'commentNum', commentNum)
      } catch (error) {
        console.log(error)
      }
    },
        /**
     * 修改关注状态成功回调
     */
    updateFollowCallBack (status) {
      console.log('updateFollowCallBack', status)
      try {
        this.callBackString = status
        this.followStatus = parseInt(status)
      } catch (error) {
        console.log(error)
      }
    },

    // async shareFeed () {
    //   let { content, userName, htmlUrl, smallPicDto } = this.detailData
    //   htmlUrl = htmlUrl.replace('feed-detail', 'feed-detail-shareview')
    //   const shareInfo = {
    //     title: content,
    //     desc: content + this.$t(`${this.moduleKey}.share-feed`),
    //     content: htmlUrl
    //   }

    //   if (smallPicDto && smallPicDto.length > 0) {
    //     shareInfo.iconUrl = smallPicDto[0].pic
    //   }

    //   this.isLoading = true
    //   const { data, head } = await addShare(this.detailData.feedId)
    //   if (head.ret === 0) {
    //     this.detailData.shareNum = this.detailData.shareNum + 1
    //     // this.followStatus = data
    //     this.isLoading = false
    //   } else if (head.ret === 17) {
    //     this.$toast(this.$t(`${this.moduleKey}.dear-starpany-user`))
    //     this.isLoading = true
    //   }

    //   if (window.StarPrinterJS) {
    //     window.StarPrinterJS.share(JSON.stringify(shareInfo))
    //   } else if (this.isiosSystem) {
    //     window.webkit.messageHandlers.share.postMessage(JSON.stringify(shareInfo))
    //   }
    // },

    printFeed () {
      if (window.StarPrinterJS) {
        window.StarPrinterJS.batchPrintPic(JSON.stringify(this.detailData))
      } else if (this.isiosSystem) {
        window.webkit.messageHandlers.batchPrintPic.postMessage(JSON.stringify(this.detailData))
      }
    },

    deleteFeed () {
      if (window.StarPrinterJS) {
        window.StarPrinterJS.delUserSelfDynamic(JSON.stringify(this.detailData))
      } else if (this.isiosSystem) {
        window.webkit.messageHandlers.delUserSelfDynamic.postMessage(JSON.stringify(this.detailData))
      }
    },

    isShowAddFollow () {
      return (window.StarPrinterJS || this.isiosSystem) && this.detailData.isFriend !== 1
    },
    /** 打印机内部打开 */
    isStarPrinterJS() {
      return window.StarPrinterJS !== null || window.StarPrinterJS !== undefined || this.isiosSystem
    },
    /** 版本号 > 2.0.0 添加好友变为 关注 */
    isNewerThan200() {
      try {
        if (window.StarPrinterJS && typeof (window.StarPrinterJS.getAppVersion) === 'function') {
          const version = window.StarPrinterJS.getAppVersion()
          const arr = version.split('.')
          if (parseInt(arr[0]) < 2) {
            return false
          } else if (parseInt(arr[0]) === 2 && parseInt(arr[1]) === 0) {
            return false
          } else {
            return true
          }
        } else if (this.isiosSystem) {
          const version = Cookies.get('appVersion')
          const arr = version.split('.')
          if (parseInt(arr[0]) < 2) {
            return false
          } else if (parseInt(arr[0]) === 2 && parseInt(arr[1]) === 0) {
            return false
          } else {
            return true
          }
        }
      } catch (error) {
        return false
      }
      return false
    },
    /** 版本号 > 3.1.0 头像头衔用最新的 */
    isNewerThan310() {
      try {
        if (window.StarPrinterJS && typeof (window.StarPrinterJS.getAppVersion) === 'function') {
          const version = window.StarPrinterJS.getAppVersion()
          const arr = version.split('.')
          if (parseInt(arr[0]) < 3) {
            return false
          } else if (parseInt(arr[0]) === 3 && parseInt(arr[1]) < 2) {
            return false
          } else {
            return true
          }
        } else if (this.isiosSystem) {
          const version = Cookies.get('appVersion')
          const arr = version.split('.')
          if (parseInt(arr[0]) < 3) {
            return false
          } else if (parseInt(arr[0]) === 3 && parseInt(arr[1]) < 2) {
            return false
          } else {
            return true
          }
        }
      } catch (error) {
        return false
      }
      return false
    },
    addComment: throttle(() => {
      if (window.StarPrinterJS && vm.detailData) {
        vm.detailData.callBackName = 'addCommentCallBack'
        window.StarPrinterJS.launchCommentAdd(
          0,
          JSON.stringify(vm.detailData)
        )
      } else if (window.webkit && window.webkit.messageHandlers && vm.detailData) {
        vm.detailData.callBackName = 'addCommentCallBack'
        const parms = {
          'type': 0,
          'detailData': vm.detailData
        }
        window.webkit.messageHandlers.launchCommentAdd.postMessage(JSON.stringify(parms))
        // window.webkit.messageHandlers.launchCommentAdd.postMessage([0, JSON.stringify(vm.detailData)])
      }
    }, throttleTime),
    navDownloadApp() {
      window.location.href = 'https://a.app.qq.com/o/simple.jsp?pkgname=com.starprinter.app'
    }
  }
}
</script>

<style lang="scss">
$width: 100vw;

@-webkit-keyframes fadeInOut {
  0% {
    opacity: 1;
  }

  50% {
    opacity: 0.5;
  }
}
html {
  font-size: 16px;
  background: #F5F5F5;
}

.content {
  width: 100%;
  height: 100%;
  padding-bottom: 6.25rem;
}

.top-line {
  height: 0.75rem;
  width: 100%;
}

.info-detail {
  background: white;
  border-radius: 10px;
  margin: 0 0.75rem;
  .header {
    height: 4.625rem;
    display: flex;
    position: relative;
    .header-img {
      // width: 2.75rem;
      // height: 2.75rem;
      // overflow: hidden;
      // //border: 1px solid #f2f2f2;
      // border-radius: 50%;
      // padding-left: ;
      // div {
      //   padding: 1.25rem 1rem 0.65rem 1rem;
      //   width: 100%;
      //   height: 0;
      //   padding-bottom: 100%;
      //   overflow: hidden;
      //   background-position: center center;
      //   background-repeat: no-repeat;
      //   -webkit-background-size: cover;
      //   -moz-background-size: cover;
      //   background-size: cover;
      // }
      height: 3.125rem;
      width: 3.125rem;
      margin: 1.0rem 0.8125rem 0.4375rem 0.8125rem;
      border-radius: 30px;
    }
    .header-img-official {
      width: 4rem;
      height: 4rem;
      min-width: 4rem;
      min-height: 4rem;
      margin: 0.375rem;
      overflow: hidden;
      position: absolute;
    }
    .header-center-box {
      flex: 1;
      line-height: 4.625rem;
      .user-name {
        font-size: 1rem;
        color: black;
      }

      .time {
        font-size: 0.75rem;
        color: #bcbbbf;
      }
    }

    .follow-btn {
      position: absolute;
      height: 1.3125rem;
      top: 1.5375rem;
      right: 2.25rem;
      width: 3.75rem;
      line-height: 1.3125rem;
      // height: 1.75rem;
      // width: 5.625rem;
      // line-height: 1.7rem;
      color: #0068FF;
      text-align: center;
      font-size: 0.75rem;
      // border: 1px solid #5a7dff;
      border-radius: 6.25rem;
      /*如果是禁用长按选择文字功能，用css*/
      -webkit-touch-callout: none;
      -webkit-user-select: none;
      -khtml-user-select: none;
      -moz-user-select: none;
      -ms-user-select: none;
      user-select: none;
    }
    &-stickobj {
      position: absolute;
      width: 2.8125rem;
      height: 2.5625rem;
      right: 0;
      top: 0;
    }
  }

  .desc {
    font-size: 1rem;
    color: black;
    line-height: 1.25rem;
    padding: 1rem;
    // padding-right: 1rem;
    word-wrap:break-word;
    span {
      color: #8e8e93;
    }
  }
  .pic-box {
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-start;
    margin-top: 1.25rem;
    margin-left: 3.1%;
    .img-box-big,
    .img-box {
      width: 30%;
      height: $width * 0.3;
      // width: 6.5625rem;
      // height: 6.5625rem;
      margin-left: 1%;
      margin-right: 1%;
      text-align: center;
      vertical-align: middle;
      overflow: hidden;
      // margin-bottom: 0.625rem;
      .img {
        width: 100%;
        height: 0;
        padding-bottom: 100%;
        overflow: hidden;
        background-position: center center;
        background-repeat: no-repeat;
        -webkit-background-size: cover;
        -moz-background-size: cover;
        background-size: cover;
        border-radius: 10px;
      }
    }

    .img-box-big {
      width: 60%;
      height: $width * 0.6;
    }
  }

  .title {
    color: #000000;
    line-height: 1.25rem;
    padding-left: 1rem;
    padding-right: 1rem;
    font-size: 1rem;
    font-weight: bold;
  }

  .fmttime {
    line-height: 2.25rem;
    text-align: right;
    padding-left: 1rem;
    padding-right: 1rem;
  }
}

.menu-box {
  width: 100%;
  height: 3.0625rem;
  padding: 0 0.25rem;
  background: white;
  display: flex;
  margin-top: 0.02 * $width;
  border-radius: 10px;
  .menu {
    width: 25%;
    height: 3.0625rem;
    font-size: 0.75rem;
    text-align: center;
    color: #000000;
    line-height: 3.0625rem;
    padding: 0 0.75rem;
    span {
      // margin-left: 0.5rem;
      margin-top: -10px;
      position: absolute;
    }
    .icon {
      width: 1.25rem;
      height: 1.25rem;
      // padding-bottom: 0.2rem;
    }
  }
}

.bottom-box {
  width: 100%;
  height: 3.4375rem;
  background: white;
  position: fixed;
  bottom: 0;
  padding: 0 0.4375rem;
  display: flex;
  font-size: 0.875rem;
  .view-comment {
    flex: 1;
    height: 2.25rem;
    line-height: 2.25rem;
    padding-left: 0.75rem;
    padding-right: 0.75rem;
    background: #F5F5F5;
    color: #bcbbbf;
    align-self: center;
    border-radius: 6.25rem;
    width: 13.4375rem;
  }
  .comment-send {
    align-self: center;
    margin-left: 0.75rem;
    margin-right: 0.25rem;
    font-weight: 400;
    color: #000000;
  }
  .menu {
    height: 3.4375rem;
    width: 4.375rem;
    font-size: 0.75rem;
    text-align: center;
    color: #bcbbbf;
    line-height: 3.4375rem;
    padding: 0 0.75rem;
    span {
      // margin-left: 0.5rem;
      margin-top: -10px;
      position: absolute;
    }
    .icon {
      width: 1.25rem;
      height: 1.25rem;
      // padding-bottom: 0.2rem;
    }
  }
}

.loading-bg-box {
  width: 100%;
  height: 100%;
  position: fixed;
  background: white;
  .loading-bg-default {
    width: 100%;
    position: absolute;
    overflow: auto;
    -webkit-animation-name: fadeInOut;
    -webkit-animation-timing-function: ease-in-out;
    -webkit-animation-iteration-count: infinite;
    -webkit-animation-duration: 1000ms;
    -webkit-animation-direction: alternate;
  }
}

.foot-center {
  text-align: center;
  &-one {
    width: 100%;
    top: 34px;
    position: absolute;
    text-align: center;
    font-size: 0.875rem;
    color: #ffffff;
  }
  &-two {
    width: 100%;
    top: 60px;
    position: absolute;
    text-align: center;
    font-size: 0.625rem;
    color: #ffffff;
  }
}
.foot-bottom {
  width: 100%;
  margin: 0 1rem;
  position: absolute;
  bottom: 0.9375rem;
  display: flex;
  &-img {
    line-height: 2.375rem;
    img {
      width: 2.375rem;
      height: 2.375rem;
    }
  }
  &-center {
    &-one {
      padding-left: 15px;
      width: 12rem;
      font-size: 0.875rem;
      color: #ffffff;
    }
    &-two {
      padding-left: 15px;
      width: 12rem;
      font-size: 0.625rem;
      color: #999999;
    }
  }
  &-right {
    width: 4.625rem;
    height: 1.5rem;
    background: -webkit-gradient(linear, left top, right top, from(#20A5FF), color-stop(0%, #20A5FF), to(#0068FF));
    background: -webkit-linear-gradient(left, #20A5FF 0%, #20A5FF 0%, #0068FF 100%);
    background: linear-gradient(90deg, #20A5FF 0%, #20A5FF 0%, #0068FF 100%);
    opacity: 1;
    border-radius: 100px;
    color: #ffffff;
    text-align: center;
    line-height: 1.5rem;
    font-size: 0.75rem;
    position: absolute;
    right: 2rem;
    top: 0.6125rem;
  }
}
img {
  object-fit: contain;
  width: 100%;
  text-align: center;
}
.foot-right-top {
  width: 104.5px;
  height: 24px;
  background: #FF8C00;
  opacity: 1;
  border-radius: 5px 20px 5px 20px;
  float: right;
  text-align: center;
  &-font {
    line-height: 24px;
    color: #ffffff;
  }
}
.foot {
  position: fixed;
    bottom: 0;
    width: 100%;
    height: 4.28125rem;
    background: #000000;
    opacity: 0.8;
}
</style>
