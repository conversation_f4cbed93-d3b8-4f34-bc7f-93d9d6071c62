<!--
 * @Description: In User Settings Edit
 * @Author: your name
 * @Date: 2019-10-16 11:28:20
 * @LastEditTime: 2019-10-16 11:38:48
 * @LastEditors: Please set LastEditors
 -->
<template>
  <div class="app-container">
    <div class="table-query-container">
      <el-button v-permission="'btn-xeasylabel-dict-edit'" @click="handleAdd(null)">新增</el-button>
    </div>
    <div class="table-query-container">
      字典类型：
      <el-select v-model="params.type" placeholder="选择类型" @change="params.pageNo=1;getList()">
        <el-option label="全部" value="" />
        <el-option
          v-for="item in distinctTypeList"
          :key="item.value"
          :label="item.name"
          :value="item.value"
        />
      </el-select>
    </div>
    <div class="table-container">
      <el-table
        ref="multipleTable"
        :data="list"
        tooltip-effect="dark"
        style="width: 100%"
        border
        max-height="700px"
      >
        <el-table-column label="排序" prop="sortNum" header-align="center" align="center" />
        <el-table-column label="名称" prop="name" header-align="center" align="center" />
        <el-table-column label="分类" prop="type" header-align="center" align="center" />
        <el-table-column label="标签" prop="label" header-align="center" align="center" />
        <el-table-column label="值" prop="value" header-align="center" align="center" />
        <el-table-column label="状态" prop="forbid" header-align="center" align="center" />
        <el-table-column label="备注" prop="remark" header-align="center" align="center" />
        <el-table-column label="操作" width="245" header-align="center" align="center">
          <template slot-scope="scope">
            <el-button size="mini" type="error" @click="handleEdit(scope.row)">编辑</el-button>
            <el-button v-permission="'btn-xeasylabel-dict-edit'" size="mini" type="error" @click="handleAdd(scope.row)">新增同类</el-button>
            <el-button v-permission="'btn-xeasylabel-dict-edit'" size="mini" type="error" @click="deleteDict(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="pagination-container" style="margin-top:20px;">
      <el-pagination
        :current-page="params.pageNo"
        :page-sizes="[10, 20, 30, 40]"
        :page-size="params.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
    <el-dialog
      :visible.sync="showDialog"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      width="60%"
      title="编辑字典项"
    >
      <el-form ref="form" :model="form" label-width="100px" class="edit-form">
        <el-form-item label="字典名称" prop="name">
          <el-input v-model="form.name" />
        </el-form-item>
        <el-form-item label="字典类别" prop="type">
          <el-input v-model="form.type" />
        </el-form-item>
        <el-form-item label="标签" prop="label">
          <el-input v-model="form.label" />
        </el-form-item>
        <el-form-item label="值" prop="value">
          <el-input v-model="form.value" />
        </el-form-item>
        <el-form-item label="图标" prop="icon">
          <el-upload
            class="avatar-uploader"
            action=""
            :show-file-list="false"
            :on-change="changePic"
            :before-upload="beforeUpload"
            accept="image/png, image/jpeg"
          >
            <img v-if="form.icon" :src="form.icon" class="avatar">
            <i v-else class="el-icon-plus avatar-uploader-icon" />
          </el-upload>
          <img v-show="false" :src="testUrl" class="avatar">
        </el-form-item>
        <el-form-item label="排序" prop="sortNum">
          <el-input
            v-model="form.sortNum"
            type="text"
          />
        </el-form-item>
        <el-form-item label="状态" prop="forbid">
          <el-radio-group v-model="form.forbid">
            <el-radio-button label="0">启用</el-radio-button>
            <el-radio-button label="1">禁用</el-radio-button>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="form.remark"
            type="text"
          />
        </el-form-item>
        <el-form-item>
          <el-button @click="handleClose">返回</el-button>
          <el-button v-permission="'btn-xeasylabel-dict-edit'" type="primary" @click="submit">保存</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>
<script>
import { fetchListByPage, deleteRecord, saveRecord, fetchDistinctTypeList } from '@/api/initialtrain/dict'
import AliyunOSS from '@/utils/aliyunOSS'
const oss = new AliyunOSS()
export default {
  data() {
    return {
      // 查询参数
      params: {
        pageNo: 1,
        pageSize: 10,
        type: ''
      },
      printerTypeLabelJxz: [],
      printerTypeLabelLxz: [],
      printerTypeLabelLanguage: [],
      distinctTypeList: [],
      cacheKey: '',
      /** 列表 */
      list: [],
      /** 总条数 */
      total: 0,
      downloadUrl: '',
      /** 时间段 */
      timeRange: '',
      timeRange2: '',
      keyword: '',
      form: {
        name: '',
        type: '',
        label: '',
        value: '',
        icon: '',
        sortNum: 0,
        forbid: 0,
        remark: ''
      },
      /** 时间格式 */
      timeFormat: 'yyyy-MM-dd',
      showDialog: false,
      testUrl: '',
      /** 待上传的文件 */
      upFile: undefined
    }
  },
  watch: {
    formData: function(val) {
      this.upFile = undefined
      this.form = { ...val }
    }
  },
  mounted() {
    this.getTypeList()
    this.getList()
  },
  methods: {
    async getTypeList() {
      const res = await fetchDistinctTypeList()
      if (res.head.ret === 0) {
        this.distinctTypeList = res.data
      }
    },
    async getList() {
      const res = await fetchListByPage(this.params)
      if (res.head.ret === 0) {
        this.list = res.data.result
        this.total = res.data.totalCount
      }
    },
    /** 切换每页条数 */
    handleSizeChange(val) {
      this.params.pageSize = val
      this.getList()
    },
    /** 切换当前页 */
    handleCurrentChange(val) {
      this.params.pageNo = val
      this.getList()
    },
    /** 查询 */
    handleQuery() {
      this.params.pageNo = 1
      this.params.pageSize = 10
      this.getList()
    },
    /** 查看动态 */
    handleEdit(row) {
      this.form = row
      if (this.form.forbid === undefined) {
        this.form.forbid = 0
      }
      this.showDialog = true
    },
    deleteDict(row) {
      const parms = {
        id: row.id
      }
      this.$confirm('此操作将永久删除，不可恢复, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteRecord(parms).then(res => {
          if (res.head.ret === 0) {
            this.$message.success('删除成功')
            this.getList()
            this.getTypeList()
          }
        })
      }).catch(() => {
      })
    },
    /** 提交 */
    submit() {
      if (this.upFile) {
        oss.uploadFile(this.upFile, (result, error) => {
          if (error) {
            this.$message.error('上传文件失败')
            return -1
          }
          if (result) {
            console.log('上传完图片后返回的数据', result)
            this.form.icon = result.name
            this.handleSave(this.form)
          }
        })
      } else {
        // 直接保存
        this.handleSave(this.form)
      }
    },
    handleSave(parms) {
      saveRecord(parms).then(res => {
        if (res.head.ret === 0) {
          this.$message.success('保存成功')
          this.getList()
          this.getTypeList()
        }
        this.showDialog = false
      })
    },
    handleAdd(row) {
      if (row) {
        this.form = {
          type: row.type,
          name: row.name,
          label: '',
          value: '',
          localeCode: '',
          icon: '',
          sortNum: 0
        }
      } else {
        this.form = {
          name: '',
          type: '',
          label: '',
          value: '',
          localeCode: '',
          icon: '',
          sortNum: 0
        }
      }
      this.showDialog = true
    },
    /** 选择图片*/
    changePic(file) {
      this.upFile = file.raw
      this.form.icon = URL.createObjectURL(file.raw)
      this.testUrl = URL.createObjectURL(file.raw)
      return false
    },
    /** 上传前 */
    beforeUpload(file) {
      return false
    },
    /** 返回 */
    handleClose(code) {
      this.showDialog = false
    }
  }
}
</script>

<style lang="scss" scoped>

</style>
