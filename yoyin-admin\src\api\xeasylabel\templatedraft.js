import request from '@/utils/request'

const getListUrl = '/api/xeasylabel/xeasylabel/v1/templatedrafts/getPage'
const getUserTemplateShowListUrl = '/api/xeasylabel/xeasylabel/v1/templatedrafts/getUserTemplateShowList'
const saveUrl = '/api/xeasylabel/xeasylabel/v1/templatedrafts/addOrUpdate'
const deleteUrl = '/api/xeasylabel/xeasylabel/v1/templatedrafts/deleteById'

const trunUrl = '/api/xeasylabel/xeasylabel/v1/templatedrafts/turnusertemplateshow'

export const fetchListByPage = async params => {
  return request({
    url: getListUrl,
    method: 'get',
    params
  })
}

export const getLanguageConfig = async params => {
  return request({
    url: getUserTemplateShowListUrl,
    method: 'get',
    params
  })
}

export const turnConfig = async params => {
  return request({
    url: trunUrl,
    method: 'get',
    params
  })
}

export const saveTemplateDrafts = async params => {
  return request({
    url: saveUrl,
    method: 'post',
    data: params
  })
}

export const deleteTemplateDrafts = async params => {
  return request({
    url: deleteUrl,
    method: 'post',
    data: params
  })
}
