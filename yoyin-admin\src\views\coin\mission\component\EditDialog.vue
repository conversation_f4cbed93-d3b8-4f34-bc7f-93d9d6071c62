<template>
  <div v-if="visiable" class="edit-dialog">
    <el-dialog
      ref="dialog"
      :show-close="false"
      :title="form.id === '' ? '新增' : '编辑' "
      :visible.sync="visiable"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      width="50%"
      center
    >
      <el-form ref="form" :model="form" :rules="rules" label-width="100px" class="edit-form">
        <el-form-item label="任务类别" prop="category">
          <el-input v-model="form.category" />
        </el-form-item>
        <el-form-item label="任务名称" prop="name">
          <el-input v-model="form.name" />
        </el-form-item>
        <el-form-item label="任务编码" prop="code">
          <el-input v-model="form.code" />
        </el-form-item>
        <el-form-item label="详情描述" prop="info">
          <el-input v-model="form.info" />
        </el-form-item>
        <el-form-item label="系统发放消息" prop="sendMess">
          <el-input v-model="form.sendMess" />
        </el-form-item>
        <el-form-item label="跳转描述" prop="jumpDesc">
          <el-input v-model="form.jumpDesc" />
        </el-form-item>
        <el-row>
          <el-col :span="8">
            <el-form-item label="积分数量" prop="costNum">
              <el-input v-model="form.costCoin" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="每日最多" prop="stockNum">
              <el-input v-model="form.dayTotal" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="次数" prop="isNews">
              <el-input v-model="form.dayNum" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="类型" prop="type">
          <el-radio-group v-model="form.type">
            <el-radio-button label="0">每日</el-radio-button>
            <el-radio-button label="1">终生一次</el-radio-button>
            <el-radio-button label="2">特殊</el-radio-button>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="领取方式" prop="autoPay">
          <el-radio-group v-model="form.autoPay">
            <el-radio-button label="0">手动</el-radio-button>
            <el-radio-button label="1">自动</el-radio-button>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="排序" prop="sortNum">
          <el-input v-model="form.sortNum" />
        </el-form-item>
        <el-form-item label="andriod调用参数" prop="paramAndroid">
          <el-input v-model="form.paramAndroid" />
        </el-form-item>
        <el-form-item label="ios调用参数" prop="paramIos">
          <el-input v-model="form.paramIos" />
        </el-form-item>
        <el-form-item>
          <el-button @click="handleClose">返回</el-button>
          <el-button v-permission="'btn-menuCoin-mission-edit'" type="primary" @click="submit">保存</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>

    <el-dialog
      ref="dialogxxx"
      :show-close="true"
      :title="'文件列表编辑'"
      :visible.sync="showDialog"
      width="50%"
      center
    >
      <el-table
        ref="multipleTable"
        :data="fileList"
        tooltip-effect="dark"
        style="width: 100%"
        border
      >
        <el-table-column label="缩略图">
          <template slot-scope="scope">
            <a :href="scope.row.pic" target="_blank">
              <el-avatar shape="square" :size="80" :src="scope.row.pic" />
            </a>
          </template>
        </el-table-column>
        <el-table-column label="图片名称">
          <template slot-scope="scope">
            {{ scope.row.name }}
          </template>
        </el-table-column>
        <el-table-column label="是否打印">
          <template slot-scope="scope">
            <el-radio-group v-model="scope.row.printFlag">
              <el-radio-button label="0">不打印</el-radio-button>
              <el-radio-button label="1">打印</el-radio-button>
            </el-radio-group>
          </template>
        </el-table-column>
        <el-table-column label="不分享能否阅读">
          <template slot-scope="scope">
            <el-radio-group v-model="scope.row.canRead">
              <el-radio-button label="0">不能</el-radio-button>
              <el-radio-button label="1">能</el-radio-button>
            </el-radio-group>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="160">
          <template slot-scope="scope">
            <el-button type="primary" icon="el-icon-top" circle @click="move(scope.$index, -1)" />
            <el-button type="primary" icon="el-icon-bottom" circle @click="move(scope.$index, 1)" />
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
  </div>
</template>

<script>
import { saveMissionRecord } from '@/api/coin/mission'

export default {
  name: 'EditDialog',
  props: {
    visiable: {
      type: Boolean,
      required: true,
      default: false
    },
    formData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      /** 表单 */
      form: {
        pics: []
      },
      /** 校验规则 */
      rules: {
        name: [
          { required: true, message: '请输入标题', trigger: 'blur' }
        ]
      },
      /** 待上传的文件 */
      upFile: undefined,
      testUrl: '',
      /** 时间格式 */
      timeFormat: 'yyyy-MM-dd HH:mm:ss',
      fileList: [],
      preUploadList: [],
      showDialog: false,
      subjectList: [],
      checkboxGroup1: []
    }
  },
  computed: {
  },
  watch: {
    formData: function(val) {
      this.form = { ...val }
    }
  },
  mounted() {
    this.show = this.visiable
  },
  methods: {
    move(index, action) {
      if (action === 1) {
        if (index !== this.fileList.length - 1) {
          this.fileList[index] = this.fileList.splice(index + 1, 1, this.fileList[index])[0]
        }
      } else {
        if (index !== 0) {
          this.fileList[index] = this.fileList.splice(index - 1, 1, this.fileList[index])[0]
        }
      }
    },
    /** 返回 */
    handleClose(code) {
      this.$emit('closeDialog', code)
    },
    /** 提交 */
    submit() {
      if (this.checkboxGroup1) {
        this.form.gradeLevel = this.checkboxGroup1.join(',')
      } else {
        this.form.gradeLevel = ''
      }
      this.$refs['form'].validate(valid => {
        if (valid) {
          // 直接保存
          this.addUpdateRecord()
        } else {
          console.log('submit error')
          return -1
        }
      })
    },
    async addUpdateRecord() {
      // 新增
      if (!this.form.id) {
        this.form.id = ''
      }
      // 更新
      // console.log('fileList=', this.fileList)
      // console.log('form.pics=', this.form.pics)
      this.form.pics = this.fileList
      const res = await saveMissionRecord(this.form)
      if (res.head.ret === 0) {
        this.$message.success('保存成功')
        this.handleClose('1')
      }
    }
  }

}
</script>

<style lang='scss' scoped>
  .avatar-uploader .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }
  .avatar-uploader .el-upload:hover {
    border-color: #409EFF;
  }
  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 178px;
    height: 178px;
    line-height: 178px;
    text-align: center;
  }
  .avatar {
    width: 178px;
    height: 178px;
    display: block;
  }
</style>
