/*
 * @Description: In User Settings Edit
 * @Author: your name
 * @Date: 2019-10-16 10:58:32
 * @LastEditTime: 2019-10-23 09:28:49
 * @LastEditors: Please set LastEditors
 */
import axios from 'axios'
import { MessageBox, Message } from 'element-ui'
import { BASE_URL } from '@/consts/index'
import store from '@/store'
// import { removeToken } from '@/utils/auth'

// import { getToken } from '@/utils/auth'
// 无效token
const TOKEN_INVALID = 2
// token为空
const TOKEN_NULL = 3

// create an axios instance
const service = axios.create({
  // baseURL: process.env.VUE_APP_BASE_API, // url = base url + request url
  baseURL: BASE_URL, // url = base url + request url
  withCredentials: true, // send cookies when cross-domain requests
  timeout: 500000 // request timeout
})

// request interceptor
service.interceptors.request.use(
  config => {
    // do something before request is sent

    if (store.getters.token) {
      // let each request carry token
      // ['X-Token'] is a custom headers key
      // please modify it according to the actual situation
      // config.headers['jwttoken'] = getToken()
      config.headers['jwttoken'] = store.getters.token
    }
    // add agent
    config.headers['sadais-agent'] = 'HEALTHWEIGHT_PLATFORM/2.0/WEB/HEALTH///'
    config.headers['Content-Type'] = 'application/json;charset=UTF-8'
    return config
  },
  error => {
    // do something with request error
    console.log(error) // for debug
    return Promise.reject(error)
  }
)

// response interceptor
service.interceptors.response.use(
  /**
   * If you want to get http information such as headers or status
   * Please return  response => response
  */

  /**
   * Determine the request status by custom code
   * Here is just an example
   * You can also judge the status by HTTP Status Code
   */
  response => {
    const res = response.data
    if (res.head.ret !== TOKEN_INVALID && res.head.ret !== TOKEN_NULL) {
      return res
    } else {
      // 跳转去登录页面
      // @HACK
      /* eslint-disable no-underscore-dangle */
      // to re-login
      MessageBox.confirm('当前授权无效或已过期，请重新登录', '提示', {
        confirmButtonText: '重新登录',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        store.dispatch('user/resetToken').then(() => {
          location.reload()
        })
      })
      // removeToken()
      // location.href = '/login'
      return res
    }
    // // 根据头部head中的ret 为 0 判断是否有误
    // if (res.head.ret !== 0) {
    //   return Promise.reject(new Error(res.head.msg || 'Error'))
    // }

    // // if the custom code is not 20000, it is judged as an error.
    // if (res.code !== 20000) {
    //   console.log('err from request.js  response : ' + res.code) // for debug
    //   Message({
    //     message: res.message || 'Error',
    //     type: 'error',
    //     duration: 5 * 1000
    //   })

    //   // 50008: Illegal token; 50012: Other clients logged in; 50014: Token expired;
    //   if (res.code === 50008 || res.code === 50012 || res.code === 50014) {
    //     // to re-login
    //     MessageBox.confirm('You have been logged out, you can cancel to stay on this page, or log in again', 'Confirm logout', {
    //       confirmButtonText: 'Re-Login',
    //       cancelButtonText: 'Cancel',
    //       type: 'warning'
    //     }).then(() => {
    //       store.dispatch('user/resetToken').then(() => {
    //         location.reload()
    //       })
    //     })
    //   }
    //   return Promise.reject(new Error(res.message || 'Error'))
    // } else {
    //   return res
    // }
    // return res
  },
  error => {
    console.log('err from request.js  error : ' + error) // for debug
    Message({
      message: error.message,
      type: 'error',
      duration: 5 * 1000
    })
    return Promise.reject(error)
  }
)

export default service
