<template>
  <div>
      <div style="padding-top:90px;"></div>
      <div class="swiper-container-class">
        <div class="swiper-container">
          <div class="swiper-wrapper">
            <div class="swiper-slide">
              <img :src="banner1" @click="navBanner(1)" style="cursor: pointer;"/>
            </div>
            <div class="swiper-slide">
              <img :src="banner2" @click="navBanner(3)" style="cursor: pointer;"/>
            </div>
            <div class="swiper-slide">
              <img :src="banner3" @click="navBanner(2)" style="cursor: pointer;"/>
            </div>
          </div>
          <div class="swiper-pagination"></div>
          <!--分页器。如果放置在swiper-container外面，需要自定义样式。-->
        </div>
      </div>

      <div style="padding-top:90px;"></div>
      <div class="one">
        <div class="blankDiv" @click="navBanner(1)"></div>
      </div>
      <div class="line-button" @click="navBanner(1)">了解详情</div>

      <div style="padding-top:120px;"></div>
      <div class="two">
        <div class="blankDiv" @click="navBanner(2)"></div>
      </div>
      <div class="line-button" @click="navBanner(2)">了解详情</div>

      <div style="padding-top:120px;"></div>
      <div class="three">
        <div class="blankDiv" @click="navBanner(3)"></div>
      </div>
      <div class="line-button" @click="navBanner(3)">了解详情</div>

      <div style="padding-top:120px;"></div>
      <div class="four">
        <div class="blankDiv" @click="navPaper(1)"></div>
      </div>
      <div class="fflex">
          <div><img src="https://m.yoyin.net/h5/img/web/home/<USER>" @click="navPaper(1)" style="cursor: pointer;"></div>
          <div><img src="https://m.yoyin.net/h5/img/web/home/<USER>" @click="navPaper(2)" style="cursor: pointer;"></div>
          <div><img src="https://m.yoyin.net/h5/img/web/home/<USER>" @click="navPaper(3)" style="cursor: pointer;"></div>
          <div><img src="https://m.yoyin.net/h5/img/web/home/<USER>" @click="navPaper(4)" style="cursor: pointer;"></div>
      </div>
  </div>
</template>

<script>
import Swiper from 'swiper'
import 'swiper/css/swiper.css'
export default {
  name: 'MyHome',
  data () {
    return {
      swImgs: ['https://lupic.cdn.bcebos.com/20210629/14912108_14.jpg', 'https://lupic.cdn.bcebos.com/20210629/4921758_14.jpg', 'https://lupic.cdn.bcebos.com/20210629/4921758_14.jpg', 'https://lupic.cdn.bcebos.com/20210629/4921758_14.jpg'],
      swiperOptions: {
        pagination: {
          el: '.swiper-pagination', // 分页标签
          clickable: true // 允许分页点击跳转
        },
        navigation: { // 左右按钮
          nextEl: '.swiper-button-next',
          prevEl: '.swiper-button-prev'
        },
        // 设定初始化时slide的索引
        initialSlide: 2,
        // 如果需要滚动条
        scrollbar: {
          el: '.swiper-scrollbar'
        },
        loop: true, // 无限循环,
        // autoplay: true,//可选选项，自动滑动
        autoplay: {
          delay: 5000,
          stopOnLastSlide: false,
          disableOnInteraction: true
        } // ,
        // effect: 'cube' // 切换特效,
      },
      banner1: 'https://m.yoyin.net/h5/img/web/home/<USER>',
      banner2: 'https://m.yoyin.net/h5/img/web/home/<USER>',
      banner3: 'https://m.yoyin.net/h5/img/web/home/<USER>'
    }
  },
  mounted () {
    // 调用延迟加载 $nextTick
    this.$nextTick(() => {
      let swiper = new Swiper('.swiper-container', {
        // effect: 'flip',
        // 是否循环
        loop: true,
        speed: 3000,
        autoplay: {
          // swiper手动滑动之后自动轮播失效的解决方法,包括触碰，拖动，点击pagination,重新启动自动播放
          disableOnInteraction: false,
          // 自动播放时间：毫秒
          delay: 8000
        },
        pagination: {
          // 小圆点
          el: '.swiper-pagination'
        }
      })
      console.log(swiper)
    })
  },
  methods: {
    nav (parms) {
      if (parms === 'one') {
        // 导航到xxx
      }
    },
    navBanner (index) {
      if (index === 1) {
        window.open('https://detail.tmall.com/item.htm?abbucket=16&id=678534899920&rn=093526b5ee68cb8a0c99ae42e6ed008b&spm=a1z10.5-b-s.w4011-23962026788.47.2c123825whMdCH', '_blank')
      } else if (index === 2) {
        window.open('https://detail.tmall.com/item.htm?spm=a1z10.5-b-s.w4011-23962026788.43.e72f6927Ygqaq7&id=************&rn=b2dfb92706d8b22a43b328ee4d694f46&abbucket=4&sku_properties=1627207:28332', '_blank')
      } else if (index === 3) {
        window.open('https://detail.tmall.com/item.htm?id=************&rn=093526b5ee68cb8a0c99ae42e6ed008b&spm=a1z10.5-b-s.w4011-23962026788.61.2c123825whMdCH&sku_properties=1627207:28320', '_blank')
      }
    },
    navPaper (index) {
      if (index === 1) {
        window.open('https://detail.tmall.com/item.htm?id=************&rn=093526b5ee68cb8a0c99ae42e6ed008b&skuId=4731524268418&spm=a1z10.5-b-s.w4011-23962026788.53.2c123825whMdCH', '_blank')
      } else if (index === 2) {
        window.open('https://detail.tmall.com/item.htm?id=************&rn=093526b5ee68cb8a0c99ae42e6ed008b&skuId=4731524268417&spm=a1z10.5-b-s.w4011-23962026788.53.2c123825whMdCH', '_blank')
      } else if (index === 3) {
        window.open('https://detail.tmall.com/item.htm?id=************&rn=093526b5ee68cb8a0c99ae42e6ed008b&skuId=4731524268420&spm=a1z10.5-b-s.w4011-23962026788.53.2c123825whMdCH', '_blank')
      } else if (index === 4) {
        window.open('https://detail.tmall.com/item.htm?id=************&rn=093526b5ee68cb8a0c99ae42e6ed008b&skuId=4731524268416&spm=a1z10.5-b-s.w4011-23962026788.53.2c123825whMdCH', '_blank')
      } else {
        // 跳转到总店首页
        window.open('https://youyinbgyp.tmall.com/index.htm?spm=a220o.1000855.w5002-23962026762.2.4d0cf2eb5Ir5sG', '_blank')
      }
    }
  }
}
</script>

<style scoped>
img {
    min-width: 1200px;
    max-width: 1920px;
    object-fit: cover;
}
span {
    color: #5EBF81;
    font-size: 24px;
    font-weight: bold;
    padding-right: 15px;
}
ul{
    padding-inline-start: 20px;
    font-weight: bold;
}
.one {
    background-image: url(https://m.yoyin.net/h5/img/web/home/<USER>
    background-repeat: no-repeat;
    background-size: 1200px 781px;
    height: 781px;
    background-position: 50% 50%;
}
.blankDiv {
  cursor: pointer;
  width: 1200px;
  height: 100%;
  margin: 0 auto;
}
.one-content {
    width: 500px;
    float: right;
    position: relative;
    font-weight: bold;
    text-align: left;
}
.one-content-title {
    padding-top: 200px;
}
.one-content-row {
    padding-top: 80px;
}
.one-content-li {
    padding-top: 40px;
}
.two {
    background-image: url(https://m.yoyin.net/h5/img/web/home/<USER>
    background-repeat: no-repeat;
    background-size: 1200px 781px;
    height: 781px;
    background-position: 50% 50%;
}
.two-title {
    padding: 236px 0 70px 0;
}
.three {
    background-image: url(https://m.yoyin.net/h5/img/web/home/<USER>
    background-repeat: no-repeat;
    background-size: 1200px 781px;
    height: 781px;
    background-position: 50% 50%;
}
.four {
    background-image: url(https://m.yoyin.net/h5/img/web/home/<USER>
    background-repeat: no-repeat;
    background-size: 1200px 600px;
    height: 600px;
    background-position: 50% 50%;
}

.three-content {
    width: 120px;
    float: right;
    position: relative;
    font-weight: bold;
    text-align: left;
}
.three-title {
    padding: 236px 0 70px 0;
}

.swiper-container {
  width: 100%;
  /* height: 35.2rem; */
  /* background-color: #F5F5F5; */
  z-index: 9999;
}

.swiper-container-class{
 width:100%;
    opacity: 1;
    margin-bottom: 4.3rem;
    /* background-color: #F5F5F5; */
}
.swiper-slide img {
    /* width: 27.8rem;
    height: 35.2rem; */
    border-radius: 1rem;
    /* width: 85%; */
    object-fit: contain;
}

.line-button {
    margin-top: 15px;
    margin: 50px auto;
    width: 116px;
    height: 40px;
    line-height: 40px;
    border-radius: 20px 20px 20px 20px;
    opacity: 1;
    border: 1px solid #63C184;
    color: #63C184;
    cursor: pointer;
}
.fflex {
    display:flex;
    justify-content: center;
    align-items: center;
    justify-content: center;
    /* padding-top: 100px; */
    padding-top: 10px;
}
.fflex img {
  height: 332px!important;
  min-width: 25%!important;
  object-fit: contain!important;
  margin: 10px 10px;
}
</style>
