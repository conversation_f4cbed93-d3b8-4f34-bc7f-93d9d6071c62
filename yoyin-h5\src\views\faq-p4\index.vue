<template>
  <div class="faq">
    <div class="faq-title2">
      {{$t(`faq-p4.q`)}}
    </div>
    <div class="faq-content2">
      <p class="first-line">{{$t(`faq-p4.a1`)}}</p>
      <p class="first-line">{{$t(`faq-p4.a2`)}}</p>
      <p class="first-line">{{$t(`faq-p4.a3`)}}</p>
      <p class="first-line" v-show="needshow"><img :src="$t(`faq-p4.img1`)" /></p>
    </div>

  </div>
</template>

<script>
import { getUrlParameter } from '@/utils/common'
export default {
  data () {
    return {
      moduleKey: 'faq',
      needshow: false
    }
  },
  async mounted () {
    this.gettingNeedshow()
  },
  methods: {
    gettingNeedshow() {
      const query = getUrlParameter()
      if (query && query.needshow) {
        this.needshow = query.needshow
      }
    }
  },
  components: {}
}
</script>

<style lang="scss">
@import '../../assets/scss/faq.scss'; // 全局faq自定义的css样式
</style>
