import { browserInfo } from '@/utils/common'
import { BASE_URL } from '@/consts/index'

const currentHref = encodeURIComponent(window.location.href)

/**
 * 解密信息
 */
const wxAuthUrl = encodeURIComponent(
  `${BASE_URL}/api/gam/community/v1/wx/getuserinfo`
)

// test-https://m.starpany.cn/test/h5/feed/views/oauth-transfer.html
// pro-https://m.starpany.cn/h5/feed/views/oauth-transfer.html
const qqAuthUrl = encodeURIComponent(
  `https://m.starpany.cn/h5/feed/views/oauth-transfer.html`
)

/**
 * 微信授权地址
 */
const wxRediectUrl = `https://open.weixin.qq.com/connect/oauth2/authorize?appid=wx92d53b2b360320e1&redirect_uri=${wxAuthUrl}&response_type=code&scope=snsapi_userinfo&state=${currentHref}#wechat_redirect`

const qqRediectUrl = `https://graph.qq.com/oauth2.0/authorize?response_type=token&client_id=101526977&redirect_uri=${qqAuthUrl}&state=${currentHref}&display=pc`

export const login = () => {
  const browser = browserInfo()
  if (browser.isWeixin) {
    window.location.href = wxRediectUrl
    return wxRediectUrl
  } else {
    console.log(qqRediectUrl)
    return (window.location.href = qqRediectUrl)
  }
}

export const oauthData2User = oauthData => {
  const defaultInfo = {
    userName: '匿名',
    userPic: '',
    sex: '女',
    from: 'qq',
    unionid: ''
  }
  if (!oauthData) return defaultInfo
  oauthData = JSON.parse(oauthData)
  const browser = browserInfo()
  if (!browser) return
  console.log('oauthData2User: ', oauthData)
  if (browser.isWeixin) {
    return {
      userName: oauthData.userName,
      userPic: oauthData.headimgurl,
      sex: oauthData.sex === '1' ? '男' : '女',
      from: 'wx',
      unionid: oauthData.unionid
    }
  } else {
    return {
      userName: oauthData.userName,
      userPic: oauthData.headimgurl,
      sex: oauthData.sex,
      from: 'qq',
      unionid: oauthData.unionid
    }
  }
}
