import request from '@/utils/request'

const getListUrl = '/platform/gam/community/v1/help/getpagelist'
const uplateUrl = '/platform/gam/community/v1/help/updateitem'
const deleteUrl = '/platform/gam/community/v1/help/deletebyid'
const addUrl = '/platform/gam/community/v1/help/additem'

export const fetchListByPage = async params => {
  return request({
    url: getListUrl,
    method: 'get',
    params
  })
}

export const addItem = async params => {
  return request({
    url: addUrl,
    method: 'post',
    data: params
  })
}

export const updateItem = async params => {
  return request({
    url: uplateUrl,
    method: 'post',
    data: params
  })
}

export const deleteItem = async params => {
  return request({
    url: deleteUrl,
    method: 'post',
    data: params
  })
}

