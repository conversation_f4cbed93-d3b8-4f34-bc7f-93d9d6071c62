<!--
 * @Description: In User Settings Edit
 * @Author: your name
 * @Date: 2019-10-16 11:28:20
 * @LastEditTime: 2019-10-16 11:38:48
 * @LastEditors: Please set LastEditors
 -->
<template>
  <div class="app-container">
    <div class="table-container">
      <el-table
        ref="multipleTable"
        :data="list"
        tooltip-effect="dark"
        style="width: 100%"
        border
      >
        <el-table-column label="选择">
          <template slot-scope="scope">
            <el-button size="mini" @click="handleEdit(scope.$index, scope.row)">选择</el-button>
          </template>
        </el-table-column>
        <el-table-column label="外框" width="100">
          <template slot-scope="scope">
            <a v-if="scope.row.borderUrl" :href="scope.row.url" target="_blank">
              <el-avatar shape="square" :size="80" :src="scope.row.borderUrl" />
            </a>
          </template>
        </el-table-column>
        <el-table-column label="标签" width="100">
          <template slot-scope="scope">
            <a v-if="scope.row.nameUrl" :href="scope.row.url" target="_blank">
              <el-avatar shape="square" :size="80" :src="scope.row.nameUrl" />
            </a>
          </template>
        </el-table-column>
        <el-table-column label="名称" width="200">
          <template slot-scope="scope">{{ scope.row.name }}</template>
        </el-table-column>
        <el-table-column label="编码" width="120">
          <template slot-scope="scope">{{ scope.row.code }}</template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>
<script>
import { fetchListByPage } from '@/api/user/titleType'
export default {
  // components: {
  //   EditDialog
  // },
  props: {
    userId: {
      type: String,
      request: true,
      default: ''
    },
    titleType: {
      type: Number,
      request: false,
      default: 0
    }
  },
  data() {
    return {
      // 查询参数
      params: {
        pageno: 1,
        pagesize: 10
      },
      /** 列表 */
      list: [],
      /** 总条数 */
      total: 0,
      /** 消息体 */
      banner: { },
      /** 勾选数据 */
      multipleSelection: [],
      /** 时间段 */
      timeRange: '',
      /** 时间格式 */
      timeFormat: 'yyyy-MM-dd',
      /** 显示编辑框 */
      showDialog: false,
      /** 科目列表 */
      subjectList: [],
      form: {},
      type: 0
    }
  },
  mounted() {
    this.getList()
  },
  methods: {
    /** 获取列表 */
    async getList() {
      const res = await fetchListByPage(this.params)
      if (res.head.ret === 0) {
        this.list = res.data
        // this.total = res.data.totalCount
      }
    },
    /** 编辑 */
    handleEdit(index, row) {
      this.$emit('handleSelectTitleType', {
        userId: this.userId,
        titleType: row.code
      })
    },
    handleClose() {
      this.showDialog = false
    }
  }
}
</script>

<style lang="scss" scoped>

</style>
