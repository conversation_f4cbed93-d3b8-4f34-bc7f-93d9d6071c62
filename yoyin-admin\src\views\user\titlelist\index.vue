<!--
 * @Description: In User Settings Edit
 * @Author: your name
 * @Date: 2019-10-16 11:28:20
 * @LastEditTime: 2019-10-16 11:38:48
 * @LastEditors: Please set LastEditors
 -->
<template>
  <div class="app-container">
    <div class="table-query-container">
      <el-button v-permission="'btn-menuCoin-title-edit'" @click="handleAdd">新增</el-button>
    </div>
    <!-- <div class="dialog-container">
      <EditDialog :visiable="showDialog" :form-data="banner" @closeDialog="closeDialog" />
    </div> -->

    <div class="table-container">
      <el-table
        ref="multipleTable"
        :data="list"
        tooltip-effect="dark"
        style="width: 100%"
        border
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column label="外框" width="100">
          <template slot-scope="scope">
            <a v-if="scope.row.borderUrl" :href="scope.row.url" target="_blank">
              <el-avatar shape="square" :size="80" :src="scope.row.borderUrl" />
            </a>
          </template>
        </el-table-column>
        <el-table-column label="标签" width="100">
          <template slot-scope="scope">
            <a v-if="scope.row.nameUrl" :href="scope.row.url" target="_blank">
              <el-avatar shape="square" :size="80" :src="scope.row.nameUrl" />
            </a>
          </template>
        </el-table-column>
        <el-table-column label="名称" width="200">
          <template slot-scope="scope">{{ scope.row.name }}</template>
        </el-table-column>
        <el-table-column label="编码" width="120">
          <template slot-scope="scope">{{ scope.row.code }}</template>
        </el-table-column>
        <el-table-column label="操作">
          <template slot-scope="scope">
            <el-button size="mini" @click="handleEdit(scope.$index, scope.row)">编辑</el-button>
            <el-button
              v-permission="'btn-menuCoin-title-edit'"
              size="mini"
              type="danger"
              @click="handleDeleteRow(scope.$index, scope.row)"
            >删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <!-- <div class="pagination-container" style="margin-top:20px;">
      <el-pagination
        :current-page="params.pageno"
        :page-sizes="[10, 20, 30, 40]"
        :page-size="params.pagesize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div> -->
    <el-dialog
      ref="dialog"
      :show-close="true"
      :title="form.id === '' ? '新增' : '编辑' "
      :visible.sync="showDialog"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      width="50%"
      center
    >
      <el-form ref="form" :model="form" label-width="150px" class="edit-form">
        <el-form-item label="编号" prop="code">
          <el-input-number v-model="form.code" label="编号" />
        </el-form-item>
        <el-form-item label="名称" prop="name">
          <el-input v-model="form.name" />
        </el-form-item>
        <el-form-item label="外框" prop="borderUrl">
          <el-upload
            class="avatar-uploader"
            action="https://httpbin.org/post"
            :http-request="myUploadImageMethod"
            :on-remove="handleRemove"
            :on-success="handleSuccess"
            :before-upload="beforeUpload"
            :show-file-list="false"
            accept="image/png, image/jpeg"
          >
            <img v-if="form.borderUrl" :src="form.borderUrl" class="avatar">
            <i v-else class="el-icon-plus avatar-uploader-icon" />
          </el-upload>
        </el-form-item>
        <el-form-item label="标签图案" prop="nameUrl">
          <el-upload
            class="avatar-uploader"
            action="https://httpbin.org/post"
            :http-request="myUploadImageMethod2"
            :on-remove="handleRemove2"
            :on-success="handleSuccess2"
            :before-upload="beforeUpload2"
            :show-file-list="false"
            accept="image/png, image/jpeg"
          >
            <img v-if="form.nameUrl" :src="form.nameUrl" class="avatar">
            <i v-else class="el-icon-plus avatar-uploader-icon" />
          </el-upload>
        </el-form-item>
        <el-form-item label="是否只允许官方授权" prop="isOfficial">
          <el-radio-group v-model="form.isOfficial">
            <el-radio-button label="0">是</el-radio-button>
            <el-radio-button label="1">否</el-radio-button>
          </el-radio-group>
          <el-alert title="用户能否自己设置此头衔，‘官方’则不能，‘其他’则可以；后续需求扩张使用" type="success" :closable="false" />
        </el-form-item>
        <el-form-item>
          <el-button @click="handleClose">返回</el-button>
          <el-button v-permission="'btn-menuCoin-title-edit'" type="primary" @click="submit">保存</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>
<script>
import { fetchListByPage, deleteRecord, saveRecord } from '@/api/user/titleType'
import AliyunOSS from '@/utils/aliyunOSS'
const oss = new AliyunOSS()
// import EditDialog from './component/EditDialog'
export default {
  // components: {
  //   EditDialog
  // },
  data() {
    return {
      // 查询参数
      params: {
        pageno: 1,
        pagesize: 10
      },
      /** 列表 */
      list: [],
      /** 总条数 */
      total: 0,
      /** 消息体 */
      banner: { },
      /** 勾选数据 */
      multipleSelection: [],
      /** 时间段 */
      timeRange: '',
      /** 时间格式 */
      timeFormat: 'yyyy-MM-dd',
      /** 显示编辑框 */
      showDialog: false,
      /** 科目列表 */
      subjectList: [],
      form: {}
    }
  },
  mounted() {
    this.getList()
  },
  methods: {
    /** 获取列表 */
    async getList() {
      const res = await fetchListByPage(this.params)
      if (res.head.ret === 0) {
        this.list = res.data
        // this.total = res.data.totalCount
      }
    },
    /** 切换每页条数 */
    handleSizeChange(val) {
      this.params.pagesize = val
      this.getList()
    },
    /** 切换当前页 */
    handleCurrentChange(val) {
      this.params.pageno = val
      this.getList()
    },
    /** 编辑 */
    handleEdit(index, row) {
      this.form = row
      this.showDialog = true
    },
    handleClose() {
      this.showDialog = false
    },
    /** 删除单行 */
    async handleDeleteRow(index, row) {
      const params = {
        id: row.id
      }
      this.$confirm('此操作将永久删除该记录, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteRecord(params).then(res => {
          if (res.head.ret === 0) {
            this.$message({
              type: 'success',
              message: '删除成功!'
            })
            this.getList()
          }
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
    },
    myUploadImageMethod(parms) {
      oss.uploadFile(parms.file, (result, error) => {
        if (error) {
          this.$message.error('上传文件失败')
          return -1
        }
        if (result) {
          this.form.borderUrl = result.url
        }
      })
    },
    myUploadImageMethod2(parms) {
      oss.uploadFile(parms.file, (result, error) => {
        if (error) {
          this.$message.error('上传文件失败')
          return -1
        }
        if (result) {
          this.form.nameUrl = result.url
        }
      })
    },
    /** 多选删除 */
    handelMultiDel() {
      this.$confirm('此操作将永久删除选中记录, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const rows = [...this.multipleSelection]
        rows.forEach(item => {
          const params = { id: item.id }
          deleteRecord(params).then(res => {
            if (res.head.ret === 0) {
              this.$message.success('删除成功')
              this.getList()
            }
          })
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
    },
    /** 勾选事件 */
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    /** 清空勾选 */
    handleClearSelection() {
      this.$refs.multipleTable.clearSelection()
    },
    /** 跳转类型 转换 */
    jumpTypeFormat(row, column) {
      const code = parseInt(row.jumpType)
      if (code === 100) {
        return '无跳转'
      } else if (code === 101) {
        return 'H5'
      } else if (code === 102) {
        return '消息中心'
      } else if (code === 103) {
        return '纸条消息'
      } else if (code === 104) {
        return '共享打印'
      }
      return '未知类型'
    },
    /** 栏目类型 转换 */
    columnTypeFormat(row, column) {
      const code = row.column
      if (code === 'home') {
        return '首页'
      } else if (code === 'other') {
        return '其他'
      }
      return '未知类型'
    },
    /** 选择时间段 */
    changeTimeRange(val) {
      this.params.startdate = val[0]
      this.params.enddate = val[1]
    },
    /** 查询 */
    handleQuery() {
      this.getList()
    },
    /** 新增 */
    handleAdd() {
      this.form = {
        borderUrl: '',
        nameUrl: ''
      }
      this.showDialog = true
    },
    /** 关闭弹框 */
    closeDialog(code) {
      this.showDialog = false
      this.banner = {}
      // 保存code
      if (code === '1') {
        this.getList()
      }
    },
    submit() {
      // TODO: 保存
      saveRecord(this.form).then(res => {
        if (res.head.ret === 0) {
          this.$message.success('保存成功')
          this.showDialog = false
          this.getList()
        }
      })
    },
    handleRemove(file) {
      // d
    },
    beforeUpload(file) {
      return true
    },
    handleSuccess(response, file) {
      console.log('应该不会进来吧？')
      return true
    },
    handleRemove2(file) {
      // d
    },
    beforeUpload2(file) {
      return true
    },
    handleSuccess2(response, file) {
      console.log('应该不会进来吧？')
      return true
    }
  }
}
</script>

<style lang="scss" scoped>

</style>
