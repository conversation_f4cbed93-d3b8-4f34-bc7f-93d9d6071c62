<!--
 * @Description: In User Settings Edit
 * @Author: your name
 * @Date: 2019-10-16 11:28:20
 * @LastEditTime: 2019-10-16 11:38:48
 * @LastEditors: Please set LastEditors
 -->
<template>
  <div class="app-container">
    <div class="table-query-container">
      <el-button @click="handleAdd(null)">新增</el-button>
    </div>
    <div class="table-container">
      <el-table
        ref="multipleTable"
        :data="list"
        tooltip-effect="dark"
        style="width: 100%"
        border
        max-height="700px"
      >
        <el-table-column label="排序" prop="sortNum" header-align="center" align="center" />
        <el-table-column label="学段" prop="gradeName" header-align="center" align="center" />
        <el-table-column label="科目名称" prop="subjectName" header-align="center" align="center" />
        <el-table-column label="知识点" prop="knowledge" header-align="center" align="center" />
        <el-table-column label="公式名称" prop="name" header-align="center" align="center" />
        <el-table-column label="公式图片" prop="picUrl" header-align="center" align="center">
          <template slot-scope="scope">
            <div v-if="scope.row.picUrl">
              <img :src="scope.row.picUrl" style="width: 80px; height:80px;">
            </div>
          </template>
        </el-table-column>
        <el-table-column label="状态" prop="showFlag" header-align="center" align="center">
          <template slot-scope="scope">
            <div v-if="scope.row.showFlag === 1">
              启用
            </div>
            <div v-else>
              禁用
            </div>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="245" header-align="center" align="center">
          <template slot-scope="scope">
            <el-button size="mini" type="error" @click="handleEdit(scope.row)">编辑</el-button>
            <el-button size="mini" type="error" @click="handleAdd(scope.row)">新增同类</el-button>
            <el-button size="mini" type="error" @click="deleteSubject(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="pagination-container" style="margin-top:20px;">
      <el-pagination
        :current-page="params.pageno"
        :page-sizes="[10, 20, 30, 40]"
        :page-size="params.pagesize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
    <el-dialog
      :visible.sync="showDialog"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      width="60%"
      title="编辑学段科目"
    >
      <el-form ref="form" :model="form" label-width="100px" class="edit-form">
        <el-form-item label="学段">
          <el-radio-group v-model="form.gradeId">
            <el-radio-button :label="item.id" v-for="item in gradesList" :key="item.id">{{item.name}}</el-radio-button>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="科目名称">
          <el-radio-group v-model="form.subjectId">
            <el-radio-button :label="item.id" v-for="item in subjectsList" :key="item.id">{{item.name}}</el-radio-button>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="知识点" prop="knowledge">
          <el-input v-model="form.knowledge" type="text" />
        </el-form-item>
        <el-form-item label="公式名称" prop="name">
          <el-input v-model="form.name" type="text" />
        </el-form-item>
        <el-form-item label="图标" prop="picUrl">
          <el-upload
            class="avatar-uploader"
            action=""
            :show-file-list="false"
            :on-change="changePic"
            :before-upload="beforeUpload"
            accept="image/png, image/jpeg"
          >
            <img v-if="form.picUrl" :src="form.picUrl" class="avatar">
            <i v-else class="el-icon-plus avatar-uploader-icon" />
          </el-upload>
          <img v-show="false" :src="testUrl" class="avatar">
        </el-form-item>
        <el-form-item label="排序" prop="sortNum">
          <el-input
            v-model="form.sortNum"
            type="text"
          />
        </el-form-item>
        <el-form-item label="状态" prop="showFlag">
          <el-radio-group v-model="form.showFlag">
            <el-radio-button label="0">禁用</el-radio-button>
            <el-radio-button label="1">启用</el-radio-button>
          </el-radio-group>
        </el-form-item>
        <el-form-item>
          <el-button @click="handleClose">返回</el-button>
          <el-button v-permission="'btn-xeasylabel-dict-edit'" type="primary" @click="submit">保存</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>
<script>
import { saveFormulainfo, deleteFormulaInfo, fetchFormulaInfoPage, gradesData, subjectsData } from '@/api/community/formulainfo'
import AliyunOSS from '@/utils/aliyunOSS'
const oss = new AliyunOSS()
oss.clearStorage()
export default {
  data() {
    return {
      // 查询参数
      params: {
        pageno: 1,
        pagesize: 10,
        type: ''
      },
      gradesList: gradesData,
      subjectsList: subjectsData,
      distinctTypeList: [],
      cacheKey: '',
      /** 列表 */
      list: [],
      /** 总条数 */
      total: 0,
      downloadUrl: '',
      /** 时间段 */
      timeRange: '',
      timeRange2: '',
      keyword: '',
      form: {
        name: '',
        type: '',
        label: '',
        value: '',
        localeCode: '',
        icon: '',
        sortNum: 0,
        forbid: 0,
        remark: ''
      },
      /** 时间格式 */
      timeFormat: 'yyyy-MM-dd',
      showDialog: false,
      testUrl: '',
      /** 待上传的文件 */
      upFile: undefined
    }
  },
  mounted() {
    this.getList()
  },
  methods: {
    handleChangeGrade(item) {
      console.log("item=", item)
      this.form.gradeName = item.name
    },
    handleChangeSubject(item) {
      this.form.subjectName = item.name
    },
    async getList() {
      const res = await fetchFormulaInfoPage(this.params)
      if (res.head.ret === 0) {
        this.list = res.data.result
        this.total = res.data.totalCount
      }
    },
    /** 切换每页条数 */
    handleSizeChange(val) {
      this.params.pageSize = val
      this.getList()
    },
    /** 切换当前页 */
    handleCurrentChange(val) {
      this.params.pageno = val
      this.getList()
    },
    /** 查询 */
    handleQuery() {
      this.params.pageno = 1
      this.params.pagesize = 10
      this.getList()
    },
    /** 查看动态 */
    handleEdit(row) {
      this.form = row
      this.showDialog = true
    },
    deleteSubject(row) {
      const parms = {
        id: row.id
      }
      this.$confirm('此操作将永久删除，不可恢复, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteFormulaInfo(parms).then(res => {
          if (res.head.ret === 0) {
            this.$message.success('删除成功')
            this.getList()
          }
        })
      }).catch(() => {
      })
    },
    /** 提交 */
    submit() {
      if (!this.form.gradeId) {
        this.$message.error('请选择年级')
        return
      }
      if (!this.form.subjectId) {
        this.$message.error('请选择科目')
        return
      }
      if (!this.form.picUrl) {
        this.$message.error('请上传图标')
        return
      }
      
      this.gradesList.forEach(item => {
        if (item.id === this.form.gradeId) {
          this.form.gradeName = item.name
        }
      })

      this.subjectsList.forEach(item => {
        if (item.id === this.form.subjectId) {
          this.form.subjectName = item.name
        }
      })

      if (this.upFile) {
        oss.uploadFile(this.upFile, (result, error) => {
          if (error) {
            this.$message.error('上传文件失败')
            return -1
          }
          if (result) {
            console.log('上传完图片后返回的数据', result)
            this.form.picUrl = result.name
            this.handleSave(this.form)
          }
        })
      } else {
        // 直接保存
        this.handleSave(this.form)
      }
    },
    handleSave(parms) {
      saveFormulainfo(parms).then(res => {
        if (res.head.ret === 0) {
          this.$message.success('保存成功')
          this.getList()
        }
        this.showDialog = false
      })
    },
    handleAdd(row) {
      if (row==null || row==undefined) {
        this.form = {
          type: "",
          name: "",
          label: '',
          value: '',
          localeCode: '',
          icon: '',
          sortNum: 0,
          showFlag: 1,
          id: '',
        }
      } else {
        console.log('row=', row)
        this.form = {
          name: '',
          gardeId: row.gradeId,
          subjectId: row.subjectId,
          gradeName: row.gradeName,
          subjectName: row.subjectName,
          knowledge: row.knowledge,
          sortNum: row.sortNum+1,
          showFlag: 1,
          id: '',
        }
      }
      this.showDialog = true
    },
    /** 选择图片*/
    changePic(file) {
      this.upFile = file.raw
      this.form.picUrl = URL.createObjectURL(file.raw)
      this.testUrl = URL.createObjectURL(file.raw)
      return false
    },
    /** 上传前 */
    beforeUpload(file) {
      return false
    },
    /** 返回 */
    handleClose(code) {
      this.showDialog = false
    }
  }
}
</script>

<style lang="scss" scoped>

</style>
