<!--
 * @Description: In User Settings Edit
 * @Author: your name
 * @Date: 2019-10-16 11:28:20
 * @LastEditTime: 2019-10-16 11:46:31
 * @LastEditors: Please set LastEditors
 -->
<template>
  <div class="app-container">
    <div class="list-contatiner">
      <!-- <div class="list-container-change" style="margin-bottom: 10px">
        <el-radio-group v-model="currentMainId" size="small" @change="refreshMaterialTypesDisplay">
          <el-radio-button v-for="item in materialMainType" :key="item.label" :label="item.id">{{ item.label }}</el-radio-button>
        </el-radio-group>
      </div> -->
      <div class="list-container-change">
        <el-radio-group v-model="currentId" size="small" @change="handelMaterialChange">
          <el-radio-button v-for="item in materialTypesDisplay" :key="item.id" :label="item.id">{{ item.zhTitle }}</el-radio-button>
        </el-radio-group>
        <p>
          <el-button v-permission="'btn-menuA4-usletter-edit'" type="success" size="small" @click="handleAdd">新增</el-button>
        </p>
        <!-- <el-button type="primary" size="small" style="margin-left: 20px;" @click="handleAdd">新增</el-button>
        <el-button type="primary" size="small" @click="handleAddType">新增贴纸分类</el-button>
        <el-button type="primary" size="small" @click="handleUpdateType">修改贴纸分类</el-button>
        <el-button type="primary" size="small" @click="handleDeleteType">删除贴纸分类</el-button> -->
      </div>
      <div class="list-container-data">
        <div class="list-container-data-cards">
          <div v-for="(item, index) in list" :key="index" class="list-container-data-cards-item">
            <Card :item="item" @notifyUpdate="notifyUpdate" @notifyEdit="notifyEdit" />
          </div>

        </div>
        <div class="list-container-data-page">
          <el-pagination
            :total="page.total"
            :current-page="page.no"
            :page-size="page.size"
            layout="total, sizes, prev, pager, next, jumper"
            @current-change="handleCurrentChange"
            @size-change="handleSizeChange"
          />
        </div>
      </div>
    </div>
    <div class="dialog-container">
      <el-dialog :append-to-body="true" :title="currentTitile" :visible.sync="showAdd" center :show-close="false" :destroy-on-close="true">
        <FontForm v-if="currentView === 'FontForm'" :id="currentId" :form-data="currentForm" @handleBack="handleBack" />
        <TextPopForm v-if="currentView === 'TextPopForm' && showAdd" :m-id="currentId" :m-name="currentName" :form-data="currentForm" @handleBack="handleBack" />
        <ToDoListForm v-if="(currentView === 'ToDoListForm' || currentView === '')" :m-id="currentId" :m-name="currentName" :form-data="currentForm" @handleBack="handleBack" />
        <CommonForm v-if="currentView === 'CommonForm'" :m-id="currentId" :m-name="currentName" :form-data="currentForm" @handleBack="handleBack" />
      </el-dialog>
      <!-- 素材类型 -->
      <el-dialog
        :title="typeEditForm.id ? '编辑贴纸类型': '新增贴纸类型'"
        :visible.sync="showEditType"
        width="30%"
        center
      >
        <el-input v-model="typeEditForm.enTitle" maxlength="20" placeholder="请输入英文标题,tz_开头如tz_todolist" style="margin-bottom: 10px;" />
        <el-input v-model="typeEditForm.zhTitle" maxlength="8" placeholder="请输入中文标题" />
        <span slot="footer" class="dialog-footer">
          <el-button @click="showEditType = false">取 消</el-button>
          <el-button v-permission="'btn-menuA4-usletter-edit'" type="primary" @click="handleAddOrUpdateType">确 定</el-button>
        </span>
      </el-dialog>
    </div>

  </div>
</template>
<script>
import { getTypes, fetchListByPage, getMaterialDetail, addMaterialType, updateMaterialType, deleteType } from '@/api/community/material'
import Card from './component/Card/index'
import CommonForm from './component/CommonForm/index'
import FontForm from './component/FontForm/index'
import TextPopForm from './component/TextPopForm/index'
import ToDoListForm from './component/ToDoListForm/index'
/** 选项卡与 组件对应关系 */
// FontForm -- tz_font
// ToDoListForm -- tz_todolist tz_postitnotes tz_frame
// TextPopForm -- tz_textbubble tz_curriculum tz_businesscard tz_idcard
// CommonForm -- tz_emoji tz_decoration tz_food tz_gesture
// const tabComponentMap = {
//   'tz_front': 'FontForm',
//   'tz_todolist': 'ToDoListForm', 'tz_postitnotes': 'ToDoListForm', 'tz_frame': 'ToDoListForm',
//   'tz_textbubble': 'TextPopForm', 'tz_curriculum': 'TextPopForm', 'tz_businesscard': 'TextPopForm', 'tz_idcard': 'TextPopForm',
//   'tz_emoji': 'CommonForm', 'tz_decoration': 'CommonForm', 'tz_food': 'CommonForm', 'tz_gesture': 'CommonForm'
// }
export default {
  components: {
    Card,
    CommonForm,
    FontForm,
    TextPopForm,
    ToDoListForm
  },
  data() {
    return {
      materialMainType: [
        { id: -1, label: '全部' }, { id: 0, label: '编辑纸条' }, { id: 2, label: '打印模板' }, { id: 5, label: 'A4打印' }],
      currentMainId: 6,
      materialTypesDisplay: [],
      /** 素材类型 */
      materialTypes: [],
      /** 当前素材类型Id */
      currentId: '',
      /** 当前素材类型名 */
      currentName: '',
      /** 标题名 */
      currentTitile: '',
      /** 分页查询 */
      page: {
        size: 20,
        no: 1,
        total: 0
      },
      /** 当前素材列表 */
      list: [],
      /** 编辑添加模式 */
      showAdd: false,
      /** 当前添加组件 */
      currentView: 'FontForm',
      /** 当前卡片内容 */
      currentForm: {},
      /** 显示编辑框 */
      showEditType: false,
      /** 类型编辑 */
      typeEditForm: {
        com: 'CommonForm',
        enTitle: '',
        name: '',
        id: '',
        zhTitle: ''
      },
      /** 选项卡与组件映射 */
      tabComponentMap: undefined
    }
  },
  mounted() {
    this.getMaterialTypes()
    // this.refreshMaterialTypesDisplay()
  },
  methods: {
    refreshMaterialTypesDisplay() {
      const tempArr = []
      console.log(this.materialTypes)
      if (this.currentMainId !== -1 && this.materialTypes) {
        const tempType = this.currentMainId
        this.materialTypes.forEach(item => {
          if (item.type === tempType) {
            tempArr.push(item)
          }
        })
        this.settingCurrentId(tempArr[0].id)

        this.materialTypesDisplay = tempArr
      } else {
        this.materialTypesDisplay = this.materialTypes
      }
      console.log('console.log(this.materialTypes)', this.materialTypesDisplay)
    },
    async settingCurrentId(id) {
      this.currentId = id
      this.getListById()
    },
    /** 获取素材类型 */
    async getMaterialTypes() {
      const res = await getTypes()
      if (res.head.ret === 0) {
        this.materialTypes = res.data
        this.materialTypes.forEach(m => {
          m.disabled = false
        })
        console.log('this.materialTypes', this.materialTypes)
        this.currentId = this.materialTypes[0].id
        this.materialTypesDisplay = this.materialTypes
        this.updateTabComponentMap()
        this.refreshMaterialTypesDisplay()
        // this.getListById()
      }
    },
    /** 更新组件映射 */
    updateTabComponentMap() {
      if (this.materialTypes && this.materialTypes.length > 0) {
        this.tabComponentMap = new Map()
        this.materialTypes.forEach(m => {
          this.tabComponentMap.set(m.name, m.com)
        })
      }
    },
    /** 获取素材列表 by Id */
    async getListById(id) {
      const res = await fetchListByPage({
        id: this.currentId,
        pageno: this.page.no,
        pagesize: this.page.size
      })
      if (res.head.ret === 0) {
        this.list = res.data.result
        this.page.total = res.data.totalCount || 0
      } else {
        this.$message.error('获取素材列表数据失败')
      }
    },
    /** 获取素材详情 */
    async getDetail(id) {
      const res = await getMaterialDetail({
        id: id
      })
      if (res.head.ret === 0) {
        this.currentForm = res.data
        this.currentForm.id = id
        this.materialTypes.forEach(m => {
          if (m.id === this.currentId) {
            this.currentName = m.name
            this.currentTitile = m.zhTitle
          }
        })
        const com = this.tabComponentMap.get(this.currentName)
        this.currentTitile = this.currentTitile + '编辑'
        this.currentView = com
        this.showAdd = true
      }
    },
    /** 每页条数 */
    handleSizeChange(val) {
      this.page.size = val
      this.getListById()
    },
    /** 当前页 */
    handleCurrentChange(val) {
      this.page.no = val
      this.getListById()
    },
    /** 切换素材类型 */
    handelMaterialChange(tab) {
      console.log('handelMaterialChange: ', this.currentId)
      this.page = {
        size: 20,
        no: 1,
        total: 0
      }
      this.materialTypes.forEach(m => {
        if (m.id === this.currentId) {
          this.currentName = m.name
          this.currentTitile = m.zhTitle
        }
      })
      this.getListById()
    },
    /** 删除后更新 */
    notifyUpdate() {
      this.getListById()
    },
    /** 点击添加 */
    handleAdd() {
      this.materialTypes.forEach(m => {
        if (m.id === this.currentId) {
          this.currentName = m.name
          this.currentTitile = m.zhTitle
        }
      })
      this.currentForm = {
        mId: this.currentId
      }
      const com = this.tabComponentMap.get(this.currentName)
      this.currentTitile = this.currentTitile + '新增'
      this.currentView = com
      this.showAdd = true
      console.log(this.currentView)
    },
    /** 增加分类 */
    handleAddType() {
      if (this.currentMainId === -1) {
        alert('请选择一个分类')
        return
      }
      this.typeEditForm = {}
      this.materialTypes.forEach(m => {
        if (m.id === this.currentId) {
          this.typeEditForm = { ...m }
        }
      })

      this.typeEditForm.enTitle = ''
      this.typeEditForm.name = ''
      this.typeEditForm.zhTitle = ''
      this.typeEditForm.id = null

      // this.typeEditForm = {
      //   com: 'CommonForm',
      //   enTitle: '',
      //   name: '',
      //   zhTitle: '',
      //   // 固定分类和子分类
      //   type: 1,
      //   subType: 3
      // }
      this.showEditType = true
    },
    /** 删除分类 */
    handleDeleteType() {
      this.$confirm('此操作将永久素材分类及所属素材，不可恢复, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        console.log('delete: ', this.tabComponentMap.get(this.currentName))
        console.log('delete: ', this.tabComponentMap)
        console.log('delete: ', this.currentName)
        if (this.tabComponentMap.get(this.currentName) !== 'CommonForm') {
          this.$message.warning('该分类非贴纸分类，不允许删除')
          return
        }

        if (this.list.length) {
          this.$message.warning('该分类下存在素材记录，请先删除记录')
          return
        }
        this.doDeleteType()
      }).catch(() => {
      })
    },
    async doDeleteType() {
      const res = await deleteType({ id: this.currentId })
      if (res.head.ret === 0) {
        this.$message({
          type: 'success',
          message: '删除成功!'
        })
        this.getMaterialTypes()
      } else {
        this.$message({
          type: 'error',
          message: '删除失败!'
        })
      }
    },
    /** 修改分类 */
    handleUpdateType() {
      this.typeEditForm = {}
      this.materialTypes.forEach(m => {
        if (m.id === this.currentId) {
          this.typeEditForm = { ...m }
        }
      })
      this.showEditType = true
    },
    /** 新增或修改分类 */
    async handleAddOrUpdateType() {
      if (!this.typeEditForm.enTitle && !this.typeEditForm.zhTitle) {
        this.$message.error('英文标题或中文标题不能为空')
        return
      }
      let res
      if (this.typeEditForm.id) {
        const data = {
          id: this.typeEditForm.id,
          com: this.typeEditForm.com,
          enTitle: this.typeEditForm.enTitle,
          zhTitle: this.typeEditForm.zhTitle
        }
        res = await updateMaterialType(data)
      } else {
        this.typeEditForm.name = this.typeEditForm.enTitle
        res = await addMaterialType(this.typeEditForm)
      }
      if (res.head.ret === 0) {
        // 刷新整个页面
        this.getMaterialTypes()
      } else {
        this.$message.error('新增或更新素材类型失败')
      }
      this.showEditType = false
    },
    /** 切换选项卡 */
    handleClickTab(tab, event) {
      const com = this.tabComponentMap.get(this.currentName)
      this.currentView = com
    },
    /** 点击返回 */
    handleBack(val) {
      this.currentForm = {}
      this.showAdd = false
      if (val === '1') {
        this.getListById()
      }
    },
    /** 子组件通知进入编辑 */
    async notifyEdit(item) {
      await this.getDetail(item.id)
      // this.materialTypes.forEach(m => {
      //   if (m.id === this.currentId) {
      //     this.currentName = m.name
      //   }
      // })
      // const com = tabComponentMap[this.currentName]
      // this.currentTitile = this.currentName + '编辑'
      // this.currentView = com
    }

  }
}
</script>

<style lang="scss" scoped>
.app-container{
  .list-contatiner{
    .list-container-data{

      &-cards{
        margin-top: 20px;
        margin-bottom: 20px;
        display: flex;
        flex-flow: row wrap;
        justify-content: flex-start;
        &-item{
          width: 15%;
          margin-left: 1%;
          border: 1px solid grey;
          margin-bottom: 10px;
        }
      }
    }
  }
}

</style>
