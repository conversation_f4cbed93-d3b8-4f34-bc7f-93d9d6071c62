<template>
  <div @click="showZY=false; showIndex=0;">
    <transition-group name="dis">
      <div class="header-title" v-show="showFlag.prod" :key="'showFlagProdTitle'">
        <div class="header-title-text">品牌介绍</div>
      </div>
      <div class="prod-content" v-show="showFlag.prod" :key="'showFlagProdContent'">
        <div class="prod-content-desc">
          <img src="https://m.yoyin.net/h5/img/mobile/about/info.png">
          <p>柚印，是一家专注于研发制造个人用户使用的生活办公化一体的热敏打印机新势力品牌！致力于为每一位用户，因打印而让生活、办公、学习等增添便捷与更多可能性；
            柚印坐落于粤港澳大湾区中的经济特别行政区——珠海。柚印的母公司为珠海芯烨科技有限公司，为全球热敏打印行业名列前茅的科技企业。至2020年热敏打印机总年销售量位居全球第二！母公司拥有非常丰厚的行业科技实力与创新实力，拥有近百项国家专利。而柚印作为芯烨科技旗下的全新子品牌，更是全心打造一个走出国门的全民消费级打印品牌！让世界懂得中国制造，让世界留下中国印记之力；
            柚印，让更多人体验到更有温度、更懂你的打印品牌。</p>
        </div>
      </div>

      <div class="header-title" v-show="showFlag.connect" :key="'showFlagConnectTitle'">
        <div class="header-title-text">联系我们</div>
      </div>
      <div class="prod-content" v-show="showFlag.connect" :key="'showFlagConnectContent'">
        <div class="prod-content-connect1">
          <img src="https://m.yoyin.net/h5/img/mobile/about/service-connect.png">
          <div class="prod-content-connect1-right">
            <div>电话热线：0756-2897617</div>
            <div>工作时间：工作日9:00-17:00</div>
          </div>
        </div>

        <div class="prod-content-connect2">
          <img src="https://m.yoyin.net/h5/img/mobile/about/service-telephone.png">
          <div class="prod-content-connect2-right">
            <div>商务合作：张经理</div>
            <div>手机：18675634674</div>
            <div>邮箱：<EMAIL></div>
          </div>
        </div>

        <div class="prod-content-connect2">
          <img src="https://m.yoyin.net/h5/img/mobile/about/service-telephone.png">
          <div class="prod-content-connect2-right">
            <div>商务合作：陈经理</div>
            <div>手机：18570144614</div>
            <div>邮箱：<EMAIL></div>
          </div>
        </div>
      </div>

      <div class="header-title" v-show="showFlag.attr" :key="'showFlagAttrTitle'">
        <div class="header-title-text">关注我们</div>
      </div>
      <div class="prod-content" v-show="showFlag.attr" :key="'showFlagAttrContent'">
<!--        <div class="prod-content-attr" @click.stop="showZY=true; showIndex=1;" ref="attrXhs">-->
<!--          <img src="../../../assets/about/xhs.png">-->
<!--          <div>小红书</div>-->
<!--        </div>-->
<!--        <div class="prod-content-attr" @click.stop="showZY=true; showIndex=2;">-->
<!--          <img src="../../../assets/about/dy.png">-->
<!--          <div>抖音商城</div>-->
<!--        </div>-->
        <div class="prod-content-attr" @click.stop="showZY=true; showIndex=1;showAttr('qrcodediv3')" ref="qrcodediv3">
          <img src="https://m.yoyin.net/h5/img/mobile/about/wx.png">
          <div>微信公众号</div>
        </div>
      </div>
<!--      <div class="zhezh" v-show="showZY" :key="'showZY111'" @click="showZY=!showZY; showIndex=0">-->
<!--      </div>-->
    </transition-group>
    <transition-group  name="up">
      <div v-show="showIndex===1" :key="'showZY1'" class="qrcodediv" :style="qrcodedivstyle" ref="qrcodedivContentRef">
        <div class="qrcodediv-content">
          <img src="https://m.yoyin.net/h5/img/mobile/about/qrcord-wx.png" >
          <div>长按可识别或保存</div>
        </div>
        <img src="https://m.yoyin.net/h5/img/mobile/about/qrcord-arrow-down.png">
      </div>
      <div v-show="showIndex===2" :key="'showZY2'" class="qrcodediv index2" >
        <div class="qrcodediv-content index2">
          <img src="https://m.yoyin.net/h5/img/mobile/about/qrcord-xhs.png" >
          <div>长按可识别或保存</div>
        </div>
        <img src="https://m.yoyin.net/h5/img/mobile/about/qrcord-arrow-down.png">
      </div>
      <div v-show="showIndex===3" :key="'showZY3'" class="qrcodediv index3">
        <div class="qrcodediv-content index3" :key="'showZY3'">
          <img src="https://m.yoyin.net/h5/img/mobile/about/qrcord-dy.png" >
          <div>长按可识别或保存</div>
        </div>
        <img src="https://m.yoyin.net/h5/img/mobile/about/qrcord-arrow-down.png">
      </div>
    </transition-group>
    <div v-show="showFlag.foot" :key="'showFlagFoot'">
      <foot-info></foot-info>
    </div>
  </div>
</template>

<script>
import FootInfo from './FootInfo'
export default {
  name: 'AboutUs',
  components: {FootInfo},
  data () {
    return {
      showZY: false,
      showIndex: 0,
      showFlag: {
        foot: false,
        prod: false,
        connect: false,
        attr: false
      },
      attrWinOffsetTop: 0,
      qrcodedivstyle: {'top': '0'}
    }
  },
  mounted () {
    const that = this
    setTimeout(function () {
      that.showFlag.prod = true
    }, 300)
    setTimeout(function () {
      that.showFlag.connect = true
    }, 500)
    setTimeout(function () {
      that.showFlag.attr = true
    }, 700)
    setTimeout(function () {
      that.showFlag.foot = true
    }, 900)
  },
  methods: {
    showAttr (refId) {
      console.log(window.getComputedStyle(this.$refs.qrcodedivContentRef).height)
      console.log(this.$refs.qrcodedivContentRef.offsetHeight)

      var height = window.getComputedStyle(this.$refs.qrcodedivContentRef).height.replace('px', '')
      this.qrcodedivstyle.top = (this.$refs[refId].offsetTop - height - 30) + 'px'
    }
  }
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
html, body {
  overflow-x: hidden;
  touch-action:none;
  touch-action:pan-y;
}
.header-title {
  min-height: 4.3rem;
  background-image: url("https://m.yoyin.net/h5/img/mobile/home/<USER>");
  background-position: left center;
  background-repeat: no-repeat;
  background-size:4.3rem 100%;
  position: relative;
  margin-bottom: 1.2rem;
  &-text {
    float: left;
    padding-left: 2.2rem;
    padding-top: 1.6rem;
    font-size: 1.7rem;
  }
}

.prod-content {
  display: flex;
  flex-wrap: wrap;
  width: 30rem;
  padding-bottom: 4.3rem;
  text-align: left;

  &-desc {
    width: 27.8rem;
    background: #FFFFFF;
    box-shadow: 0px 10px 13px rgba(0, 0, 0, 0.03);
    opacity: 1;
    border-radius: 10px;
    img{
      width: 27.8rem;
      height: 11.9rem;
    }
    p {
      padding: 0 2rem;
      font-size: 1.6rem;
      line-height: 3.2rem;
      font-weight: 400;
    }
  }
  &-connect1 {
    width: 27.8rem;
    height: 9.3rem;
    background: #FFFFFF;
    box-shadow: 0px 10px 13px rgba(0, 0, 0, 0.03);
    opacity: 1;
    border-radius: 10px;
    position: relative;
    img {
      width: 3.9rem;
      height: 3.9rem;
      padding: 2.5rem 1.4rem;
      position: absolute;
    }
    &-right {
      position: absolute;
      padding-left: 6.6rem;
      padding-top: 1.8rem;
      height: 100%;
      vertical-align:middle;
      line-height: 2.7rem;
      font-size: 1.5rem;
    }
  }

  &-connect2 {
    width: 27.8rem;
    height: 11.6rem;
    background: #FFFFFF;
    box-shadow: 0px 10px 13px rgba(0, 0, 0, 0.03);
    opacity: 1;
    border-radius: 10px;
    position: relative;
    margin-top: 1.1rem;
    img {
      padding: 3.9rem 1.4rem;
      width: 3.9rem;
      height: 3.9rem;
      position: absolute;
      vertical-align:middle;
    }
    &-right {
      right: 0;
      height: 100%;
      vertical-align:middle;
      padding-left: 6.6rem;
      padding-top: 2.3rem;
      line-height: 2.7rem;
      font-size: 1.5rem;
    }
  }

  &-attr {
    width: 8.5rem;
    height: 9.7rem;
    background: #FFFFFF;
    box-shadow: 0px 10px 13px rgba(0, 0, 0, 0.03);
    opacity: 1;
    border-radius: 10px;
    margin-right: 1.1rem;
    text-align: center;
    img {
      padding-top: 1.8rem;
      width: 3.3rem;
      height: 3.3rem;
    }
    line-height: 2.8rem;
    font-size: 1.3rem;
  }
}

.zhezh{
  position: absolute;
  top: 0%;
   left: 0%;
   right: 0;
   bottom: 0%;
  width: 100%;
  height: 100%;
  background-color: black;
  z-index: 999;
  /*-moz-opacity: 0.1;*/
  opacity: 0;
}

.qrcodediv {
  top: 78rem;
  height: 16rem;
  position: absolute;
  opacity: 1;
  border-radius: 10px;
  &-content {
    padding: 2.1rem 0;
    position: relative;
    width: 13.3rem;
    height: 11.9rem;
    box-shadow: 7px 10px 33px rgba(0, 0, 0, 0.2);
    background-color: #FFFFFF;
    border: 0px solid #707070;
    text-align: center;
    border-radius: 10px;
    img {
      position: relative!important;
      left: 0rem!important;
      //padding-top: 2.1rem!important;
      width: 9.8rem!important;
      height: 9.7rem!important;
    }
  }
  img {
    position: absolute;
    left: 2rem;
    width: 3.2rem;
    height: 1.2rem;
    z-index: 99999;
  }
  font-size: 1.3rem;
  &.index1 {
  }
  &.index2 {
    left: 15rem;
    img {
      left: 5rem;
    }
  }
  &.index3 {
    left: 22.2rem;
    img {
      left: 7rem;
    }
  }
}

.dis-enter{
  opacity:0;
}
.dis-enter-active{
  transition:opacity 1.5s;
}
.dis-leave-active{
  transition:transform 0s;
}
.dis-leave-to{
  transform: translateX(0);
}

.up-enter{
  opacity:0;
  //transform: translateY(10rem);
}
.up-enter-active{
  animation: pulse 1s linear infinite alternate;
}
.up-enter-to {
  opacity:1;
}
.up-leave-active{
  transition:transform 0s;
}
.up-leave-to{
  opacity:0;
}

//.navshow-enter{
//  opacity:0;
//}
//.navshow-enter-active{
//  animation: pulse 1s linear infinite alternate;
//}
//.navshow-leave-active{
//  transition:transform 0s;
//}
//.navshow-leave-to{
//  /*transform: translateX(0);*/
//}

@keyframes pulse {
  0%, 50%, 100% {
    -webkit-transition-timing-function: cubic-bezier(0.3, 0.6, 0.6, 1.000);
    transition-timing-function: cubic-bezier(0.2135, 0.6, 0.6, 1.000);
  }

  0% {
    opacity: 1;
    transform-origin:left 100%;
    -webkit-transform: scale3d(0.1, 0.1, 1);
    transform: scale3d(0.1, 0.1, 1);
  }

  50% {
    -webkit-transform: scale3d(1, 1, 1);
    transform: scale3d(1, 1, 1);
  }

  100% {
    opacity: 1;
    -webkit-transform: scale3d(1, 1, 1);
    transform: scale3d(1, 1, 1);
  }
}

@keyframes heart{
  //from{transform:translate(0,15rem)}
  //to{transform:translate(0,0px)}
  from {width: 3.2rem;height: 0px;opacity: 1;}
  to {width: 3.2rem;height: 1.2rem;opacity: 1;}
}
</style>
