<template>
  <div class="faq">
    <div class="faq-title2">
      添加设备时，未发现可连接的设备？
    </div>
    <div class="faq-content2">
      <p class="first-line">1、请确认设备已经开机；</p>
      <p class="first-line">2、确认手机蓝牙已经开启；</p>
      <p class="first-line">3、排除他人已连接设备的可能性；</p>
      <p class="first-line">4、尝试重启APP、打印机设备；</p>
      <p class="first-line">5、如果还是无法连接，请联系客服。</p>
    </div>

  </div>
</template>

<script>
export default {
  data () {
    return {
      moduleKey: 'faq'
    }
  },
  async mounted () { },
  methods: {},
  components: {}
}
</script>

<style lang="scss">
@import '../../assets/scss/faq.scss'; // 全局faq自定义的css样式
</style>
