<template>
  <div>
      <div class="one">
          <div class="one-content">
              <div class="one-content-title">
                  <span class="one-content-title-bold">柚印 —— 错题打印机</span>走到哪儿都能印
              </div>
              <div class="one-content-row">
                  <div>柚印是一款小初高学霸们必备的学习神器，有了它，能大大提高孩子的学习效率。</div>
                  <div>整理错题、打印资料非常便利；随时随地想打就打。</div>
              </div>
              <div class="one-content-li">
                  <ul>
                      <li>无墨环保，蓝牙连接</li>
                      <li>体型较小，不占地方</li>
                      <li>使用便捷，功能齐全</li>
                  </ul>
              </div>
          </div>
      </div>
      <div class="two">
          <div class="two-title"><span>功能简介</span></div>
          <div class="two-li">
            <ul>
                      <li>拍题搜题</li><br><br>
                      <li>精选题库</li><br><br>
                      <li>电子错题本</li>
                  </ul>
          </div>
      </div>
      <div class="three">
          <div class="three-content">
            <div class="three-title"><span>功能简介</span></div>
            <div class="three-li">
                <ul>
                        <li>图片打印</li><br><br>
                        <li>模板打印</li><br><br>
                        <li>表格打印</li>
                    </ul>
            </div>
          </div>
      </div>
  </div>
</template>

<script>
export default {
  name: 'Home2'
}
</script>

<style scoped>
span {
    color: #5EBF81;
    font-size: 24px;
    font-weight: bold;
    padding-right: 15px;
}
ul{
    padding-inline-start: 20px;
    font-weight: bold;
}
.one {
    background-image: url(../../assets/home1.png);
    background-repeat: no-repeat;
    background-size:100% auto;
    height: 582px;
}
.one-content {
    width: 500px;
    float: right;
    position: relative;
    font-weight: bold;
    text-align: left;
}
.one-content-title {
    padding-top: 200px;
}
.one-content-row {
    padding-top: 80px;
}
.one-content-li {
    padding-top: 40px;
}
.two {
    background-image: url(../../assets/home2.png);
    background-repeat: no-repeat;
    background-size:100% auto;
    height: 838.3px;
    text-align: left;
}
.two-title {
    padding: 236px 0 70px 0;
}
.three {
    background-image: url(../../assets/home3.png);
    background-repeat: no-repeat;
    background-size:100% auto;
    height: 838.3px;
    text-align: right;
}
.three-content {
    width: 120px;
    float: right;
    position: relative;
    font-weight: bold;
    text-align: left;
}
.three-title {
    padding: 236px 0 70px 0;
}
</style>
