<template>
  <div>
    <paper-body-cell
      v-for="(item, index) in paperStructure"
      :key="index"
      :ques="item"
      v-on="$listeners"
    >
      <template #question="slotProps">
        <slot name="question" :ques="slotProps.ques"></slot>
      </template>

      <template #segment="slotProps">
        <slot name="segment" :segment="slotProps.segment"></slot>
      </template>
    </paper-body-cell>
  </div>
</template>

<script>
  import paperBodyCell from './paper-body-cell.vue'
  export default {
    components: { paperBodyCell },
    props: {
      paperStructure: {
        type: Array,
        default: () => {
          return []
        }
      }
    }
  }
</script>

<style lang="scss" scoped></style>
