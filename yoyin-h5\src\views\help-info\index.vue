<template>
  <div class="content">
    <div class="info">图片排版不紧凑，打印出来太多空白啦！这样太浪费纸了，好心疼耗材~如果你也有这种情况，那就来试试【自定义拼图】吧，帮你节约耗材，打印出最优效果哦！</div>
    <div class="image"><img src="https://m.yoyin.net/h5/img/zidingyipingtu2/01.png"></div>
    <div class="info">1、首先选中需要打印的图片(2-9张)，点击“确定”进入编辑页面</div>
    <div class="image"><img src="https://m.yoyin.net/h5/img/zidingyipingtu2/02.png"></div>
    <div class="image"><img src="https://m.yoyin.net/h5/img/zidingyipingtu2/03.png"></div>
    <div class="info">2、图片可进行编辑操作，允许调整图片滤镜效果以及去除不需要被打印的部分。</div>
    <div class="info">①选中图片，点击【滤镜】图标进入【滤镜调整页】后，可根据实际需求切换图像/文字滤镜/裁剪。</div>
    <div class="image"><img src="https://m.yoyin.net/h5/img/zidingyipingtu2/04.png"></div>
    <div class="info">②通过点击【滤镜调整页】中的【裁剪】图标进入【裁剪编辑页】后，可对图片进行自由裁剪（尽可能少留空白）</div>
    <div class="image"><img src="https://m.yoyin.net/h5/img/zidingyipingtu2/05.png"></div>
    <div class="info">3、将裁剪后的图片缩放到合适尺寸然后按照需要的排版方式排列在一起。</div>
    <div class="info">接下来点击【完成】就可以在预览页见证效果啦~</div>
    <div class="image"><img src="https://m.yoyin.net/h5/img/zidingyipingtu2/06.png"></div>
  </div>
</template>

<script>
export default {
  data () {
    return {
    }
  },
  components: {}
}
</script>

<style lang="scss">
body {
  font-size: 16px;
  color: #333333;
}
.content {
  margin: 1rem;
  padding-bottom: 3.125rem;
}
.image {
  padding: 1rem 0;
}
img {
  width: 100%;
  object-fit: contain;
}
</style>
