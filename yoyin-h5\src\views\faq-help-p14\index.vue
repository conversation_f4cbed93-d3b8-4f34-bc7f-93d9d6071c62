<template>
  <div class="faq">
    <div class="faq-title2">
      打印机如何开关机？
    </div>
    <div class="faq-content2">
      <p class="first-line">1、开机：长按电源键2秒，指示灯常亮则表示打印机已经开启。</p>
      <p class="first-line">2、手动关机：长按电源键2秒，指示灯熄灭，后则表示已经关机</p>
      <p class="first-line">3、自动关机：连接设备后—点击设备详情—自动关机时间设置，根据需要设置自动关机的时间。</p>
      <p class="first-line"><img src="../../assets/img/p14-1.png"></p>
    </div>

  </div>
</template>

<script>
export default {
  data () {
    return {
      moduleKey: 'faq'
    }
  },
  async mounted () { },
  methods: {},
  components: {}
}
</script>

<style lang="scss">
@import '../../assets/scss/faq.scss'; // 全局faq自定义的css样式
</style>
