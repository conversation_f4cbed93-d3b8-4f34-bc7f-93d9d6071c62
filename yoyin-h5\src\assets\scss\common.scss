* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    -webkit-tap-highlight-color: transparent;
}

html {
    font-family: "PingFangSC-Regular", "Microsoft YaHei", "微软雅黑", "Helvetica Neue", "Helvetica, STHeiTi", "Arial", "sans-serif";
    font-size: 14px;
    height: 100%;
    width: 100%;
    -ms-text-size-adjust: 100%;
    -webkit-text-size-adjust: 100%;
}

body {
    margin: 0 auto;
    height: 100%;
    line-height: 1.5;
    color: #666;
    overflow-x: hidden;
    -webkit-overflow-scrolling: touch;
}

article,
aside,
details,
figcaption,
figure,
footer,
header,
hgroup,
main,
nav,
section,
summary {
    display: block;
}

audio,
canvas,
progress,
video {
    display: inline-block;
    vertical-align: baseline;
}

audio:not([controls]) {
    display: none;
    height: 0;
}

[hidden],
template {
    display: none;
}

svg:not(:root) {
    overflow: hidden;
}

a {
    background: transparent;
    text-decoration: none;
    color: #666;
}

a:active {
    outline: 0;
}

a:active {
    color: #006699;
}

abbr[title] {
    border-bottom: 1px dotted;
}

b,
strong {
    font-weight: bold;
}

dfn {
    font-style: italic;
}

mark {
    background: #ff0;
    color: #000;
}

small {
    font-size: 80%;
}

sub,
sup {
    font-size: 75%;
    line-height: 0;
    position: relative;
    vertical-align: baseline;
}

sup {
    top: -0.5em;
}

sub {
    bottom: -0.25em;
}

img {
    border: 0;
    vertical-align: middle;
}

hr {
    -moz-box-sizing: content-box;
    box-sizing: content-box;
    height: 0;
}

pre {
    overflow: auto;
    white-space: pre;
    white-space: pre-wrap;
    word-wrap: break-word;
}

code,
kbd,
pre,
samp {
    font-family: monospace, monospace;
    font-size: 1em;
}

button,
input,
optgroup,
select,
textarea {
    color: inherit;
    font: inherit;
    margin: 0;
}

button {
    overflow: visible;
}

button,
select {
    text-transform: none;
}

button,
html input[type="button"],
input[type="reset"],
input[type="submit"] {
    -webkit-appearance: button;
    cursor: pointer;
}

button[disabled],
html input[disabled] {
    cursor: default;
}

button::-moz-focus-inner,
input::-moz-focus-inner {
    border: 0;
    padding: 0;
}

input {
    line-height: normal;
}

input[type="checkbox"],
input[type="radio"] {
    box-sizing: border-box;
    padding: 0;
}

input[type="number"]::-webkit-inner-spin-button,
input[type="number"]::-webkit-outer-spin-button {
    height: auto;
}

input[type="search"] {
    -webkit-appearance: textfield;
    -moz-box-sizing: border-box;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
}

input[type="search"]::-webkit-search-cancel-button,
input[type="search"]::-webkit-search-decoration {
    -webkit-appearance: none;
}

fieldset {
    border: 1px solid #c0c0c0;
    margin: 0 2px;
    padding: 0.35em 0.625em 0.75em;
}

legend {
    border: 0;
    padding: 0;
}

textarea {
    overflow: auto;
    resize: vertical;
}

optgroup {
    font-weight: bold;
}

table {
    border-collapse: collapse;
    border-spacing: 0;
}

td,
th {
    padding: 0;
}

p {
    margin: 0;
}

h1,
h2,
h3,
h4,
h5,
h6,
p,
figure,
form,
blockquote {
    margin: 0;
    font-size: 1em;
    font-weight: normal;
}

ul,
ol,
li,
dl,
dd {
    margin: 0;
    padding: 0;
}

ul,
ol {
    list-style: none outside none;
}

h1,
h2,
h3 {
    line-height: 2;
}

h1 {
    font-size: 1.8rem;
}

h2 {
    font-size: 1.6rem;
}

h3 {
    font-size: 1.4rem;
}

input::-moz-placeholder,
textarea::-moz-placeholder {
    color: #cccccc;
}

input:-ms-input-placeholder,
textarea:-ms-input-placeholder {
    color: #cccccc;
}

input::-webkit-input-placeholder,
textarea::-webkit-input-placeholder {
    color: #cccccc;
}

:focus {
    outline: none;
}

.fl {
    float: left;
    display: inline;
}

.fr {
    float: right;
    display: inline;
}

.clearfix {
    clear: both;
}

.clearfix:after {
    clear: both;
    display: block;
    content: ' ';
    height: 0;
    width: 100%;
}

.ellipsis {
    width: 100%;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
    word-wrap: break-word;
    word-break: break-all;
}

@font-face {
    font-family: peb;
    src: url("../font/peb.ttf")
}

a,
img,
div {
    -webkit-tap-highlight-color: transparent;
}

:focus {
    -webkit-tap-highlight-color: transparent;
}

// 响应式内容
.screen-pc {
    display: none !important;
}

.screen-phone {
    display: block !important;
}

@media (min-width: 750px) {
    body {
        font-size: 14px;
    }
    .layout-center {
        margin: 0 auto;
        max-width: 1200px;
    }
    .screen-phone {
        display: none !important;
    }
    .screen-pc {
        display: block !important;
    }
}

@media (max-width: 750px) {
    body {
        font-size: 16px;
    }
}

@media (max-width: 400px) {
    body {
        font-size: 14px;
    }
}

@media (max-width: 320px) {
    body {
        font-size: 12px;
    }
}
