// 变量定义
$faq-bg-color: #F5F5F5;

/*公共样式*/ 
/*页面背景*/
body{
    // background: $faq-bg-color;
}

.faq{
    background: #fff;
    // margin: 10px;
    padding: 16px 16px 0 6px;
    // border-radius: 8px;
    &-title{
        font-size: 18px;
        font-weight: bold;
        border-bottom: 1px solid #D5D4D9;
        color: #000;
        background-image: url(~@/assets/img/box.png);
        background-position-y: center;
        background-repeat: no-repeat;
        span{
            // border-left: 4px solid #0068FF;
            padding: 0;
            height: 50px;
            line-height: 50px;
            padding-left: 10px;
        }
    }
    &-content{
        margin: 20px 0 30px ;
        p{
            text-indent: 2em;
        }
    }

    &-title2{
        font-size: 18px;
        font-weight: bold;
        // border-bottom: 1px solid #D5D4D9;
        color: #000;
        background-image: url(~@/assets/img/ic_question.png);
        background-position-y: center;
        background-repeat: no-repeat;
        background-size: 20px 20px;
        height: 50px;
        // line-height: 50px;
        padding-top: 12px;
        padding-left: 30px;
        margin: 0 10px;
    }
    &-content2{
        padding-left: 30px;
        margin: 20px 10px;
        font-size: 14px;
        background-image: url(~@/assets/img/ic_answer.png);
        background-position-y: top;
        background-repeat: no-repeat;
        background-size: 20px 20px;
        line-height: 24px;
        p {
            img {
                width: 100%;
                padding: 15px 0;
            }
            &.remark {
                color: #999999;
                font-size: 12px;
            }
        }
    }
}