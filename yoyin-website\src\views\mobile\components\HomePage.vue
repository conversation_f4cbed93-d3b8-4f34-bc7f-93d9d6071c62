<!--<link rel="stylesheet" href="../../../../node_modules/swiper/swiper-bundle.css">-->
<template>
  <div>
    <!-- 轮播 -->
    <transition-group name="dis">
      <!-- 为了加阴影 -->
      <div class="swiper-container-class" v-show="showFlag.banner" :key="'showFlagBanner'">
        <div class="swiper-container">
          <div class="swiper-wrapper">
            <div class="swiper-slide">
              <img :src="banner1" @click="navBanner(1)" />
            </div>
            <div class="swiper-slide">
              <img :src="banner2" @click="navBanner(2)" />
            </div>
            <div class="swiper-slide">
              <img :src="banner3" @click="navBanner(3)" />
            </div>
          </div>
          <div class="swiper-pagination"></div>
          <!--分页器。如果放置在swiper-container外面，需要自定义样式。-->
        </div>
      </div>

    <!-- 打印机 -->
      <div class="header-title" v-show="showFlag.printer" :key="'showPrinterTitle'">
        <div class="header-title-text">打印机</div>
        <div class="header-title-text-all" @click="navTm">全部</div>
      </div>
      <div class="prod-content" v-show="showFlag.printer" :key="'showPrinterContent'">
        <div class="prod-content-print1" @click="navGoods('tp2-s')">
          <img src="https://m.yoyin.net/h5/img/mobile/home/<USER>">
          <div class="prod-content-print1-title">TP2-S</div>
          <div class="prod-content-print1-desc">学习与生活都可靠的2寸便携打印机</div>
        </div>
        <div class="prod-content-print1" @click="navGoods('a4')">
          <img src="https://m.yoyin.net/h5/img/mobile/home/<USER>">
          <div class="prod-content-print1-title">A4打印机</div>
          <div class="prod-content-print1-desc">便携随行 轻松打印</div>
        </div>
        <br>
<!--        <div class="prod-content-print2">-->
<!--          <div class="prod-content-print2-left">-->
<!--            <div class="prod-content-print2-left-title">TP8</div>-->
<!--            <div class="prod-content-print2-left-desc">副文本介绍</div>-->
<!--          </div>-->
<!--          <img src="https://m.yoyin.net/h5/img/mobile/home/<USER>">-->
<!--        </div>-->
      </div>

    <!-- 配件与耗材 -->
      <div class="header-title" v-show="showFlag.consumable"  :key="'showConsumableTitle'">
        <div class="header-title-text">配件与耗材</div>
        <div class="header-title-text-all" @click="navCard">全部</div>
      </div>
      <div class="prod-content" v-show="showFlag.consumable" :key="'showConsumableContent'">
        <div class="prod-content-consumable" @click="navCard">
          <img src="https://m.yoyin.net/h5/img/mobile/home/<USER>">
          <div class="prod-content-consumable-title">2寸热敏打印机</div>
        </div>
        <div class="prod-content-consumable" @click="navCard">
          <img src="https://m.yoyin.net/h5/img/mobile/home/<USER>">
          <div class="prod-content-consumable-title">4寸热敏打印机</div>
        </div>
        <div class="prod-content-consumable" @click="navCard">
          <img src="https://m.yoyin.net/h5/img/mobile/home/<USER>">
          <div class="prod-content-consumable-title">A5热敏打印机</div>
        </div>
        <div class="prod-content-consumable" @click="navCard">
          <img src="https://m.yoyin.net/h5/img/mobile/home/<USER>">
          <div class="prod-content-consumable-title">A4热敏打印机</div>
        </div>
      </div>
    <!-- 商城 -->
      <div class="header-title" v-show="showFlag.shop" :key="'showFlagShopTitle'">
        <div class="header-title-text">商城</div>
      </div>
      <div class="prod-content" v-show="showFlag.shop" :key="'showFlagShopContent'">
        <div class="prod-content-shop" @click="navTm">
          <img src="https://m.yoyin.net/h5/img/mobile/home/<USER>">
          <div class="prod-content-shop-title">天猫商城</div>
        </div>
<!--        <div class="prod-content-shop">-->
<!--          <img src="https://m.yoyin.net/h5/img/mobile/home/<USER>">-->
<!--          <div class="prod-content-shop-title">抖音商城</div>-->
<!--        </div>-->
      </div>

    <!-- 商城 -->
    <div class="header-title" v-show="showFlag.download" :key="'showFlagDownloadTitle'">
      <div class="header-title-text">APP下载</div>
    </div>
    <div class="prod-content" v-show="showFlag.download" :key="'showFlagDownloadContent'">
      <div class="prod-content-download" @click="nav('yoyin')">
        <img src="https://m.yoyin.net/h5/img/mobile/home/<USER>">
        <div class="prod-content-download-right">
          <div class="prod-content-download-right-title">柚印APP</div>
          <div class="prod-content-download-right-desc">适用于：2寸和4寸打印机</div>
          <div class="prod-content-download-right-desc">适用型号：TP2-S/TP2-Y/TP4</div>
        </div>
      </div>

      <div class="prod-content-download" @click="nav('yoyin4a')">
        <img src="https://m.yoyin.net/h5/img/mobile/home/<USER>">
        <div class="prod-content-download-right">
          <div class="prod-content-download-right-title">A4打印机</div>
          <div class="prod-content-download-right-desc">适用于：A4打印机</div>
          <div class="prod-content-download-right-desc">适用型号：TP8/TP8-S</div>
        </div>
      </div>

      <div class="prod-content-download" @click="nav('xprint')">
        <img src="https://m.yoyin.net/h5/img/mobile/home/<USER>">
        <div class="prod-content-download-right">
          <div class="prod-content-download-right-title">芯意贴</div>
          <div class="prod-content-download-right-desc">适用于：标签机</div>
          <div class="prod-content-download-right-desc">适用型号：TP8/TP8-S</div>
        </div>
      </div>
    </div>

    <div v-show="showFlag.foot" :key="'showFlagFoot'">
      <foot-info></foot-info>
    </div>
    </transition-group>
  </div>
</template>

<script>
import Swiper from 'swiper'
import 'swiper/css/swiper.css'
import FootInfo from './FootInfo'

export default {
  name: 'HomePage',
  components: {FootInfo},
  data () {
    return {
      swImgs: ['https://lupic.cdn.bcebos.com/20210629/14912108_14.jpg', 'https://lupic.cdn.bcebos.com/20210629/4921758_14.jpg', 'https://lupic.cdn.bcebos.com/20210629/4921758_14.jpg', 'https://lupic.cdn.bcebos.com/20210629/4921758_14.jpg'],
      swiperOptions: {
        pagination: {
          el: '.swiper-pagination', // 分页标签
          clickable: true // 允许分页点击跳转
        },
        navigation: { // 左右按钮
          nextEl: '.swiper-button-next',
          prevEl: '.swiper-button-prev'
        },
        // 设定初始化时slide的索引
        initialSlide: 2,
        // 如果需要滚动条
        scrollbar: {
          el: '.swiper-scrollbar'
        },
        loop: true, // 无限循环,
        // autoplay: true,//可选选项，自动滑动
        autoplay: {
          delay: 5000,
          stopOnLastSlide: false,
          disableOnInteraction: true
        },
        effect: 'cube' // 切换特效,
      },
      showFlag: {
        banner: false,
        printer: false,
        consumable: false,
        shop: false,
        download: false,
        foot: false
      },
      banner1: 'https://m.yoyin.net/h5/img/mobile/home/<USER>',
      banner2: 'https://m.yoyin.net/h5/img/mobile/home/<USER>',
      banner3: 'https://m.yoyin.net/h5/img/mobile/home/<USER>'
    }
  },
  mounted () {
    const that = this
    that.showFlag.banner = true
    that.getBanner()
    setTimeout(function () {
      that.showFlag.printer = true
    }, 300)
    setTimeout(function () {
      that.showFlag.consumable = true
    }, 500)
    setTimeout(function () {
      that.showFlag.shop = true
    }, 700)
    setTimeout(function () {
      that.showFlag.download = true
    }, 900)
    setTimeout(function () {
      that.showFlag.foot = true
    }, 1100)
    // new Swiper('.swiper-container', {})
    // var mySwiper = new Swiper('.swiper-container', {
    //   loop: true, // 循环模式选项
    //   autoplay: true
    // })
  },
  //
  methods: {
    // 封装轮播函数
    getBanner () {
      // 调用延迟加载 $nextTick
      this.$nextTick(() => {
        let swiper = new Swiper('.swiper-container', {
          effect: 'flip',
          // 是否循环
          loop: true,
          autoplay: {
            // swiper手动滑动之后自动轮播失效的解决方法,包括触碰，拖动，点击pagination,重新启动自动播放
            disableOnInteraction: false,
            // 自动播放时间：毫秒
            delay: 5000
          },
          pagination: {
            // 小圆点
            el: '.swiper-pagination'
          }
        })
        console.log(swiper)
      })
    },
    nav (type) {
      if (type === 'yoyin') {
        window.location.href = 'https://m.yoyin.net/h5/download/download.html'
      } else if (type === 'yoyin4a') {
        window.location.href = 'https://m.yoyin.net/h5/download/download4a.html'
      } else if (type === 'xprint') {
        window.location.href = 'https://share.xplable.com/h5/download/download.html'
      }
    },
    navTm () {
      window.location.href = 'https://youyinbgyp.tmall.com/?spm=a1z10.3-b-s.1997427721.d4918089.2a43340c9muwdG'
    },
    navCard () {
      window.location.href = 'https://detail.tmall.com/item.htm?spm=a220m.1000862.1000725.1.610f76fajpkvSU&id=655465984825&areaId=442000&is_b=1&cat_id=2&rn=486a62bbebce3546085315586971f08a'
    },
    navGoods (type) {
      if (type === 'tp2-s') {
        window.location.href = 'https://detail.tmall.com/item.htm?spm=a220m.1000862.1000725.31.610f76fajpkvSU&id=659824507080&areaId=442000&is_b=1&cat_id=2&rn=486a62bbebce3546085315586971f08a'
      } else if (type === 'a4') {
        window.location.href = 'https://detail.tmall.com/item.htm?spm=a220m.1000862.1000725.26.610f76fajpkvSU&id=654812449194&areaId=442000&is_b=1&cat_id=2&rn=486a62bbebce3546085315586971f08a&sku_properties=1627207:28320\n'
      }
    },
    navBanner (index) {
      if (index === 1) {
        window.location.href = 'https://detail.tmall.com/item.htm?spm=a220m.1000862.1000725.1.610f76fajpkvSU&id=655465984825&areaId=442000&is_b=1&cat_id=2&rn=486a62bbebce3546085315586971f08a'
      } else if (index === 2) {
        window.location.href = 'https://detail.tmall.com/item.htm?spm=a220m.1000862.1000725.26.610f76fajpkvSU&id=654812449194&areaId=442000&is_b=1&cat_id=2&rn=486a62bbebce3546085315586971f08a&sku_properties=1627207:28320'
      } else if (index === 3) {
        window.location.href = 'https://detail.tmall.com/item.htm?spm=a220m.1000862.1000725.31.610f76fajpkvSU&id=659824507080&areaId=442000&is_b=1&cat_id=2&rn=486a62bbebce3546085315586971f08a'
      }
    }
  }
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
html, body {
  overflow-x: hidden;
  touch-action:none;
  touch-action:pan-y;
}
.swiper-container {
  width: 100%;
  height: 35.2rem;
  background-color: #F5F5F5;
  &-class {
    width:100%;
    opacity: 1;
    margin-bottom: 4.3rem;
    background-color: #F5F5F5;
  }
  z-index: 9999;
}
.swiper-slide {
  background-color: #F5F5F5;
  text-align: left;
  img {
    width: 27.8rem;
    height: 35.2rem;
    border-radius: 1rem;
    box-shadow: 0rem 2.3rem 3.3rem rgba(0, 0, 0, 0.25);
  }
}
/deep/ .swiper-pagination-bullet {

}
/deep/ .swiper-pagination-bullet{
  background-color: #ffffff!important;
}

.header-title {
  min-height: 4.3rem;
  background-image: url("https://m.yoyin.net/h5/img/mobile/home/<USER>");
  background-position: left center;
  background-repeat: no-repeat;
  background-size:4.3rem 100%;
  position: relative;
  margin-bottom: 1.2rem;
  width: 30rem;
  &-text {
    float: left;
    padding-left: 2.2rem;
    padding-top: 1.6rem;
    font-size: 1.7rem;
    &-all {
      float: right;
      right: 0;
      font-size: 1.5rem;
      color: #666666;
      background-image: url("https://m.yoyin.net/h5/img/mobile/home/<USER>");
      background-position: right center;
      background-repeat: no-repeat;
      background-size: 0.7rem;
      margin-top: 2rem;
      padding-right: 1.5rem;
      margin-right: 2rem;
    }
  }
}

.prod-content {
  display: flex;
  flex-wrap: wrap;
  width: 30rem;
  padding-bottom: 4.3rem;
  &-print1 {
    text-align: center;
    width: 13.4rem;
    height: 15.7rem;
    background: #FFFFFF;
    box-shadow: 0px 10px 13px rgba(0, 0, 0, 0.03);
    opacity: 1;
    border-radius: 10px;
    margin: 0rem 1.1rem 1.1rem 0;
    img {
      width: 12.5rem;
      height: 9rem;
    }
    &-title{
      width: 100%;
      font-size: 1.5rem;
      color: #222222;
    }
    &-desc {
      width: 100%;
      font-size: 1.2rem;
      color: #999999;
    }
  }
  &-print2 {
    width: 27.9rem;
    height: 12.0rem;
    background: #FFFFFF;
    box-shadow: 0px 10px 13px rgba(0, 0, 0, 0.03);
    opacity: 1;
    border-radius: 10px;
    position: relative;
    img {
      width: 12.1rem;
      height: 9.4rem;
      position: absolute;
      right: 1.1rem;
      top: 1.1rem;
    }
    &-left {
      padding: 3.8rem 0 0 0rem;
      width: 13.4rem;
      text-align: center;
      &-title{
        width: 100%;
        font-size: 1.5rem;
        color: #222222;
      }
      &-desc {
        width: 100%;
        font-size: 1.2rem;
        color: #999999;
      }
    }

  }
  &-consumable {
    width: 13.4rem;
    height: 16.7rem;
    background: #FFFFFF;
    box-shadow: 0px 10px 13px rgba(0, 0, 0, 0.03);
    opacity: 1;
    border-radius: 10px;
    margin: 0rem 1.1rem 1.1rem 0;
    text-align: center;
    img {
      width: 13.4rem;
      height: 12.1rem;
    }
    &-title {
      line-height: 4.7rem;
      font-size: 1.5rem;
      color: #222222;
    }
  }
  &-shop {
    display: flex;
    margin: 0 1.3rem 0 0;
    width: 13.4rem;
    height: 7.6rem;
    background: #FFFFFF;
    box-shadow: 0px 10px 13px rgba(0, 0, 0, 0.03);
    opacity: 1;
    border-radius: 10px;
    img {
      padding: 1.8rem 1.1rem 0 1.1rem;
      width: 4rem;
      height: 4rem;
    }
    &-title {
      padding-top: 1.8rem;
      line-height: 4rem;
      font-size: 1.5rem;
      color: #222222;
    }
  }
  &-download {
    position: relative;
    //display: ;
    width: 27.8rem;
    height: 8rem;
    background: #FFFFFF;
    box-shadow: 0px 10px 13px rgba(0, 0, 0, 0.03);
    opacity: 1;
    border-radius: 10px;
    margin: 0 1.1rem 1.1rem 0;
    img {
      // padding: 1.8rem 1.8rem 0 1.8rem;
      position: absolute;
      left: 1.8rem;
      top:1.8rem;
      width: 4.5rem;
      height: 4.5rem;
      box-shadow: 0px 10px 13px rgba(0, 0, 0, 0.03);
      border-radius: 10px;
    }
    &-right{
      padding-top: 1.2rem;
      padding-left: 8.1rem;
      text-align: left;
      &-title {
        width: 100%;
        font-size: 1.5rem;
        color: #222222;
      }
      &-desc {
        width: 100%;
        font-size: 1.1rem;
        color: #666666;
      }
    }
  }
}

.dis-enter{
  opacity:0;
}
.dis-enter-active{
  transition:opacity 1.5s;
}
.dis-leave-active{
  transition:transform 1s;
}
.dis-leave-to{
  transform: translateX(0);
}
</style>
