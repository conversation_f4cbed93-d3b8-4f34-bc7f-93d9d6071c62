<template>
  <div v-if="visiable" class="edit-dialog">
    <el-dialog
      ref="dialog"
      :show-close="false"
      :title="form.id === '' ? '新增banner' : '编辑banner' "
      :visible.sync="visiable"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      width="50%"
      center
    >
      <el-form ref="form" :model="form" :rules="rules" label-width="100px" class="edit-form">
        <el-form-item label="名称" prop="name">
          <el-input v-model="form.name" />
        </el-form-item>
        <el-form-item label="栏目类型" prop="column">
          <el-radio-group v-model="form.column">
            <el-radio-button label="home">首页柚印</el-radio-button>
            <el-radio-button label="home_a4">首页A4</el-radio-button>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="类型" prop="jumpType">
          <el-radio-group v-model="form.jumpType">
            <el-radio-button label="100">无跳转</el-radio-button>
            <el-radio-button label="101">H5</el-radio-button>
            <el-radio-button label="105">原生</el-radio-button>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="开始展示的版本号" prop="version">
          <el-input
            v-model="form.version"
            type="text"
            maxlength="200"
            placeholder="请输入格式x.x.x"
          />
          {主版本号} + "." + {版本小号} + "." + {修复版本号} 例如:  3.3.1
        </el-form-item>
        <el-form-item v-if="form.jumpType === 101 || form.jumpType === '101' " label="链接" prop="jumpVal">
          <el-input v-model="form.jumpVal" />
        </el-form-item>

        <el-form-item v-if="form.column === 'knowledge'" label="对应干货id" prop="jumpVal">
          <el-input v-model="form.jumpVal" />
          <el-button @click="knowledgeShow=true">选择</el-button>
        </el-form-item>

        <el-form-item v-if="form.column === 'knowledge'" label="年级" prop="remark">
          <!-- <el-radio-group v-model="form.remark">
            <el-radio-button label="xx">小学</el-radio-button>
            <el-radio-button label="cz">初中</el-radio-button>
            <el-radio-button label="gz">高中</el-radio-button>
            <el-radio-button label="">无</el-radio-button>
          </el-radio-group> -->
          <el-checkbox-group v-model="checkboxGroup1">
            <el-checkbox-button v-for="item in gradeLevelList" :key="item.key" :label="item.key">{{ item.label }}</el-checkbox-button>
          </el-checkbox-group>
        </el-form-item>

        <el-form-item prop="sort">
          <el-tooltip slot="label" class="item " effect="dark" content="用于排序" placement="top">
            <span>
              序号<i class="el-icon-warning-outline" />
            </span>
          </el-tooltip>
          <el-input v-model="form.sort" />
        </el-form-item>
        <el-form-item label="起始时间" prop="startTime">
          <el-date-picker
            v-model="form.startTime"
            :value-format="timeFormat"
            type="datetime"
            placeholder="选择日期时间"
          />
        </el-form-item>

        <el-form-item label="结束时间" prop="endTime">
          <el-date-picker
            v-model="form.endTime"
            type="datetime"
            :value-format="timeFormat"
            placeholder="选择日期时间"
          />
        </el-form-item>
        <el-form-item label="分享展示标题" prop="shareTitle">
          <el-input v-model="form.shareTitle" />
        </el-form-item>
        <el-form-item label="分享展示内容" prop="shareContent">
          <el-input
            v-model="form.shareContent"
            type="textarea"
            :rows="3"
            maxlength="200"
            placeholder="请输入回复内容"
          />
        </el-form-item>
        <el-form-item label="是否展示分享按钮" prop="shareFlag">
          <el-radio-group v-model="form.shareFlag">
            <el-radio-button label="0">不展示</el-radio-button>
            <el-radio-button label="1">展示</el-radio-button>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if="form.jumpType === 105 || form.jumpType === '105' " label="andriod调用参数" prop="paramAndroid">
          <el-input v-model="form.paramAndroid" />
        </el-form-item>
        <el-form-item v-if="form.jumpType === 105 || form.jumpType === '105' " label="ios调用参数" prop="paramIos">
          <el-input v-model="form.paramIos" />
        </el-form-item>

        <el-form-item label="图片" prop="pic">
          <el-upload
            class="avatar-uploader"
            action=""
            :show-file-list="false"
            :on-change="changePic"
            :before-upload="beforeUpload"
            accept="image/png, image/jpeg"
          >
            <img v-if="form.pic" :src="form.pic" class="avatar">
            <i v-else class="el-icon-plus avatar-uploader-icon" />
          </el-upload>
          <img v-show="false" :src="testUrl" class="avatar">
        </el-form-item>

        <el-form-item>
          <el-button @click="handleClose">返回</el-button>
          <el-button v-permission="'btn-menuSystemsetting-banner-edit'" type="primary" @click="submit">保存</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
    <el-dialog
      :show-close="true"
      :title="'干货列表'"
      :visible.sync="knowledgeShow"
      width="50%"
      center
    >
      <knowledge-list @selectKnowLedge="selectKnowLedge" />
    </el-dialog>
  </div>
</template>

<script>
import { saveRecord } from '@/api/community/banner'
import AliyunOSS from '@/utils/aliyunOSS'
import KnowledgeList from '@/views/community/banner/component/KnowledgeList/index'
const oss = new AliyunOSS()

export default {
  name: 'EditDialog',
  components: { KnowledgeList },
  props: {
    visiable: {
      type: Boolean,
      required: true,
      default: false
    },
    formData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      knowledgeShow: false,
      /** 表单 */
      form: {},
      /** 校验规则 */
      rules: {
        name: [
          { required: true, message: '请输入标题', trigger: 'blur' },
          { min: 3, max: 20, message: '长度在 2 到 30 个字符', trigger: 'blur' }
        ],
        column: [
          { required: true, message: '请选择栏目类型', trigger: 'change' }
        ],
        jumpType: [
          { required: true, message: '请选择类型', trigger: 'change' }
        ],
        jumpVal: [
          { required: true, message: '请输入链接', trigger: 'blur' }
        ],
        sort: [
          { required: true, message: '请输入序号，序号用于排序', trigger: 'blur' }
        ],

        startTime: [
          { required: true, message: '请选择时间', trigger: 'change' }
        ],
        endTime: [
          { required: true, message: '请选择时间', trigger: 'change' }
        ]
      },
      /** 待上传的文件 */
      upFile: undefined,
      testUrl: '',
      /** 时间格式 */
      timeFormat: 'yyyy-MM-dd HH:mm:ss',
      gradeLevelList: [
        {
          label: '小学',
          key: 'xx'
        },
        {
          label: '初中',
          key: 'cz'
        },
        {
          label: '高中',
          key: 'gz'
        }
      ],
      checkboxGroup1: []

    }
  },
  computed: {
  },
  watch: {
    formData: function(val) {
      this.upFile = undefined
      this.form = { ...val }
      if (this.form.remark && this.form.column === 'knowledge') {
        this.checkboxGroup1 = this.form.remark.split(',')
      } else {
        this.checkboxGroup1 = []
      }
    }
  },
  mounted() {
    this.show = this.visiable
  },
  methods: {
    selectKnowLedge(id) {
      this.form.jumpVal = id
      this.knowledgeShow = false
    },
    /** 返回 */
    handleClose(code) {
      this.$emit('closeDialog', code)
    },
    /** 选择图片*/
    changePic(file) {
      this.upFile = file.raw
      this.form.pic = URL.createObjectURL(file.raw)
      this.testUrl = URL.createObjectURL(file.raw)
      return false
    },
    /** 上传前 */
    beforeUpload(file) {
      return false
    },
    /** 提交 */
    submit() {
      if (this.checkboxGroup1) {
        this.form.remark = this.checkboxGroup1.join(',')
      } else {
        this.form.remark = ''
      }
      if (this.form.column !== 'knowledge' && !this.form.pic) {
        this.$message.error('请上传图片')
        return
      }
      this.$refs['form'].validate(valid => {
        if (valid) {
          if (this.upFile) {
            oss.uploadFile(this.upFile, (result, error) => {
              if (error) {
                this.$message.error('上传文件失败')
                return -1
              }
              if (result) {
                this.form.pic = result.url
                this.addUpdateRecord()
              }
            })
          } else {
            // 直接保存
            this.addUpdateRecord()
          }
        } else {
          console.log('submit error')
          return -1
        }
      })
    },
    async addUpdateRecord() {
      // 新增
      if (!this.form.id) {
        this.form.id = ''
      }
      // 更新
      const res = await saveRecord(this.form)
      if (res.head.ret === 0) {
        this.$message.success('保存成功')
        this.handleClose('1')
      }
    }
  }

}
</script>

<style lang='scss' scoped>
  .avatar-uploader .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }
  .avatar-uploader .el-upload:hover {
    border-color: #409EFF;
  }
  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 178px;
    height: 178px;
    line-height: 178px;
    text-align: center;
  }
  .avatar {
    width: 178px;
    height: 178px;
    display: block;
  }
</style>
