import request from '@/utils/request'

// 获取办公素材列表
export function fetchOfficeMaterialList(params) {
  return request({
    url: '/platform/gam/community/v1/official-material/list',
    method: 'get',
    params
  })
}

// 新增或更新办公素材
export function saveOfficeMaterial(data) {
  return request({
    url: '/platform/gam/community/v1/official-material/save',
    method: 'post',
    data
  })
}

// 删除办公素材
export function deleteOfficeMaterial(id) {
  return request({
    url: '/platform/gam/community/v1/official-material/delete',
    method: 'post',
    data: { id }
  })
}

// 获取办公素材分类列表
export function fetchOfficeMaterialCategoryList() {
  return request({
    url: '/platform/gam/community/v1/official-material/category-list',
    method: 'get'
  })
}

// 获取办公素材详情
export function fetchOfficeMaterialDetail(params) {
  return request({
    url: '/platform/gam/community/v1/official-material/detail',
    method: 'get',
    params
  })
}

// 查询办公素材分页列表
export function fetchOfficeMaterialPageList(params) {
  return request({
    url: '/platform/gam/community/v1/official-material/pagelist',
    method: 'get',
    params
  })
} 