import request from '@/utils/request'

// 翻页查询
const LIST_URL = '/platform/gam/community/v1/coin/getgoodspage'
const DELETE_RECORD_URL = '/platform/gam/community/v1/coin/deleteGoods'
const SAVE_URL = '/platform/gam/community/v1/coin/savegoods'
const GET_COIN_GOODS_TYPE = '/platform/gam/community/v1/coin/getgoodstypetab'
// 删除记录

/**
 * 查询列表数据
 * @param params 参数
 */
export const fetchGoodsListByPage = async params => {
  return request({
    url: LIST_URL,
    method: 'get',
    params: params
  })
}

export const getGoodsTypeTab = async params => {
  return request({
    url: GET_COIN_GOODS_TYPE,
    method: 'get',
    params: params
  })
}

/**
 * 删除记录
 * @param {String} id 记录id
 */
export const deleteGoodsRecord = async params => {
  return request({
    url: DELETE_RECORD_URL,
    method: 'get',
    params
  })
}

/** 保存记录 */
export const saveGoodsRecord = async params => {
  return request({
    url: SAVE_URL,
    method: 'post',
    data: params
  })
}
