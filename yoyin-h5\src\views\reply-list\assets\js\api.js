import axios from '@/plugin/axios'

/**
 * 查询评论详情
 * @param {Sting} commid 评论id
 */
export const getCommentDetail = async commid => {
  const params = { commid }
  const { data } = await axios.get(
    '/api/gam/community/v1/comment/getcomminfo',
    { params }
  )
  return data
}

/**
 * 获取回复列表
 * @param {String} commid  评论id
 * @param {String} pageno  页码
 * @param {String} pagesize 每页条数
 * @param {String} lastid  最后记录id
 */
export const getReplyList = async (commid, pageno, pagesize, lastid) => {
  const params = { commid, pageno, pagesize, lastid }
  const { data } = await axios.get(
    '/api/gam/community/v1/comment/getreplylist',
    { params }
  )
  return data
}

/**
 * 删除回复
 * @param {String} id 评论id
 */
export const deleteReply = async id => {
  const params = {
    id
  }
  const { data } = await axios.get('/api/gam/community/v1/comment/delreply', {
    params
  })
  return data
}
