<template>
  <div class="topbar">
    <div class="title">{{titleName}}</div>
    <img src="https://m.yoyin.net/h5/img/mobile/<EMAIL>" >
  </div>
</template>

<script>
export default {
  props: ['titleName'],
  name: 'TopBar'
}
</script>

<style scoped>
  .topbar {
    position: relative;
    padding-bottom: 2.8rem;
  }
  .topbar img {
    height: 0.4rem;
  }
  .title {
    font-size: 2.2rem;
    color: #222222;
    font-weight: 600;
    line-height: 3.33rem;
  }
  .line {
    display: flex;
  }
  .line .line-grey {
    min-height: 1px;
    width: 40rem;
    display: inline;
  }

  .line .line-greed {
    border-bottom: 0.4rem solid #63C184;
    width: 4.4rem;
  }
</style>
