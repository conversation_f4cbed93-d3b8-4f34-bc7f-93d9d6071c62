<template>
  <div class="content">
    <div class="title">{{model.title}}</div>
    <div class="title-image"><img src="./assets/img/<EMAIL>"></div>
    <div style="text-align: center;margin:15px">
      <div v-for="item in pics" :key="item.pic">
        <img v-if="item.canRead===1" :src="item.pic"/>
      </div>
    </div>
    <div class="foot" >
      <!-- <div class="foot-right-top"><font class="foot-right-top-font">{{model.clickNum}}人已获得干货</font></div> -->
      <!-- <div class="foot-center-one">试读结束</div>
      <div class="foot-center-two">下载打印机APP 获取干货入手不亏</div> -->
      <div class="foot-bottom">
        <div class="foot-bottom-img">
          <img src="./assets/img/<EMAIL>" />
        </div>
        <div class="foot-bottom-center">
          <div class="foot-bottom-center-one">XPlife</div>
          <div class="foot-bottom-center-two">错题打印机</div>
        </div>
        <div class="foot-bottom-right" @click="navDownloadApp">立即打印</div>
      </div>
    </div>
  </div>
</template>

<script>
import { appJS, getUrlParameter } from '@/utils/common'
import { getDetail } from './assets/js/api'
export default {
  data () {
    return {
      pics: '',
      model: {},
      isBottom: false
    }
  },
  created() {
    const query = getUrlParameter()
    this.getDetailById(query.id)
  },
  methods: {
    async getDetailById(id) {
      const { data, head } = await getDetail(id)
       if (head.ret === 0) {
         this.model = data
         this.pics = data.pics
         window.onscroll = this.throttle(this.onScroll)
       }
    },
    onScroll() {
        // 变量scrollTop是滚动条滚动时,距离顶部的距离
        let scrollTop = document.documentElement.scrollTop || document.body.scrollTop

        // 变量windowHeight是可视区的高度
        let windowHeight = document.documentElement.clientHeight || document.body.clientHeight
        // 变量scrollHeight是滚动条的总高度
        let scrollHeight = document.documentElement.scrollHeight || document.body.scrollHeight

        // 滚动条到底部的条件
        if (Math.abs(scrollTop + windowHeight - scrollHeight) < 50) {
            // 写后台加载数据的函数
            console.log('到底部了')
            this.isBottom = true
        } else {
          this.isBottom = false
        }
    },
    // 函数节流
    throttle(fn, interval = 300) {
        let canRun = true
        return function() {
            if (!canRun) {
              return
            }
            canRun = false
            setTimeout(() => {
                fn.apply(this, arguments)
                canRun = true
            }, interval)
        }
    },
    navDownloadApp() {
      window.location.href = 'https://a.app.qq.com/o/simple.jsp?pkgname=com.yoyinprinter.app'
    }
  }
}
</script>

<style lang="scss">
.title {
  margin: 21.5px;
  font-size: 20px;
  color: #000;
  font-weight: bold;
  &-image {
    text-align: center;
    padding: 33px;
    img {
      width: 70px;
      height: 70px;
    }
  }
}
.foot-center {
  text-align: center;
  &-one {
    width: 100%;
    top: 34px;
    position: absolute;
    text-align: center;
    font-size: 0.875rem;
    color: #ffffff;
  }
  &-two {
    width: 100%;
    top: 60px;
    position: absolute;
    text-align: center;
    font-size: 0.625rem;
    color: #ffffff;
  }
}
.foot-bottom {
  width: 100%;
  margin: 0 1rem;
  position: absolute;
  bottom: 0.9375rem;
  display: flex;
  &-img {
    line-height: 2.375rem;
    img {
      width: 2.375rem;
      height: 2.375rem;
    }
  }
  &-center {
    &-one {
      padding-left: 15px;
      width: 12rem;
      font-size: 0.875rem;
      color: #ffffff;
    }
    &-two {
      padding-left: 15px;
      width: 12rem;
      font-size: 0.625rem;
      color: #999999;
    }
  }
  &-right {
    width: 6.125rem;
    height: 2.3125rem;
    // background: #63C184;
    background: #FFFFFF;
    opacity: 1;
    border-radius: 100px;
    color: #222222;
    text-align: center;
    line-height: 2.3125rem;
    font-size: 0.5rem;
    position: absolute;
    right: 2rem;
    top: 0.125rem;
  }
}
img {
  object-fit: contain;
  width: 100%;
  text-align: center;
}
.foot-right-top {
  width: 104.5px;
  height: 24px;
  background: #FF8C00;
  opacity: 1;
  border-radius: 5px 20px 5px 20px;
  float: right;
  text-align: center;
  &-font {
    line-height: 24px;
    color: #ffffff;
  }
}
.foot {
  position: fixed;
    bottom: 0;
    width: 100%;
    height: 4.3125rem;
    background: #000000;
    opacity: 0.8;
}
</style>
