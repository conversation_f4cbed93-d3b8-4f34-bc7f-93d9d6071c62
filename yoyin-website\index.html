<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width,initial-scale=1.0">
    <meta name="Keywords" content="柚印,柚子,打印机,热敏打印机,柚子打印机,错题,芯意,贴，耗材" />
    <meta name="Description" content="柚印官网, A4打印机, 芯意贴,打印机" />
    <link rel="shortcut icon" href="logo.ico" />
    <style>
      html{
        font-size: 62.5%
      }
      body {
        margin: 0;
        /* background-color: #F5F5F5; */
        background-color: #ffffff;
      }
      html, body {
        overflow-x: hidden;
        touch-action:none;
        touch-action:pan-y;
      }
    </style>
    <!-- <title>XPLife打印机</title> -->
    <title>柚印官方网站</title>
  </head>
  <body>
    <div id="app"></div>
    <!-- built files will be auto injected -->
    <script language="JavaScript">
      (function(){
        var currClientWidth,currClientHeight,fontValue,originWidth, originHeight;
        originWidth = 375;//ui设计稿的宽度，一般750或640
        originHeight = 750;
        __resize();
        window.addEventListener('resize', __resize, false);
        function __resize() {
          currClientWidth = document.documentElement.offsetWidth;
          currClientHeight = document.documentElement.clientHeight;
          // alert(currClientWidth)
          // alert(currClientHeight)
          // if (currClientHeight>currClientWidth){
          //   currClientWidth = 750
          // }
          if (currClientWidth>750){
            currClientWidth = 750
          }

          // 如果高比宽低超过一个比例，则按高的做比例
          // if () {
          //
          // } else {
          //
          // }
          // originWidth = 750
          fontValue = (62.5 * (currClientWidth / originWidth)).toFixed(2);
          document.documentElement.style.fontSize = fontValue + '%';
        }
      })()
    </script>
  </body>
</html>
