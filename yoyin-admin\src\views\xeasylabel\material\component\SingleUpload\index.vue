<template>
  <div class="singleupload">
    <el-upload
      class="avatar-uploader"
      accept="image/png, image/jpeg"
      action="https://jsonplaceholder.typicode.com/posts/"
      :show-file-list="false"
      :auto-upload="false"
      :on-change="onChnagePic"
    >
      <HoverMask v-if="getUrl">
        <img :src="getUrl" class="avatar">
        <template v-slot:action>
          <i class="el-icon-view" @click.stop="handleClick" />
        </template>
      </HoverMask>
      <i v-else class="el-icon-plus avatar-uploader-icon" />
    </el-upload>
    <el-dialog :visible.sync="dialogVisible" append-to-body>
      <img width="100%" :src="getUrl" alt="">
    </el-dialog>
  </div>
</template>
<script>
import HoverMask from '@/components/HoverMask/index'
import { isImage } from '@/utils/index'
export default {
  name: 'SingleUpload',
  components: {
    HoverMask
  },
  props: {
    keyWord: {
      type: String,
      default: ''
    },
    initUrl: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      imageUrl: '',
      dialogVisible: false,
      currentFile: undefined

    }
  },
  computed: {
    getUrl() {
      const url = this.imageUrl || this.initUrl
      return url
    }
  },
  watch: {
  },
  mounted() {
  },
  methods: {
    handleClick() {
      this.dialogVisible = true
    },
    onChnagePic(file, fileList) {
      if (!isImage(file.raw)) {
        this.$message.error('仅支持图片上传')
        return
      }
      this.currentFile = file.raw
      this.imageUrl = URL.createObjectURL(file.raw)
      // 通知父 文件对象
      this.$emit('updateFile', this.keyWord, this.currentFile)
    },
    clearFile() {
      this.imageUrl = ''
    }
  }
}
</script>
