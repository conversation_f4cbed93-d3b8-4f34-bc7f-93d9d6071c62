import axios from '@/plugin/axios'

import OSS from 'ali-oss'

export default class AliyunOSS {
  constructor() {
    this.client = undefined
    this.queue = []
  }

  /* eslint-disable class-methods-use-this, no-multi-assign, no-bitwise */
  uuid() {
    const s = []
    const hexDigits = '0123456789abcdef'
    for (let i = 0; i < 36; i++) {
      s[i] = hexDigits.substr(Math.floor(Math.random() * 0x10), 1)
    }
    s[14] = '4'
    s[19] = hexDigits.substr((s[19] & 0x3) | 0x8, 1)
    s[8] = s[13] = s[18] = s[23] = '-'

    const uuid = s.join('')
    return uuid
  }

  async initClient() {
    if (this.isLoading) {
      return
    }
    this.isLoading = true

    const {
      data: { head, data }
    } = await axios.get('/api/log/event/v1/storage/getosstoken')
    if (head.ret === 0) {
      const options = {
        accessKeyId: data.accessKeyId,
        accessKeySecret: data.accessKeySecret,
        bucket: data.paramMap.OSS.bucket,
        endpoint: data.paramMap.OSS.endPoint,
        stsToken: data.securityToken
      }
      this.objectName = data.paramMap.OSS.objectName
      this.client = new OSS(options)
      this.emptyQueue()
    }
    this.isLoading = false
  }

  emptyQueue() {
    for (let i = 0; i < this.queue.length; i++) {
      const { file, suffix, callback = null } = this.queue[i]
      const objectName = this.objectName + this.uuid() + suffix
      this.client
        .put(objectName, file)
        .then(result => {
          if (
            Object.prototype.toString.call(callback) === '[object Function]'
          ) {
            callback(result, null)
          }
        })
        .catch(e => {
          if (
            Object.prototype.toString.call(callback) === '[object Function]'
          ) {
            callback(null, e)
          }
        })
    }
    this.queue = []
  }

  uploadFile(file, suffix = '.jpeg', callback) {
    this.queue.push({ file, suffix, callback })
    if (!this.client) {
      this.initClient()
    } else {
      this.emptyQueue()
    }
  }
  async uploadFileSync(file, suffix = '.jpeg') {
    // console.log('initClient')
    await this.initClient()
    const objectName = this.objectName + this.uuid() + suffix
    let that = this
    // console.log(that.client)
    // console.log(that.client.put)
    setTimeout(() => {
      that.client.put(objectName, file)
    }, 50)
    return objectName
    // try {
    //   console.log('put :')
    //   console.log('objectName: ', objectName)
    //   let result = await this.client.put(objectName, file)
    //   console.log(result)
    //   return objectName
    //  } catch (err) {
    //    console.log('err')
    //    console.log(err)
    //    return objectName
    //  }
  }
}
