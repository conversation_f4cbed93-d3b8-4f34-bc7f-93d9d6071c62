<template>
  <div class="content">
    <div class="content-title">{{$t(`doc-import-ios.title`)}}</div>

    <div class="content-step-pic"><img src="./assets/img/step01.png" /></div>
    <div class="content-step-pic"><img src="./assets/img/step02.png" /></div>

  </div>
</template>
<script>
export default {
  data () {
    return {
      moduleKey: 'doc-operation-instructions'
    }
  },
  async mounted () {

  }
}
</script>

<style lang="scss">

.content {
  display: flex;
  flex-direction: column;
  padding: 20px 15px;
  &-title{
    font-size: 20px;
    font-weight: bold;
    margin-bottom: 15px;
  }
  &-step{
    display: flex;
    flex-direction: column;
    &-text{
      display: flex;
      align-items: center;
      &-logo{
        height: 45px;
        width: 45px;
        margin-right: 8px;
        img{
          height: 45px;
          width: 45px;
        }
      }
      &-tlogo{
        img{
          height: 27px;
          width: 24px;
        }        
      }
    }
    &-pic{
      align-self: center;
      img {
        width: 100%;
        height: auto;
      }      
    }
    &-pics{
      display: flex;
      width: 90vw;
      div{
        width: 50%;
      }
    }
  }
  &-divider{
    width:96%;
    height:0px;
    border:1px dashed #d8d4d4c9;
    align-self: center;
    margin: 28px auto 35px auto;
  }

}
</style>

