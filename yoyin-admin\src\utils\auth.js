// import Cookies from 'js-cookie'

// const TokenKey = 'vue_admin_template_token'

// export function getToken() {
//   const res = Cookies.get(TokenKey)
//   console.log('getToken', res)
//   return res
// }

// export function setToken(token) {
//   const res = Cookies.set(TokenKey, token)
//   console.log('setToken', res)
//   return res
// }

// export function removeToken() {
//   return Cookies.remove(TokenKey)
// }

const TokenKey = 'star_admin_token'
const AuthKey = 'star_admin_auth'

export function getToken() {
  return localStorage.getItem(TokenKey)
}

export function setToken(token) {
  return localStorage.setItem(TokenKey, token)
}

export function removeToken() {
  return localStorage.removeItem(TokenKey)
}

export function setAuthList(resList) {
  return localStorage.setItem(AuthKey, resList.join(','))
}

export function getAuthList() {
  const authStr = localStorage.getItem(AuthKey)
  if (authStr) {
    return authStr.split(',')
  }
  return []
}
